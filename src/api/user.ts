import { get, post, encrypt } from '~/utils/http'
export const addUserByEmailAsk = (params) => { // 注册 通过邮箱获取验证码
  return post('/api/v1/ua/registerUserByEmailAsk', params)
}
export const addUserByEmail = (params) => { // 注册 通过邮箱注册用户
  return post('/api/v1/ua/registerUserByEmail', params)
}
export const resetLoginByEmailAsk = (params) => { // 重置 通过邮箱重置登录发送验证码
  return post('/api/v1/ua/askForResetLoginByEmail', params)
}
export const resetLoginConfirm = (params) => { // 重置 修改登录密码确认
  return post('/api/v1/ua/resetUserLoginConfirm', params)
}
export const resetLoginByEmail = (params) => { // 重置 通过邮箱重置登录发送验证码
  return post('/api/v1/ua/resetLoginPwdByEmail', params)
}
export const userLogin = (params) => { // 登录
  return post('/api/v1/ua/userLogin', params)
}
export const thirdLogin = (params) => { // 第三方登录
  return post('/api/v1/ua/thirdLogin', params)
}
export const getUserInfo = (params) => { // 用户公共 获取用户信息接口
  return post('/api/v1/ua/getUserInfo', params)
}
export const qrContent = (params) => { // 登录 获取二维码登录
  return post('/api/v1/ua/qrCodeContent', params)
}
export const loginByQrId = (params) => {
  return post('/api/v1/ua/userLoginByQrCodeId', params)
}
export const loginConfirm = (params) => { // 登录 二次确认登录
  return post('/api/v1/ua/userLoginConfirm', params)
}
export const loginOut = (params) => { // 用户公共 退出登录
  return post('/api/v1/ua/userLogout', params)
}
export const UasetLoginNotVerifyTotp = (params) => { // 设置是否验证谷歌
  return post('/api/v1/ua/setLoginNotVerifyTotp', params)
}
export const setUserVisible = (params) => { // 用户公共 设置用户信息可见
  return post('/api/v1/ua/updateVisible', params)
}
export const getWebMange = (params) => { // 获取设备管理列表
  return post('/api/v1/ua/getWebMange', params)
}
export const delDevice = (params) => { // 踢出登录
  return post('/api/v1/ua/delDevice', params)
}
export const setAuthNotVerify = (params) => { // 登陆免验证
  return post('/api/v1/ua/setAuthNotVerify', params)
}
export const bindTotpAsk = (params) => { // 安全设置 获取绑定谷歌信息内容
  return post('/api/v1/ua/askForBindTotpAsk', params)
}
export const bindTotpConfirm1 = (params) => { // 安全设置 确认绑定/修改谷歌验证码
  return post('/api/v1/ua/confirmOneForBindTotp', params)
}
export const setEmailVerify = (params) => { // 安全设置 设置/修改防钓鱼码
  return post('/api/v1/ua/setUserEmailVerify', params)
}
export const changePwd = (params) => { // 安全设置 修改登录密码
  return post('/api/v1/ua/updatePwdByUserId', params)
}
export const getSonList = (params) => { // 子账户 查询子账户
  return post('/api/v1/ua/getSonList', params)
}
export const addForSon = (params) => { // 子账户 子账户新增
  return post('/api/v1/ua/addForSon', params)
}
export const updateSonDesc = (params) => { // 子账户 修改备注
  return post('/api/v1/ua/updateSonDesc', params)
}
export const sonActive = (parmas) => { // 子账户 激活子账户
  return post('/api/v1/ua/activeSon', parmas)
}
export const freeze = (params) => { // 子账户 子账户冻结
  return post('/api/v1/ua/freezeSon', params)
}
export const unfreeze = (params) => { // 子账户 子账户解除
  return post('/api/v1/ua/unfreezeSon', params)
}
export const updateIsSwitch = (parmas) => { // 子账户 开启/关闭切换功能
  return post('/api/v1/ua/updateIsSwitch', parmas)
}
export const switchAccount = (params) => { // 切换用户
  return post('/api/v1/ua/switchAccount', params)
}
export const getSwitchAccountList= (params) => { // 获取切换用户list
  return post('/api/v1/ua/switchAccount/list', params)
}
export const querySonApiKey = (params) => { // 子账户 获取API信息列表
  return post('/api/v1/ua/querySonApikey', params)
}
export const queryAllSonApiKey = (params) => { // 子账户 获取所有API信息列表
  return post('/api/v1/ua/queryAllSonApikey', params)
}
export const addSonApiKey = (params) => { // 子账户 新增api信息
  return post('/api/v1/ua/addSonApikey', params)
}
export const deleteSonApiKey = (params) => { // 子账户 删除api信息
  return post('/api/v1/ua/deleteSonApikey', params)
}
export const updateSonApiKey = (params) => { // 子账户 api信息更新
  return post('/api/v1/ua/updateSonApikey', params)
}
export const sonChangeLoginPwd = (params) => { // 子账户 修改子账户登录密码
  return post('/api/v1/ua/changeSonLoginPwd', params)
}
export const setSonDetailInfo = (params) => { // 设置子账户详细信息
  return post('/api/v1/ua/setSonDetailInfo', params)
}
export const getSonGoolgKey = (params) => { // 子账户 设置子账户谷歌
  return post('/api/v1/ua/getSonTotp', params)
}
export const activeSonGoolgKey = (params) => { // 子账户 激活子账户谷歌
  return post('/api/v1/ua/activeSonTotp', params)
}
export const saveRealName = (params) => { // 安全项验证 申请身份认证
  return post('/api/v1/ua/saveRealName', params)
}
export const emailConfirmAsk = (params) => { // 邮箱验证码确认
  return post('/api/v1/ua/askForEmailConfirm', params)
}
export const getUserRateApi = (params) => {
  return post('/api/v1/ua/getUserRate', params)
}
export const saveUserLeverageApi = (params) => { // 仓位模式设置
  return post('/api/v1/ua/saveUserLeverage', params)
}
export const getUserLeverageApi = (params) => { // 当前仓位模式获取
  return post('/api/v1/ua/getUserLeverage', params)
}
export const getInvClickApi = (params) => { // 统计邀请的次数
  return post('/api/v1/ua/invClick', params)
}
export const queryApiKey = (params) => { // API 获取api信息
  return post('/api/v1/ua/queryApikey', params)
}
export const deleteApiKey = (params) => { // API 删除api信息
  return post('/api/v1/ua/deleteApikey', params)
}
export const updateApiKey = (params) => { // API 更新保存api信息
  return post('/api/v1/ua/updateApikey', params)
}
export const addApiKey = (params) => { // API 新增api信息
  return post('/api/v1/ua/addApikey', params)
}
export const qrCodeUploadApI  = (params) => { // 上传二维码信息
  return post('/api/v1/ua/qrCodeUpload', params)
}
export const editProfileAPI = (params) => { // 编辑时区
  return post('/api/v1/ua/editProfile', params)
}

