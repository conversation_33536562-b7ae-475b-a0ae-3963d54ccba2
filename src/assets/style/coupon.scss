.coupon-container{
  .coupon-wrapper{
    .coupon-banner{
      width:100%;
      height:263px;
      // background:rgba(240, 185, 11, 0.2);
      .banner-center{
        max-width:1920px;
        margin:0 auto;
        height:263px;
        overflow:hidden;
        .banner-left{
          padding-left:20px;
          h1{
            font-size:48px;
            padding-bottom:12px;
            @include color(tc-primary);
          }
          h2{
            display:none;
          }
          p{
            font-size:30px;
            @include color(tc-primary);
            span{
              @include color(theme);
            }
          }
        }
        .banner-right{
          width:250px;
          margin-right:10%;
        }
      }
    }
    .coupon-list-container{
      padding:0 20px;
      max-width:1920px;
      margin:0 auto;
      .coupon-list-title{
        padding:20px 0;
        h2{
          font-size:28px;
          @include color(tc-primary);
        }
        .coupon-nav-list{
          li{
            cursor:pointer;
            font-size:24px;
            margin-right:24px;
            position: relative;
            @include color(tc-secondary);
            &.active{
              &:after{
                content:'';
                display: block;
                position: absolute;
                width:16px;
                height:2px;
                bottom:-12px;
                left:50%;
                margin-left:-8px;
                @include bg-color(theme);
              }
              @include color(tc-primary);
            }
          }
        }
        .coupon-title-right{
          width: 200px;
        }
      }
      .coupon-list-cont{
        padding-bottom:20px;
        min-height:500px;
        .coupon-list-wrap{
          .coupon-item-wrap{
            overflow:hidden;
            flex: 1;
            height: auto;
            margin: 0 20px 20px 0;
            max-width: calc(33.333333% - 14px);
            min-width: calc(33.333333% - 14px);
            position: relative;
            width: calc(33.333333% - 14px);
            box-shadow: 0px 8px 12px 0px rgba(0, 0, 0, 0.04);
            &.disabled{
              filter: grayscale(100%);
            }
            &:nth-child(3n){
              margin-right: 0;
            }
            .coupon-item-cont{
              border:1px solid;
              border-radius:12px;
              position: relative;
              @include border-color(border);
              @include bg-color(bg-primary);
              &:before{
                content:'';
                display:block;
                width:26px;
                height:26px;
                border-radius:50%;
                border:1px solid;
                position: absolute;
                left:120px;
                top:-13px;
                z-index:9;
                @include bg-color(bg-primary);
                @include border-color(border);
              }
              &:after{
                content:'';
                display:block;
                width:26px;
                height:26px;
                border-radius:50%;
                border:1px solid;
                position: absolute;
                left:120px;
                bottom:-13px;
                z-index:9;
                @include bg-color(bg-primary);
                @include border-color(border);
                box-shadow: inset 0px 18px 18px 0px rgba(0, 0, 0, 0.04);
              }
              .tag{
                position: absolute;
                top:-10px;
                right:-5px;
                width:100px;
                height:100px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size:14px;
                @include color(tc-secondary);
                transform: rotate(-40deg);
                @include get-img('~/assets/images/coupon/tag-audit-light.png', '~/assets/images/coupon/tag-audit-dark.png');
                background-size:100% auto;
              }
              dl{
                display:flex;
                dt{
                  border-top-left-radius: 12px;
                  border-bottom-left-radius: 12px;
                  width:121px;
                  height:216px;
                  background: linear-gradient(163.54deg, rgba(255, 183, 68, 0.2) 33.43%, rgba(255, 239, 141, 0.1) 92.76%);
                  position: relative;
                  .bg-wrap{
                    width:80px;
                    height:80px;
                    position: absolute;
                    top:-20px;
                    left:50%;
                    margin-left:-40px;
                    &.peizi-icon{
                      background: url('~/assets/images/coupon/peizi-bg.png') no-repeat center;
                      background-size: 100% auto;
                    }
                    &.tiyanjin-icon{
                      background: url('~/assets/images/coupon/tiyanjin-bg.png') no-repeat center;
                      background-size: 100% auto;
                    }
                    &.recharge-icon{
                      background: url('~/assets/images/coupon/recharge-bg.png') no-repeat center;
                      background-size: 100% auto;
                    }
                    &.fee-icon{
                      background: url('~/assets/images/coupon/fee-bg.png') no-repeat center;
                      background-size: 100% auto;
                    }
                    &.kuisun-icon{
                      background: url('~/assets/images/coupon/kuisun-bg.png') no-repeat center;
                      background-size: 100% auto;
                    }
                  }
                  &:after{
                    content:'';
                    position:absolute;
                    right:-15px;
                    top:0;
                    bottom:0;
                    width:15px;
                    height:216px;
                    background:url('~/assets/images/coupon/dt-bg-img.png') no-repeat;
                    background-size:auto 100%;
                  }
                  .text-box{
                    text-align:center;
                    @include color(theme);
                    h3{
                      font-size:48px;
                    }
                    p{
                      font-size:14px;
                    }
                  }
                }
                dd{
                  padding:16px 16px 16px 24px;
                  flex:1;
                  div{
                    width:100%;
                  }
                  .info-title{
                    font-size:22px;
                    @include color(tc-primary);
                    .info-icon{
                      width:32px;
                      height:32px;
                      margin-right:12px;
                      &.peizi-icon{
                        background: url('~/assets/images/coupon/peizi-icon.png') no-repeat center;
                        background-size: 100% auto;
                      }
                      &.tiyanjin-icon{
                        background: url('~/assets/images/coupon/tiyanjin-icon.png') no-repeat center;
                        background-size: 100% auto;
                      }
                      &.recharge-icon{
                        background: url('~/assets/images/coupon/recharge-icon.png') no-repeat center;
                        background-size: 100% auto;
                      }
                      &.fee-icon{
                        background: url('~/assets/images/coupon/fee-icon.png') no-repeat center;
                        background-size: 100% auto;
                      }
                      &.kuisun-icon{
                        background: url('~/assets/images/coupon/kuisun-icon.png') no-repeat center;
                        background-size: 100% auto;
                      }
                    }
                  }
                  .info-desc{
                    width:89%;
                    padding-top:12px;
                    font-size:14px;
                    display: -webkit-box;
                    align-items: flex-start;
                    justify-content: flex-start;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    @include color(tc-secondary);
                  }
                  .info-btn-box{
                    padding-top:12px;
                    font-size: 14px;
                    @include color(tc-secondary);
                    .el-button{
                      border-radius:28px;
                      height:40px;
                      min-width:134px;
                    }
                  }
                  .info-rule{
                    margin-top:12px;
                    cursor: pointer;
                    border-top:1px dashed;
                    font-size:14px;
                    padding:8px 0 0;
                    @include color(tc-secondary);
                    @include border-color(border);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.coupon-rule-title{
  font-size:16px;
  margin-top:12px;
  margin-bottom:8px;
  @include color(tc-primary);
}
.coupon-rule-text{
  padding:8px 0 0px;
  margin-top:-12px;
}
@media only screen and (min-width: 768px) and (max-width: 1280px) {
  .coupon-container{
    .coupon-wrapper{
      .coupon-banner{
        .banner-center{
          .banner-left{
          padding-left:20px;
          h1{
            font-size:40px;
            padding-bottom:12px;
            @include color(tc-primary);
          }
          h2{
            display:none;
          }
          p{
            font-size:24px;
            @include color(tc-primary);
            span{
              @include color(theme);
            }
          }
        }
          .banner-right{
            width:250px;
            margin-right:10%;
          }
        }
      }
      .coupon-list-container{
        padding:0 20px;
        max-width:1920px;
        margin:0 auto;
        .coupon-list-title{
          padding:20px 0;
          h2{
            font-size:28px;
            @include color(tc-primary);
          }
          .coupon-title-right{
            width: 200px;
          }
        }
        .coupon-list-cont{
          padding-bottom:20px;
          .coupon-list-wrap{
            .coupon-item-wrap{
              overflow:hidden;
              flex: 1;
              height: auto;
              margin: 0 20px 20px 0;
              max-width: calc(50% - 14px);
              min-width: calc(50% - 14px);
              position: relative;
              width: calc(50% - 14px);
              &.disabled{
                filter: grayscale(100%);
              }
              &:nth-child(3n){
                margin-right: 20px;
              }
              &:nth-child(2n){
                margin-right: 0px;
              }
              .coupon-item-cont{
                box-shadow: 0px 8px 12px 0px rgba(0, 0, 0, 0.04);
                border:1px solid;
                border-radius:12px;
                position: relative;
                @include border-color(border);
                @include bg-color(bg-primary);
                &:before{
                  content:'';
                  display:block;
                  width:26px;
                  height:26px;
                  border-radius:50%;
                  border:1px solid;
                  position: absolute;
                  left:120px;
                  top:-13px;
                  z-index:9;
                  @include bg-color(bg-primary);
                  @include border-color(border);
                }
                &:after{
                  content:'';
                  display:block;
                  width:26px;
                  height:26px;
                  border-radius:50%;
                  border:1px solid;
                  position: absolute;
                  left:120px;
                  bottom:-13px;
                  z-index:9;
                  @include bg-color(bg-primary);
                  @include border-color(border);
                }
                dl{
                  display:flex;
                  dt{
                    border-top-left-radius: 12px;
                    border-bottom-left-radius: 12px;
                    width:121px;
                    height:216px;
                    background: linear-gradient(163.54deg, rgba(255, 183, 68, 0.2) 33.43%, rgba(255, 239, 141, 0.1) 92.76%);
                    position: relative;
                    &:after{
                      content:'';
                      position:absolute;
                      right:-15px;
                      top:0;
                      bottom:0;
                      width:15px;
                      height:216px;
                      background:url('~/assets/images/coupon/dt-bg-img.png') no-repeat;
                      background-size:auto 100%;
                    }
                    .text-box{
                      text-align:center;
                      @include color(theme);
                      h3{
                        font-size:40px;
                      }
                      p{
                        font-size:14px;
                      }
                    }
                  }
                  dd{
                    padding:16px 16px 16px 24px;
                    flex:1;
                    div{
                      width:100%;
                    }
                    .info-title{
                      font-size:22px;
                      @include color(tc-primary);
                      .info-icon{
                        width:32px;
                        height:32px;
                        margin-right:12px;
                        &.peizi-icon{
                          background: url('~/assets/images/coupon/peizi-icon.png') no-repeat center;
                          background-size: 100% auto;
                        }
                        &.tiyanjin-icon{
                          background: url('~/assets/images/coupon/tiyanjin-icon.png') no-repeat center;
                          background-size: 100% auto;
                        }
                        &.recharge-icon{
                          background: url('~/assets/images/coupon/recharge-icon.png') no-repeat center;
                          background-size: 100% auto;
                        }
                        &.fee-icon{
                          background: url('~/assets/images/coupon/fee-icon.png') no-repeat center;
                          background-size: 100% auto;
                        }
                        &.kuisun-icon{
                          background: url('~/assets/images/coupon/kuisun-icon.png') no-repeat center;
                          background-size: 100% auto;
                        }
                      }
                    }
                    .info-desc{
                      padding-top:12px;
                      font-size:14px;
                      display: -webkit-box;
                      -webkit-box-orient: vertical;
                      -webkit-line-clamp: 2;
                      overflow: hidden;
                      @include color(tc-secondary);
                    }
                    .info-btn-box{
                      padding-top:12px;
                      font-size: 14px;
                      @include color(tc-secondary);
                      .el-button{
                        border-radius:28px;
                        height:40px;
                        min-width:134px;
                      }
                    }
                    .info-rule{
                      margin-top:12px;
                      cursor: pointer;
                      border-top:1px dashed;
                      font-size:14px;
                      padding:8px 0 0;
                      @include color(tc-secondary);
                      @include border-color(border);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
@include mb {
  .coupon-container{
    .coupon-wrapper{
      .coupon-banner{
        width:100%;
        height:auto;
        .banner-center{
          max-width:100%;
          height:auto;
          overflow:hidden;
          .banner-left{
            padding: 20px 16px;
            h1{
              display:none;
            }
            h2{
              display:block;
              font-size:32px;
              padding-bottom:12px;
              @include color(tc-primary);
            }
            p{
              font-size:14px;
              @include color(tc-secondary);
              span{
                @include color(theme);
              }
            }
          }
          .banner-right{
            display:none;
          }
        }
      }
      .coupon-list-container{
        padding:0 16px;
        max-width:auto;
        margin:0 auto;
        .coupon-list-title{
          padding:16px 0;
          h2{
            display:none;
          }
          .coupon-nav-list{
            flex:1;
            li{
              cursor:pointer;
              font-size:16px;
              margin-right:12px;
              position: relative;
              @include color(tc-secondary);
              &.active{
                &:after{
                  content:'';
                  display: block;
                  position: absolute;
                  width:14px;
                  height:2px;
                  bottom:-10px;
                  left:50%;
                  margin-left:-8px;
                  @include bg-color(theme);
                }
                @include color(tc-primary);
              }
            }
          }
          .coupon-title-right{
            flex:1;
            width:auto;
          }
        }
        .coupon-list-cont{
          .coupon-list-wrap{
            .coupon-item-wrap{
              overflow:hidden;
              flex: 1;
              height: auto;
              margin: 0;
              margin-bottom:16px;
              max-width: 100%;
              min-width: 100%;
              position: relative;
              width: 100%;
              .coupon-item-cont{
                &:before{
                  content:'';
                  display:block;
                  width:15px;
                  height:15px;
                  left:76px;
                  top:-7.5px;
                  z-index:9;
                }
                &:after{
                  content:'';
                  display:block;
                  width:15px;
                  height:15px;
                  left:76px;
                  bottom:-7.5px;
                  z-index:9;
                }
                .tag{
                  width:80px;
                  height:80px;
                  font-size:12px;
                }
                dl{
                  display:flex;
                  dt{
                    width:76px;
                    height:auto;
                    .bg-wrap{
                      width:60px;
                      height:60px;
                      position: absolute;
                      top:-20px;
                      left:50%;
                      margin-left:-30px;
                    }
                    &:after{
                      content:'';
                      position:absolute;
                      right:-15px;
                      top:0;
                      bottom:0;
                      width:15px;
                      height:auto;
                      background:url('~/assets/images/coupon/dt-bg-img-m.png') no-repeat;
                      background-size:auto 100%;
                    }
                    .text-box{
                      h3{
                        font-size:24px;
                      }
                      p{
                        font-size:14px;
                      }
                    }
                  }
                  dd{
                    padding:12px 12px 12px 24px;
                    flex:1;
                    .info-title{
                      font-size:16px;
                      @include color(tc-primary);
                      .info-icon{
                        width:20px;
                        height:20px;
                        margin-right:12px;
                      }
                    }
                    .info-desc{
                      padding-top:8px;
                      font-size:14px;
                    }
                    .info-btn-box{
                      padding-top:8px;
                      font-size: 14px;
                      @include color(tc-secondary);
                      .el-button{
                        border-radius:28px;
                        height:32px;
                        min-width:inherit;
                      }
                    }
                    .info-rule{
                      margin-top:12px;
                      cursor: pointer;
                      border-top:1px dashed;
                      font-size:14px;
                      padding:8px 0 0;
                      @include color(tc-secondary);
                      @include border-color(border);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .coupon-rule-title{
    margin-top:0;
  }
  .coupon-rule-text{
    padding:16px 0;
  }
}