
@font-face {
  font-family: 'DINPro';
  src: url('~/assets/fonts/DINPro-Medium.ttf');
}
$zh-cn: 'zh'; // 简体中文
$en-us: 'en'; // 英文
$ko-kr: 'ko';
body{
  font-family: "DINPro", "SF Pro SC", "SF Pro Text", "Helvetica Neue", "Helvetica", "Arial", "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif !important;
  font-weight:500;
  @include bg-color(bg-primary);
  [lang=#{$zh-cn}] & {
    font-family: "DINPro", "SF Pro SC", "SF Pro Text", "Helvetica Neue", "Helvetica", "Arial", "PingFang SC", "Microsoft YaHei", "微软雅黑", sans-serif !important;
  }
  [lang=#{$ko-kr}] & {
    font-family: "DINPro", "SF Pro KR", "SF Pro Text", "Apple Gothic", "HY Gulim", "MalgunGothic", "HY Dotum", "Lexi Gulim", "Helvetica Neue", "Helvetica", "Arial", sans-serif  !important;
  }
}
.el-message-box__title{
  @include color(tc-primary);
}
.eye-icon{
  cursor: pointer;
  padding:10px 0;
  cursor: pointer;
  svg{
    font-size:16px !important;
    @include color(tc-primary);
  }
}
// 覆盖修改element-plus样式
/* 取消input的上下箭头 */
html{
  &.light{
    input::-moz-placeholder {
      font-size:14px !important;
      color:#cbced8;
    }
    input::-webkit-input-placeholder{
      font-size:14px !important;
      color:#cbced8;
    }
    input::placeholder{
      font-size:14px !important;
      color:#cbced8;
    }
  }
  &.dark{
    input::-moz-placeholder {
      font-size:14px !important;
      color:#4D5362;
    }
    input::-webkit-input-placeholder{
      font-size:14px !important;
      color:#4D5362;
    }
    input::placeholder{
      font-size:14px !important;
      color:#4D5362;
    }
  }
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
 
input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}
 
input[type="number"] {
  -moz-appearance: textfield;
}
.el-input{
  border:0;
}
.el-input__inner{
  @include border-color(border);
}
.el-input__prefix{
  display:flex;
  align-items: center;
  padding:0px 12px;
  @include color(tc-secondary);
}
.el-input--prefix .el-input__inner{
  padding-left:48px;
}
// 覆盖修改element-ui样式
.el-button{
  padding:8px 18px;
  background: transparent !important;
  @include color(theme);
  @include border-color(theme);
  @include pc-hover {
    &:hover{
      background: rgba(240, 185, 11, 0.1) !important;
      @include border-color(theme);
    }
  }
  &.el-button--primary{
    background: getSingleColor(theme, 1, light) !important;
    color: getSingleColor(tc-primary, 1, light) !important;
    @include pc-hover {
      &:hover{
        background: getSingleColor(theme, 1, dark) !important;
        color: getSingleColor(tc-primary, 1, light) !important;
      }
    }
  }
  &.is-disabled{
    opacity:0.5;
  }
}
.el-popover, .el-dialog{
  border-radius:12px;
  padding:0;
  @include border-color(border);
  @include color(tc-primary);
}
.el-input__inner{
  height: 42px !important;
  font-size:16px !important;
  border-radius:6px;
  @include pc-hover {
    &:hover, &:focus, &:active{
      @include border-color(theme);
    }
  }
}
.el-tabs__nav-wrap{
  &:after{
    @include bg-color(border);
  }
}
.el-table::before, .el-table--group::after, .el-table--border::after{
  @include bg-color(border);
}
.el-table{
  @include bg-color(bg-primary);
  @include color(tc-primary);
  .el-table__inner-wrapper{
    &:before{
      display:none;
    }
  }
  th{
    &.el-table__cell{
      border:0 !important;
      font-weight:normal;
      font-size:14px;
      @include bg-color(bg-primary);
      @include border-color(border);
      @include color(tc-secondary);
    }
  }
  td{
    &.el-table__cell{
      border:0;
      @include bg-color(bg-primary);
      @include border-color(border);
    }
  }
  tr{
    @include bg-color(bg-primary);
    @include pc-hover {
      &:hover{
        @include bg-color(bg-quaternary);
        td{
          &.el-table__cell{
            border:0;
            @include bg-color(bg-quaternary);
          }
        }
      }
    }
  }
}
.el-pagination{
  &.is-background{
    .btn-prev,.btn-next{
      border:1px solid;
      width:32px;
      height:32px;
      line-height:32px;
      max-width:32px;
      border-radius:4px;
      margin:0 6px;
      @include border-color(border);
      @include bg-color(bg-primary);
      @include color(tc-primary);
      @include pc-hover {
        &:hover{
          @include bg-color(bg-quaternary);
        }
      }
    }
    .el-pager{
      li{
        width:32px;
        height:32px;
        line-height:32px;
        max-width:32px;
        border-radius:4px;
        border:1px solid;
        margin:0 6px;
        cursor: pointer;
        @include bg-color(bg-primary);
        @include color(tc-primary);
        @include border-color(border);
        @include pc-hover {
          &:hover{
            @include bg-color(bg-quaternary);
          }
        }
        &.is-active{
          background:#f0b90b !important;
          color:#414655 !important;
          &:hover{
            background:#f0b90b !important;
            color:#414655 !important;
          }
        }
      }
    }
  }
}
.el-form{
  .el-form-item{
    display:block;
  }
  .el-form-item__label{
    font-size:14px;
    height:20px;
    line-height:20px;
    margin-bottom:8px;
    @include color(tc-primary);
    &:before{
      display:none;
    }
  }
}
.el-dialog__headerbtn{
  .el-dialog__close{
    display:block;
    width:20px;
    height:20px;
    // @include get-img('@/assets/images/common/icon_more_nor-light.png','@/assets/images/common/icon_more_nor-dark.png');
    background-size:100% auto;
    @include pc-hover {
      &:hover{
        // @include get-img('@/assets/images/common/icon_more_nor-light-hover.png','@/assets/images/common/icon_more_nor-dark-hover.png');
        background-size:100% auto;
      }
    }
    &:before{
      display:none;
    }
  }
  .el-dialog__title{
    @include color(tc-primary);
  }
}
.el-dialog{
  .el-dialog__header{
    padding:24px;
    &.show-close{
      padding-right:48px;
    }
    .el-dialog__title{
      @include color(tc-primary);
    }
    .el-dialog__headerbtn{
      top:10px;
    }
    .el-dialog__close{
      font-size:22px;
    }
  }
  .el-dialog__body{
    padding:0 24px 40px;
  }
  .el-button{
    width:100%;
    height:40px;
  }
}

.el-dropdown-menu{
  .el-dropdown-menu__item{
    padding:8px 16px;
    @include pc-hover {
      &:hover, &:focus{
        @include bg-color(bg-quaternary);
      }
    }
    &.active{
      position: relative;
      @include color(theme);
      &:after{
        content:'';
        position: absolute;
        top:50%;
        margin-top:-8px;
        right:16px;
        width:16px;
        height:16px;
        background:url('@/assets/images/common/icon_active-right.png') no-repeat center;
        background-size:100% auto;
      }
      @include pc-hover {
        &:hover{
          [class^=light] & {
            background:#fff;
          }
          [class^=right] & {
            background: #2b2c31;
          }
        }
      }
    }
  }
}
.el-breadcrumb{
  .el-breadcrumb__item{
    .el-breadcrumb__inner{
      font-size:16px;
      @include color(tc-primary);
      &.is-link{
        font-weight:normal;
        @include color(tc-secondary);
      }
    }
    .el-icon{
      @include color(tc-secondary);
      svg{
        font-size:16px !important;
      }
    }
  }
}
.el-steps{
  &.el-steps--vertical{
    .el-step{
      min-height:100px;
      .el-step__head{
        width:20px;
        .el-step__line{
          left:10px;
        }
        .el-step__icon{
          border:0;
          width:20px;
          height:20px;
          display:block;
          line-height:20px;
          text-align: center;
        }
        &.is-finish{
          .el-step__line{
            @include bg-color(theme);
          }
          .el-step__icon{
            @include bg-color(theme);
            @include color(tc-button);
          }
        }
        &.is-process{
          .el-step__line{
            @include bg-color(theme);
          }
          .el-step__icon{
            @include bg-color(theme);
            @include color(tc-button);
          }
        }
        &.is-wait{
          .el-step__line{
            @include bg-color(tc-tertiary);
          }
          .el-step__icon{
            @include bg-color(tc-tertiary);
            @include color(tc-button);
          }
        }
      }
      .el-step__main{
        margin-top:-2px;
        padding-bottom:20px;
        padding-left:12px;
        .el-step__title{
          font-size:20px;
          font-weight:500;
          padding-bottom:12px;
          &.is-finish{
            @include color(tc-primary);
          }
          &.is-process{
            @include color(tc-primary);
          }
          &.is-wait {
            @include color(tc-secondary);
          }
        }
        .el-step__description{
          padding-right:0;
          &.is-finish, &.is-process, &.is-wait{
            @include color(tc-primary);
          }
          &.is-wait{
            // display:none;
          }
        }
      }
    }
  }
}
.el-steps{
  &.el-steps--horizontal{
    .el-step{
      .el-step__head{
        &:last-child{
          width:20px;
        }
        .el-step__icon{
          border:0;
          width:20px;
          height:20px;
          display:block;
          line-height:20px;
          text-align: center;
        }
        &.is-finish{
          .el-step__line{
            @include bg-color(theme);
          }
          .el-step__icon{
            @include bg-color(theme);
            @include color(tc-button);
          }
        }
        &.is-process{
          .el-step__line{
            @include bg-color(theme);
          }
          .el-step__icon{
            @include bg-color(theme);
            @include color(tc-button);
          }
        }
        &.is-wait{
          .el-step__line{
            @include bg-color(tc-tertiary);
          }
          .el-step__icon{
            @include bg-color(tc-tertiary);
            @include color(tc-button);
          }
        }
      }
      .el-step__main{
        &:last-child{
          width:20px;
        }
        .el-step__title{
          &.is-finish{
            @include color(tc-primary);
          }
          &.is-process{
            @include color(tc-primary);
          }
          &.is-wait {
            @include color(tc-secondary);
          }
        }
      }
    }
  }
}
.el-checkbox__inner{
  width:18px !important;
  height:18px !important;
  border:0 !important;
  border-radius:0px !important;
  background: url('~/assets/images/common/icon_check_nor.png') no-repeat center !important;
  background-size:100% auto !important;
}
.el-checkbox__input{
  &.is-checked{
    .el-checkbox__inner{
      width:18px !important;
      height:18px !important;
      border:0 !important;
      border-radius:0px !important;
      background: url('~/assets/images/common/icon_check_sel.png') no-repeat center !important;
      background-size:100% auto !important;
      &:after{
        display:none;
      }
    }
  }
}
.el-select__caret{
  &:after{
    content: '';
    display:block;
    width:12px;
    height:12px;
    background: url('@/assets/images/common/icon_arrow.png') no-repeat center;
    background-size:100% auto;
  }
  svg{
    display: none;
  }
}
// 解决登录框自动填充账号密码背景色

html.light input:-internal-autofill-previewed,
html.light input:-internal-autofill-selected {
  color:#414655 !important;
  transition: background-color 1500s ease-out 10s;
  -webkit-text-fill-color: #414655; /* 确保字体颜色生效 */
  -moz-text-fill-color: #414655; /* 确保字体颜色生效 */
  background:#ffffff !important;
}
html.dark input:-internal-autofill-previewed,
html.dark input:-internal-autofill-selected {
  color:#ffffff !important;
  transition: background-color 1500s ease-out 10s;
  -webkit-text-fill-color: #ffffff; /* 确保字体颜色生效 */
  -moz-text-fill-color: #ffffff; /* 确保字体颜色生效 */
  background:#181A1F !important;
}
html{
  &.light{
    .el-popover, .el-dialog{
      background-color:#ffffff;
      .el-dialog__title{
        color:#414655 !important;
      }
    }
  }
  &.dark{
    .el-popover, .el-dialog{
      background-color: #2b2c31;
      .el-dialog__title{
        color:#ffffff !important;
      }
      .popper__arrow{
        border-bottom-color: #363a45;
        &:after{
          border-bottom-color: #2b2c31;
        }
      }
    }
  }
}
html.light{
  .el-button--primary{
    --el-button-text-color: var(--el-color-black);
    @include pc-hover {
      &:hover{
        color: var(--el-color-black);
      }
    }
  }
}
.icon-my-loading {
  display: inline-flex;
  overflow: hidden;
  animation: rotating 1.5s linear infinite;
}
.div-table-cell{
  .el-loading-parent--relative{
    opacity:0.5;
    cursor: default;
    .el-loading-mask{
      background:none;
      .el-loading-spinner{
        left:-26px;
        margin-top:-8px;
        svg{
          opacity: 1;
          width:14px;
          height:14px;
        }
      }
    }
  }
}
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.el-popover.el-popper{
  padding:12px !important;
}
.el-popover.el-popper, .el-dialog{
  border-radius:4px;
  padding:0;
}
.el-popper.is-dark, .el-popper.is-dark .el-popper__arrow:before{
  color:#ffffff;
  background: getSingleColor(bg-fourthary, 1, dark) !important;
  border: getSingleColor(bg-fourthary, 1, dark) !important;
}
html{
  &.dark{
    .el-popper{
      color:#fff !important;
    }
  }
}
.el-input__wrapper{
  border-radius:4px;
  @include pc-hover {
    &:hover{
      box-shadow: 0 0 0 1px var(--el-color-primary-light-3) inset;
    }
  }
}
.el-input-group--prepend>.el-input__wrapper{
  border-bottom-left-radius: 4px !important;
  border-top-left-radius: 4px !important;
}
.el-input-group--append>.el-input__wrapper{
  border-bottom-right-radius: 4px !important;
  border-top-right-radius: 4px !important;
}
.el-input__inner{
  height: 42px;
  border-radius:8px;
}
.el-input-group__prepend{
  border:0;
  box-shadow:none;
  background: none;
}
.el-input{
  .el-input__wrapper{
    background:none;
  }
  &.is-disabled{
    .el-input__wrapper{
      @include bg-color(bg-tertiary);
    }
  }
  &.el-input-group--append, &.el-input-group--prepend{
    position: relative;
    .el-input-group__prepend, .el-input-group__append{
      position: absolute;
      padding:0;
      background:none;
      z-index:2;
      border:0;
      box-shadow: none;
    }
    .el-input-group__prepend{
      left:12px;
      top:0;
    }
    .el-input-group__append{
      right:12px;
      top:0;
      box-shadow:none;
    }
  }
  &.el-input-group--append{
    .el-input__inner{
      padding-right:32px;
    }
  }
  &.el-input-group--prepend{
    .el-input__inner{
      padding-left:32px;
    }
  }
}
.el-input__suffix{
  .el-input__suffix-inner {
    border-color: none;
    .el-input__clear{
      position: relative;
      // 默认清空按钮隐藏
      >svg{
      display: none;
      }
      &:after{
        //对应图标
        position: absolute;
        content:'';
        display: block;
        width: 16px;
        height: 16px;
        background-size:16px auto;
        @include get-img('@/assets/images/common/icon_more_nor-light.png','@/assets/images/common/icon_more_nor-dark.png');
      }
    }

  }
}
.el-select__wrapper.is-disabled{
  @include bg-color(bg-tertiary);
}
.el-select-dropdown__item.is-hovering{
  @include bg-color(bg-quaternary);
}
.el-select__wrapper{
  height:44px;
  background:none;
}
.el-loading-mask{
  [class^="light"] & {
    background-color: #ffffff !important;
  }
  [class^="dark"] & {
    background-color: #181A1F !important;
  }
}
@include md {

}
.el-message-box{
  padding:24px 32px 32px !important;
  .el-message-box__header{
    padding-bottom:18px;
  }
  .el-message-box__headerbtn{
    top:12px;
    right:12px;
    .el-icon {
      font-size:20px!important;
      width:20px !important;
      height:20px!important;
      .el-message-box__close{
        svg{
          width:20px !important;
          height:20px!important;
        }
      }
    }
  }
  .el-message-box__btns{
    display: flex;
    padding-top:24px;
    .el-button{
      flex:1;
      height:40px;
    }
  }
}
@include mb {
  .el-popover{
    width:98% !important;
  }
  .el-dialog{
    width:88% !important;
  }
  .date-picker-box{
    .van-picker{
      [class="light"] & {
        background:#fff;
      }
      [class="dark"] & {
        background:#181A1F;
      }
      .van-picker-column__item{
        @include color(tc-primary);
      }
      [class="dark"] & {
        .van-picker__mask{
          background-image:linear-gradient(180deg, rgba(24, 26, 31, .6), rgba(24, 26, 31, .1)), linear-gradient(0deg, rgba(24, 26, 31, .6), rgba(24, 26, 31, .1));
        }
      }
    }
  }
  .el-message-box{
    padding:24px !important;
    width:90% !important;
    .el-message-box__header{
      padding-bottom:18px;
    }
    .el-message-box__headerbtn{
      top:12px;
      right:12px;
      .el-icon {
        font-size:20px!important;
        width:20px !important;
        height:20px!important;
        .el-message-box__close{
          svg{
            width:20px !important;
            height:20px!important;
          }
        }
      }
    }
    .el-message-box__btns{
      display: flex;
      padding-top:24px;
      .el-button{
        flex:1;
        height:40px;
      }
    }
  }
  .el-steps{
    &.el-steps--vertical{
      .el-step{
        .el-step__main{
          .el-step__title{
            font-size:16px;
          }
        }
      }
    }
  }
}
@include mb {
  .el-dialog .el-dialog__header{
    padding:16px;
  }
  .el-dialog .el-dialog__body{
    padding:0 16px 24px;
  }
  .el-dialog__headerbtn{
    right:-10px;
  }
}
