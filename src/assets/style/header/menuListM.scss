@import url('@/assets/style/animate.scss');
.menu-list-mobile-cont{
  position:fixed;
  top:0;
  left:0;
  right:0;
  bottom:0;
  z-index:9999;
  @include bg-color(bg-primary);
  .menu-list-mobile-close{
    position:fixed;
    top:18px;
    right:20px;
    width:28px;
    height:28px;
    z-index:15;
    border-radius:50%;
    cursor:pointer;
    @include bg-color(bg-tertiary);
    @include color(tc-primary);
  }
  .menu-list-mobile-head{
    width:100%;
    height:64px;
    position:absolute;
    top:0;
    right:0;
    left:0;
    z-index:10;
    .back-btn{
      margin-left:20px;
      width:28px;
      height:28px;
      text-align:center;
      line-height:28px;
      border-radius:50%;
      @include bg-color(bg-quaternary);
      @include color(tc-primary);
      svg{
        transform: rotate(180deg);
      }
    }
    .menu-list-mobile-head-wrap{
      margin:0 20px;
      border-bottom:1px solid;
      height:63px;
      @include border-color(border);
      .search-input-text{
        height:63px;
        .icon-search{
          font-size:20px;
          margin-right:16px;
          @include color(tc-secondary);
        }
        .input-text{
          cursor:pointer;
          margin-right:42px;
          font-size:14px;
          @include color(tc-secondary);
        }
        .el-input{
          &.searchInputM{
            .el-input__wrapper{
              border:0;
              box-shadow: none;
              padding:0;
              padding-right:16px;
            }
          }
        }
        .el-button{
          &.cancel-btn{
            background:none !important;
            border:0;
            box-shadow:none;
            font-size:14px;
            padding:0;
            @include color(tc-primary);
          }
        }
      }
    }
  }
  .menu-list-normal{
    position:absolute;
    top:64px;
    bottom:0;
    left:0;
    right:0;
    z-index:10;
    overflow-y:auto;
    .normal-title{
      padding:0 20px;
      height:60px;
      line-height:60px;
      font-size:20px;
      @include color(tc-primary);
    }
    ul{
      padding:0 20px;
      li{
        height:54px;
        line-height:54px;
        font-size:14px;
        @include color(tc-primary);
        &.active{
          position: relative;
          &:after{
            content:'';
            position: absolute;
            top:50%;
            margin-top:-8px;
            right:16px;
            width:16px;
            height:16px;
            background:url('@/assets/images/common/icon_active-right.png') no-repeat center;
            background-size:100% auto;
          }
        }
      }
    }
  }
  .menu-list-mobile-foot{
    width:100%;
    height:54px;
    line-height:54px;
    position:absolute;
    bottom:0;
    right:0;
    left:0;
    z-index:10;
    .menu-list-mobile-foot-wrap{
      text-align:center;
      margin: 0 20px;
      border-top:1px solid;
      @include border-color(border);
      @include color(tc-primary);
    }
  }
  .menu-list-mobile-search{
    position:absolute;
    top:64px;
    bottom:0;
    left:0;
    right:0;
    z-index:10;
    overflow-y:auto;
    .menu-list-mobile-search-wrap{
      padding:0 20px;
      .search-history-list{
        .history-title{
          height:46px;
          @include color(tc-secondary);
        }
        .history-list{
          li{
            padding:8px 12px;
            border-radius:8px;
            display:inline-block;
            margin-right:12px;
            font-size:14px;
            margin-bottom:12px;
            @include bg-color(bg-quaternary);
            a{
              @include color(tc-primary);
            }
            &:active{
              a{
                @include color(theme);
              }
            }
          }
        }
      }
      .search-list-cont{
        .search-list-wrap{
          .cont-wrap{
            border-bottom:1px solid;
            @include border-color(border);
            &:last-child{
              border-bottom:0;
            }
            h2{
              font-size:14px;
              line-height: 46px;
              font-weight:normal;
              @include color(tc-secondary);
            }
            ul{
              li{
                padding:10px 0px;
                .li-left{
                  .name{
                    h3{
                      font-size:14px;
                      @include color(tc-primary);
                    }
                    p{
                      font-size:12px;
                      @include color(tc-secondary);
                    }
                  }
                }
                .li-right{
                  h3{
                    text-align:right;
                    font-size:18px;
                  }
                  p{
                    text-align:right;
                    font-size:14px;
                    @include color(tc-primary);
                  }
                }
              }
              .more-bx{
                line-height:44px;
                @include color(theme);
                em{
                  font-style:inherit;
                }
                svg{
                  &.icon-up{
                    transform: rotate(180deg);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .menu-list-mobile-cont{
    position:absolute;
    top:74px;
    bottom:54px;
    left:0;
    right:0;
    z-index:10;
    overflow-y:auto;
    &.isMenuUser{
      top:24px;
    }
    .menu-list-mobile-wrap{
      .userLogin{
        .user-info-name{
          padding:16px 20px;
          text-align:center;
          font-size:20px;
          @include color(tc-primary);
          .user-gray-box{
            font-size:12px;
            margin-left:12px;
            padding:4px 8px;
            border-radius:4px;
            @include bg-color(bg-quaternary);
            @include color(tc-secondary);
          }
          .user-level-box{
            margin-left:12px;
            margin-top:12px;
            font-size:12px;
            padding:4px 8px;
            border-radius:4px;
            background:rgba(240, 185, 11, 0.1);
            @include color(theme);
          }
        }
        .warning-text{
          font-size:12px;
          padding:4px 8px;
          border-radius:2px;
          background: rgba(219, 99, 114, 0.1);
          cursor:pointer;
          @include color(warn);
        }
        .waiting-text{
          font-size:12px;
          padding:4px 8px;
          border-radius:2px;
          cursor:pointer;
          @include color(tc-secondary);
          @include bg-color(bg-quaternary);
        }
        .success-text{
          font-size:12px;
          padding:4px 8px;
          border-radius:2px;
          background: rgba(59, 193, 137, 0.1);
          cursor:pointer;
          @include color(rise);
        }
        .login-register-btn{
          padding:16px 20px;
          .el-button {
            width:100%;
            height:42px;
            border-radius:30px;
            &.login-btn{
              @include color(theme);
              @include border-color(theme);
            }
          }
        }
      }
      .menu-list-cont-box{
        ul{
          padding:0 20px;
          li{
            padding:16px 0;
            @include color(tc-primary);
            .list-icon-m{
              width:24px;
              height:24px;
              margin-right:12px;
              &.qiehuan{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zizhanghuqie-light.png', '@/assets/images/common/icon_zizhanghuqie-dark.png');
              }
              &.zhanghu{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zhanghu-light.png', '@/assets/images/common/icon_zhanghu-light.png');
              }
              &.api{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_api-light.png', '@/assets/images/common/icon_api-dark.png');
              }
              &.shiming{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_shiming-light.png','@/assets/images/common/icon_shiming-dark.png');
              }
              &.chongzhi{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_chongzhi-light.png','@/assets/images/common/icon_chongzhi-dark.png');
              }
              &.hangqing{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_hangqing-light.png','@/assets/images/common/icon_hangqing-dark.png');
              }
              &.future{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_future-light.png','@/assets/images/common/icon_future-dark.png');
              }
              &.exchange{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_exchange-light.png','@/assets/images/common/icon_exchange-dark.png');
              }
              &.zonglan{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zonglan-light.png','@/assets/images/common/icon_zonglan-dark.png');
              }
              &.zichan{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zichan-light.png','@/assets/images/common/icon_zichan-dark.png');
              }
              &.orders{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_orders-light.png','@/assets/images/common/icon_orders-dark.png');
              }
              &.zizhanghu{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zizhanghu-light.png','@/assets/images/common/icon_zizhanghu-dark.png');
              }
              &.shezhi{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_shezhi-light.png','@/assets/images/common/icon_shezhi-dark.png');
              }
              &.yaoqing{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_yaoqing-light.png','@/assets/images/common/icon_yaoqing-dark.png');
              }
              &.help{
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_help-light.png','@/assets/images/common/icon_help-dark.png');
              }
              &.launchpool{
                background-size:100% auto;
                @include get-img('@/assets/images/common/Launchpool-m-light.png','@/assets/images/common/Launchpool-m-light.png');
              }
              &.fuli{
                background-size:100% auto;
                @include get-img('@/assets/images/common/fuli-m-light.png','@/assets/images/common/fuli-m-dark.png');
              }
            }
            &.subli{
              padding:10px 0;
              @include color(tc-secondary);
            }
            p{
              font-size:16px;
            }
            .info-text{
              font-size:14px;
              @include color(tc-secondary);
            }
            .li-right{
              .right-arrow-box{
                transform:rotate(90deg);
                @include color(tc-primary);
                &.showMenu{
                  transform:rotate(-90deg);
                }
              }
              .theme-list{
                  border:1px solid;
                  padding:2px;
                  border-radius:6px;
                  @include color(border);
                .theme-item{
                  width:32px;
                  height:20px;
                  text-align:center;
                  @include color(tc-secondary);
                  &.active{
                    @include bg-color(bg-secondary);
                    @include color(tc-primary);
                  }
                  &:first-child{
                    border-top-left-radius:4px;
                    border-bottom-left-radius:4px;
                  }
                  &:last-child{
                    border-top-right-radius:4px;
                    border-bottom-right-radius:4px;
                  }
                }
              }
              svg{
                @include color(tc-secondary);
              }
            }
          }
          .box-line{
            width:100%;
            height:1px;
            @include bg-color(border);
          }
        }
      }
    }
  }
}