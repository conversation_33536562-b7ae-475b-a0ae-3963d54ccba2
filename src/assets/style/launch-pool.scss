.launch-pool-container{
  width:100%;
  min-height:calc(100vh - 74px);
  .launch-pool-container-center{
    max-width: 1920px;
    margin:0 auto;
  }
  .launch-pool-title-h5-cont{
    display:none;
  }
  .launch-pool-banner{
    width:100%;
    height:380px;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 100% 100%;
    @include get-img('~/assets/images/launch-pool/banner-bg-light.jpg', '~/assets/images/launch-pool/banner-bg-dark.jpg');
    .launch-pool-center{
      max-width:1920px;
      margin:0 auto;
      height:380px;
      padding:0 16px;
      .pool-cont{
        width:100%;
        height:100%;
        .left-cont{
          h1 {
            font-size:60px;
            font-weight:600;
            padding-bottom:20px;
            white-space: nowrap;
            @include color(tc-primary);
          }
          h2{
            font-size:30px;
            font-weight:500;
            padding-bottom:16px;
            @include color(tc-primary);
          }
          .search-cont{
            .el-input{
              &.search-input{
                height:50px;
              }
            }
            .el-button{
              width:120px;
              height:50px;
              border-radius:25px;
              margin-left:24px;
            }
          }
        }
        .right-cont{
          width:484px;
          img{
            width:100%;
            height:auto;
          }
        }
      }
      .pool-list{
        width:100%;
        height:120px;
        ul{
          width:100%;
          height:120px;
          li{
            font-size:14px;
            p{
              font-size:28px;
              padding-top:8px;
            }
          }
        }
      }
    }
  }
  .launch-pool-wrapper{
    min-height:500px;
    padding:28px 16px;
    @include bg-color(bg-secondary);
    .launch-pool-center-wrap{
      max-width:1920px;
      margin:0 auto;
      width:100%;
      height:auto;
      display:flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .launch-pool-info-box{
        border-radius:16px;
        flex: 1;
        height: auto;
        margin: 0 24px 24px 0;
        max-width: calc(33.333333% - 16px);
        min-width: calc(33.333333% - 16px);
        position: relative;
        width: calc(33.333333% - 16px);
        @include bg-color(bg-primary);
        &:nth-child(4n){
          margin-right: 24px;
        }
        .launch-pool-info-pd{
          padding:24px;
          .info-title{
            padding-bottom:12px;
            .title-left{
              font-size:18px;
              @include color(tc-primary);
              img{
                width:32px;
                height:auto;
              }
            }
            .title-right{
              font-size:12px;
              padding:4px 12px;
              border-radius:4px;
              @include color(tc-secondary);
              @include bg-color(bg-quaternary);
              &.rise{
                background-color:rgba(59, 193, 137, 0.1) !important;
                @include color(rise);
              }
              &.fall{
                background-color:rgba(255, 98, 98, 0.1) !important;
                @include color(fall);
              }
            }
          }
          .info-desc{
            font-size:14px;
            @include color(tc-secondary);
          }
          .symbol-cont-box{
            margin: 32px 0;
            .symbol-bd{
              width:48px;
              height:48px;
              border-radius:50%;
              border:2px solid;
              overflow:hidden;
              @include border-color(bg-primary);
              @include bg-color(bg-quaternary);
              &.mg-ln{
                margin-left:-10px;
              }
            }
            .icon-box{
              font-size:48px;
            }
            p{
              padding-top:12px;
              font-size:14px;
              @include color(tc-primary);
            }
          }
          ul{
            li{
              padding-bottom:8px;
              &:last-child{
                padding-bottom:0;
              }
            }
          }
        }
      }
    }
  }
  .launch-pool-wrapper-detail{
    padding:0 16px;
    .back-cont-box{
      height:65px;
      font-size:20px;
      cursor: pointer;
      svg{
        transform: rotate(180deg);
        margin-right:8px;
      }
    }
    .launch-pool-detail-info{
      width:100%;
      dl{
        width:100%;
        dt{
          .info-icon{
            font-size:60px;
            width:60px;
            height:60px;
          }
          margin-right:12px;
        }
        dd{
          flex:1;
          h2{
            font-weight:normal;
            .status-tag{
              font-size:14px;
              padding:4px 12px;
              border-radius:4px;
              @include color(tc-secondary);
              @include bg-color(bg-quaternary);
              &.rise{
                background-color:rgba(59, 193, 137, 0.1) !important;
                @include color(rise);
              }
              &.fall{
                background-color:rgba(255, 98, 98, 0.1) !important;
                @include color(fall);
              }
            }
          }
          .info-link-box{
            padding-top:8px;
            li{
              border-radius:4px;
              padding:4px 12px;
              margin-right:16px;
              font-size:12px;
              @include bg-color(bg-quaternary);
              a{
                @include color(tc-primary);
              }
            }
          }
        }
      }
      .info-desc{
        padding-top:20px;
        font-size:14px;
        @include color(tc-primary);
      }
      .lauch-pool-info-li-cont{
        ul{
          li{
            margin-right:12px;
          }
        }
      }
      .launch-pool-box{
        padding-top:20px;
        padding-bottom:40px;
        .box-info-cont{
          margin-right:20px;
          flex:1;
          border-radius:12px;
          @include bg-color(bg-quaternary);
          .box-info-cont-pd{
            padding:16px;
            .info-dl{
              .dl-icon{
                font-size:40px;
                width:40px;
                height:40px;
                margin-right:12px;
                margin-bottom:20px;
                border-radius:50%;
              }
              .info-dd{
                h2{
                  font-size:20px;
                  @include color(tc-primary);
                }
                p{
                  margin-top:8px;
                  font-size:14px;
                  line-height:16px;
                  height:36px;
                  @include color(tc-secondary);
                }
              }
            }
            .launch-btn{
              margin-top:24px;
              height:40px;
              width:100%;
            }
            .info-text{
              padding-top:8px;
              a{
                font-size:14px;
                @include color(theme);
              }
            }
          }
        }
        .box-info-btn{
          width:25%;
        }
      }
    }
    .launch-pool-login-cont{
      padding:40px;
      margin-top:30px;
      border-radius:12px;
      border: 1px solid rgba(240, 185, 11, 0.2);
      background: linear-gradient(90deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0.02) 100%);
    }
  }
}
@include pc {
  .launch-pool-container{
    width:100%;
    min-height:calc(100vh - 74px);
    .launch-pool-banner{
      width:100%;
      height:380px;
      @include bg-color(bg-quaternary);
      .launch-pool-center{
        height:380px;
        padding:0 16px;
        .pool-cont{
          width:100%;
          height:100%;
          .left-cont{
            h1 {
              font-size:60px;
              font-weight:600;
              padding-bottom:20px;
              @include color(tc-primary);
            }
            h2{
              font-size:30px;
              font-weight:500;
              padding-bottom:16px;
              @include color(tc-primary);
            }
            .search-cont{
              width:100%;
              .el-input{
                &.search-input{
                  height:50px;
                }
              }
              .el-button{
                width:auto;
                min-width:120px;
                height:50px;
                border-radius:25px;
                margin-left:24px;
                white-space: break-spaces;
              }
            }
          }
          .right-cont{
          }
        }
        .pool-list{
          width:100%;
          height:120px;
          ul{
            width:100%;
            height:120px;
            li{
              font-size:14px;
              p{
                font-size:28px;
                padding-top:8px;
              }
            }
          }
        }
      }
    }
    .launch-pool-wrapper{
      min-height:500px;
      padding:28px 16px;
      @include bg-color(bg-secondary);
      .launch-pool-center-wrap{
        max-width:1920px;
        margin:0 auto;
        width:100%;
        height:auto;
        display:flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .launch-pool-info-box{
          border-radius:16px;
          flex: 1;
          height: auto;
          margin: 0 16px 16px 0;
          max-width: calc(25% - 12px);
          min-width: calc(25% - 12px);
          position: relative;
          width: calc(25% - 12px);
          @include bg-color(bg-primary);
          &:nth-child(4n){
            margin-right: 0;
          }
          .launch-pool-info-pd{
            .info-title{
  
            }
            .info-desc{
  
            }
            .symbol-cont-box{
  
            }
            ul{
              li{
  
              }
            }
          }
        }
      }
    }
  }
}
@include md {
  .launch-pool-container{
    width:100%;
    min-height:calc(100vh - 74px);
    .launch-pool-banner{
      width:100%;
      height:380px;
      @include bg-color(bg-quaternary);
      .launch-pool-center{
        height:380px;
        padding:0 16px;
        .pool-cont{
          width:100%;
          height:100%;
          .left-cont{
            h1 {
              font-size:60px;
              font-weight:600;
              padding-bottom:20px;
              @include color(tc-primary);
            }
            h2{
              font-size:30px;
              font-weight:500;
              padding-bottom:16px;
              @include color(tc-primary);
            }
            .search-cont{
              .el-input{
                &.search-input{
                  height:50px;
                }
              }
              .el-button{
                width:120px;
                height:50px;
                border-radius:25px;
                margin-left:24px;
              }
            }
          }
          .right-cont{
          }
        }
        .pool-list{
          width:100%;
          height:120px;
          ul{
            width:100%;
            height:120px;
            li{
              font-size:14px;
              p{
                font-size:28px;
                padding-top:8px;
              }
            }
          }
        }
      }
    }
    .launch-pool-wrapper{
      min-height:500px;
      padding:28px 16px;
      @include bg-color(bg-secondary);
      .launch-pool-center-wrap{
        max-width:1920px;
        margin:0 auto;
        width:100%;
        height:auto;
        display:flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .launch-pool-info-box{
          border-radius:16px;
          flex: 1;
          height: auto;
          margin: 0 24px 24px 0;
          max-width: calc(33.333333% - 16px);
          min-width: calc(33.333333% - 16px);
          position: relative;
          width: calc(33.333333% - 16px);
          @include bg-color(bg-primary);
          &:nth-child(4n){
            margin-right: 24px;
          }
          &:nth-child(3n){
            margin-right: 0;
          }
          .launch-pool-info-pd{
            .info-title{
  
            }
            .info-desc{
  
            }
            .symbol-cont-box{
  
            }
            ul{
              li{
  
              }
            }
          }
        }
      }
    }
  }
}
@include mb {
  .launch-pool-container{
    width:100%;
    min-height:calc(100vh - 74px);
    .launch-pool-container-center{
      max-width: 100%;
      margin:0 auto;
    }
    .launch-pool-title-h5-cont{
      font-size:14px;
      display:flex;
      height:40px;
      align-items: center;
      padding:0 16px;
      border-bottom:1px solid;
      @include border-color(border);
      @include color(tc-secondary);
      span{
        &.active{
          @include color(tc-primary);
        }
      }
      svg{
        &.rotate{
          transform: rotate(90deg);
        }
      }
    }
    .launch-pool-banner{
      padding:20px 0 40px;
      width:100%;
      height:auto;
      @include bg-color(bg-quaternary);
      .launch-pool-center{
        max-width: 100%;
        height:auto;
        padding:0 16px;
        .pool-cont{
          position: relative;
          .left-cont{
            width:100%;
            h1 {
              font-size:24px;
              padding-bottom:10px;
            }
            h2{
              width:60%;
              font-size:24px;
              font-weight:500;
              padding-bottom:16px;
              @include color(tc-primary);
            }
            .search-cont{
              .el-input{
                &.search-input{
                  height:40px;
                }
              }
              .el-button{
                width:120px;
                height:40px;
                margin-left:16px;
              }
            }
          }
          .right-cont{
            display:none;
            position: absolute;
            width:50%;
            right:-8px;
            top:-6px;
          }
        }
        .pool-list{
          width:100%;
          height:120px;
          ul{
            width:100%;
            display:block;
            li{
              float:left;
              font-size:14px;
              flex: 1;
              width:50%;
              padding-top:20px;
              p{
                font-size:14px;
                padding-top:8px;
              }
            }
          }
        }
      }
    }
    .launch-pool-wrapper{
      min-height:500px;
      padding:16px;
      @include bg-color(bg-secondary);
      .launch-pool-center-wrap{
        max-width:1920px;
        margin:0 auto;
        width:100%;
        height:auto;
        display:flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .launch-pool-info-box{
          border-radius:16px;
          flex: 1;
          height: auto;
          margin: 0 0 16px 0;
          max-width: 100%;
          min-width: 100%;
          position: relative;
          width: 100%;
          @include bg-color(bg-primary);
          &:nth-child(4n){
            margin-right: 0;
          }
          &:nth-child(3n){
            margin-right: 0;
          }
          .launch-pool-info-pd{
            padding:24px;
            .info-title{
              padding-bottom:12px;
              .title-left{
                font-size:18px;
                @include color(tc-primary);
              }
              .title-right{
                font-size:12px;
                padding:4px 12px;
                border-radius:4px;
                @include color(tc-secondary);
                @include bg-color(bg-quaternary);
                &.rise{
                  @include color(rise);
                }
                &.fall{
                  @include color(fall);
                }
              }
            }
            .info-desc{
              font-size:14px;
              @include color(tc-secondary);
            }
            .symbol-cont-box{
              margin: 32px 0;
              .symbol-bd{
                width:48px;
                height:48px;
                border-radius:50%;
                border:2px solid;
                @include border-color(bg-primary);
                @include bg-color(bg-quaternary);
                &.mg-ln{
                  margin-left:-10px;
                }
              }
              .icon-box{
                font-size:48px;
              }
              p{
                padding-top:12px;
                font-size:14px;
                @include color(tc-primary);
              }
            }
            ul{
              li{
                padding-bottom:8px;
                &:last-child{
                  padding-bottom:0;
                }
              }
            }
          }
        }
      }
    }
    .launch-pool-wrapper-detail{
      padding:20px 16px 0;
      .back-cont-box{
        display:none;
      }
      .launch-pool-detail-info{
        width:100%;
        dl{
          width:100%;
          display:block;
          dt{
            width:32px;
            height:32px;
            img{
              width:100%;
              height:auto;
            }
            .info-icon{
              font-size:32px;
              width:32px;
              height:32px;
            }
            margin-right:12px;
          }
          dd{
            flex:1;
            display:block;
            h2{
              font-weight:normal;
              justify-content: space-between;
              margin-top:-34px;
              height:32px;
              line-height:32px;
              margin-left:44px;
              .font-size-24{
                font-size:18px !important;
              }
              .status-tag{
                font-size:14px;
                padding:4px 12px;
                border-radius:4px;
                @include color(tc-secondary);
                @include bg-color(bg-quaternary);
                &.rise{
                  @include color(rise);
                }
                &.fall{
                  @include color(fall);
                }
              }
            }
            .info-link-box{
              padding-top:20px;
              li{
                border-radius:4px;
                padding:4px 12px;
                margin-right:16px;
                font-size:12px;
                @include bg-color(bg-quaternary);
                a{
                  @include color(tc-primary);
                }
              }
            }
            .end-time-box{
              display:none;
            }
          }
        }
        .info-desc{
          padding-top:20px;
          font-size:14px;
          @include color(tc-primary);
        }
        .info-amount{
          &.pd-t32{
            padding-top:20px !important;
          }
          &.pd-b20{
            padding-bottom:12px !important;
          }
          .font-size-20{
            font-size:14px !important;
          }
        }
        .lauch-pool-info-li-cont{
          ul{
            display:block;
            li{
              display:flex;
              justify-content: space-between;
              margin-right:0;
              &.mg-r80{
                margin-right:0 !important;
              }
              .more-txt{
                width:80%;
                flex-wrap: wrap;
                justify-content: flex-end;
              }
              .pd-t8{
                padding-top:0px !important;
              }
              .font-size-20{
                font-size:14px !important;
              }
            }
          }
        }
        .launch-pool-box{
          padding-top:20px;
          padding-bottom:40px;
          display: block;
          .box-info-cont{
            margin-right:0;
            margin-bottom: 12px;
            flex:1;
            border-radius:12px;
            @include bg-color(bg-quaternary);
            .box-info-cont-pd{
              padding:16px;
              .info-dl{
                padding-bottom:10px;
                .dl-icon{
                  font-size:40px;
                  width:40px;
                  height:40px;
                  margin-right:12px;
                  margin-bottom:20px;
                }
                .info-dd{
                  h2{
                    font-size:20px;
                    @include color(tc-primary);
                  }
                  p{
                    margin-top:8px;
                    font-size:14px;
                    line-height:16px;
                    height:36px;
                    @include color(tc-secondary);
                  }
                }
              }
              .font-size-32 {
                font-size:20px !important;
                line-height:28px !important;
              }
              .launch-btn{
                margin-top:24px;
                height:40px;
                width:100%;
              }
              .info-text{
                padding-top:8px;
                a{
                  font-size:14px;
                  @include color(theme);
                }
              }
            }
          }
          .box-info-btn{
            padding-top:20px;
            width:100%;
            .el-button{
              width:100%;
              height:40px;
            }
          }
        }
      }
      .launch-pool-login-cont{
        padding:40px 20px;
        margin-top:10px;
        margin-bottom:40px;
        border-radius:12px;
        .font-size-24{
          font-size:18px !important;
        }
        .mg-b32{
          margin-bottom:24px !important;
        }
      }
    }
  }
}