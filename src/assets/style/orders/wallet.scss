.wallet-container{
  position: relative;
  padding:0 20px;
  min-height:500px;
  .wallet-wrapper{
    width:100%;
    .user-wrap-cont{
      padding:23px 0;
      .user-info-left{
        padding-right:40px;
        position:relative;
        &:after{
          content: '';
          display:block;
          position:absolute;
          top:50%;
          right:0;
          margin-top:-18px;
          width:1px;
          height:35px;
          @include bg-color(border);
        }
        .account-head{
          width:40px;
          height:40px;
          border-radius:6px;
          margin-right:8px;
          font-size:20px;
          text-align:center;
          line-height:40px;
          font-weight:bold;
          @include bg-color(theme);
          @include color(tc-primary);
        }
        .account-email{
          font-size:24px;
          @include color(tc-primary);
          svg{
            font-weight:normal;
            font-size:16px !important;
            margin-left:8px;
            cursor:pointer;
            @include color(tc-secondary);
          }
        }
      }
      .user-info-right{
        .item-cont{
          padding:0 40px;
          position:relative;
          &:last-child{
            &:after{
              display:none;
            }
          }
          &:after{
            content: '';
            display:block;
            position:absolute;
            top:50%;
            right:0;
            margin-top:-18px;
            width:1px;
            height:35px;
            @include bg-color(border);
          }
          .item-label{
            font-size:14px;
            @include color(tc-secondary);
            svg{
              margin-left:4px;
              font-size:14px !important;
              cursor:pointer;
            }
          }
          .item-value{
            font-size:16px;
            padding-top:4px;
          }
        }
      }
      .user-device-info{
        padding:12px 16px;
        border-radius:6px;
        border:1px solid;
        @include border-color(border);
        @include bg-color(bg-quaternary);
      }
    }
    .warn-tips-cont{
      margin-top:12px;
    }
    .wallet-assets-box{
      margin-top:20px;
      border:1px solid;
      border-radius:12px;
      overflow:hidden;
      @include border-color(border);
      &.mg-0{
        margin-top:0;
      }
      .wallet-assets{
        display:flex;
        padding:24px;
        justify-content: space-between;
        @include color(tc-primary);
        &.wallet-asset-all{
          background: linear-gradient(90deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0.02) 100%);
          .left{
            width:90%;
          }
        }
        &.wallet-asset-index{
          border-bottom:1px solid;
          @include border-color(border);
          &:last-child{
            border-bottom:0;
          }
          .left{
            width:90%;
            cursor: pointer;
            .title-cont{
              font-size:16px;
              padding-bottom:8px;
              font-weight:normal;
              @include color(tc-secondary);
            }
            .info-text{
              span{
                font-size:24px;
              }
              em{
                font-size:14px;
              }
            }
            .info-small-text{
              font-size:14px;
            }
            .info-min-text{
              font-size:14px;
              @include color(tc-secondary);
            }
          }
        }
        .left{
          .title-cont{
            font-size:18px;
            font-weight:500;
            padding-bottom:16px;
            @include color(tc-primary);
            .underline{
              text-decoration: underline;
            }
            .icon{
              width:28px;
              height:28px;
              margin-right:8px;
              background-size:100% auto;
              @include get-img('@/assets/images/common/icon_zichan-light.png', '@/assets/images/common/icon_zichan-dark.png');
            }
            svg{
              font-weight:normal;
              font-size:16px !important;
              margin-left:8px;
              cursor:pointer;
              @include color(tc-secondary);
            }
          }
          .info-text{
            padding-bottom:8px;
            span{
              font-size:28px;
              font-weight:500;
              margin-right:8px;
            }
            em{
              font-size:24px;
              font-style:inherit;
            }
          }
          .info-small-text{
            font-size:16px;
          }
          .symbol-info{
            margin-right:30px;
            .iconImg{
              width:32px;
              height:32px;
              margin-right:12px;
            }
            .info-text{
              h2{
                font-weight:500;
                font-size:18px;
                @include color(tc-primary);
              }
              p{
                font-size:14px;
                @include color(tc-secondary);
              }
            }
          }
          .info-items{
            .items{
              margin-right:12px;
              p{
                font-size:14px;
                @include color(tc-primary);
                &:first-child{
                  @include color(tc-secondary);
                }
              }
            }
          }
        }
        .right{
          display:flex;
          a{
            display:block;
            height:32px;
            margin-left:12px;
            padding:4px 12px;
            border-radius:4px;
            border:1px solid;
            font-size:14px;
            white-space: nowrap;
            @include color(theme);
            @include border-color(theme);
            &.primary{
              @include bg-color(theme);
              @include color(tc-primary);
              @include pc-hover {
                &:hover{
                  @include bg-color(theme-hover);
                }
              }
              &:active{
                @include bg-color(theme-active);
              }
            }
            @include pc-hover {
              &:hover{
                background-color: rgba(243, 199, 60, 0.1);
              }
            }
            &:active{
              background-color: rgba(243, 199, 60, 0.3);
            }
          }
        }
      }
    }
    .wallet-table-cont{
      margin-top:20px;
      border-radius:12px;
      border:1px solid;
      overflow:hidden;
      @include border-color(border);
      .wallet-table-wrap{
        .table-header{
          padding:24px;
          border-bottom:1px solid;
          @include border-color(border);
          .head-left{
            font-size:20px;
            @include color(tc-primary);
          }
          .head-right{
            .el-input{
              margin-right:12px;
              .el-input__wrapper{
                border-radius:4px !important;
                border:0;
                .el-input__inner{
                  height:36px !important;
                }
              }
            }
            .orders-select{
              margin-right:0;
              margin-top:0;
            }
          }
        }
        .table-body{
          height:700px;
          .el-table{
            .el-table__cell{
              position: static;
            }
            .cell{
              overflow: visible;
            }
            tr{
              overflow: visible;
              td{
                &.el-table__cell{
                  overflow: visible !important;
                }
              }
              td, th{
                overflow: visible;
                &:first-child{
                  padding:0 20px;
                }
                &:last-child{
                  padding:0 20px;
                }
              }
            }
          }
          .symbol-box{
            .iconImg{
              font-size:30px;
              margin-right:12px;
            }
            .symbolTxt{
              h3{
                font-weight:500;
                @include color(tc-primary);
              }
              p{
                font-size:14px;
                @include color(tc-secondary);
              }
            }
          }
          .more-box{
            position: relative;
            overflow: visible; /* 确保内容不会被裁剪 */
            .more-btn{
              font-size:28px !important;
              cursor:pointer;
            }
            .more-cont-list{
              width:136px;
              height:auto;
              position: absolute;
              left:50%;
              margin-left:-68px;
              border-radius:4px;
              border:1px solid;
              @include bg-color(bg-primary);
              @include border-color(border);
              top: 100%; /* 显示在 more-box 的下方 */
              left: 0;
              z-index: 9999; /* 确保在最上层 */
              min-width: 120px; /* 根据内容调整宽度 */
              background-color: white; /* 确保背景色不透明 */
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影以突出显示 */
              li{
                text-align:center;
                height:28px;
                line-height:28px;
                cursor: pointer;
                font-size:14px;
                @include color(tc-primary);
                &.disabled{
                  opacity:0.5;
                  cursor: default;
                  &:hover{
                    @include color(tc-primary);
                  }
                }
                &:hover{
                  @include bg-color(bg-quaternary);
                  @include color(theme);
                }
              }
            }
          }
        }
        .trade-title{
          padding:24px;
          font-size:20px;
          @include color(tc-primary);
        }
        .trade-cont{
          padding:0 4px 4px 24px;
          ul{
            display:flex;
            flex-wrap:wrap;
            justify-content: space-between;
            align-items: flex-start;
            li{
              cursor: pointer;
              flex:1;
              height:96px;
              display:flex;
              flex-direction: column;
              border:1px solid;
              border-radius:12px;
              margin-right:20px;
              margin-bottom:20px;
              padding:16px;
              justify-content: space-between;
              @include border-color(border);
              h3{
                font-size:18px;
                font-weight:500;
                @include color(tc-primary);
                .tag-icon{
                  display:block;
                  font-size:12px;
                  margin-left:8px;
                  padding:2px 8px;
                  border-radius:2px;
                  @include bg-color(bg-quaternary);
                  @include color(tc-primary);
                }
              }
              p{
                font-size:14px;
                display: flex;
                justify-content: space-between;
                span{
                  @include color(tc-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}
.el-dropdown__popper{
  &.caozuo-box{
    .el-dropdown-menu{
      .el-dropdown-menu__item{
        display: flex;
        justify-content: center;
        @include pc-hover {
          &:hover{
            a{
              @include color(theme);
            }
          }
        }
      }
    }
  }
}
@include md{
  .wallet-container{
    .wallet-wrapper{
      .user-wrap-cont{
        .user-info-left{
          padding-right:24px;
          .account-email{
            font-size:18px;
          }
        }
        .user-info-right{
          .item-cont{
            padding:0 24px;
          }
        }
      }
      .wallet-assets-box{
        .wallet-assets{
          .left{
            .info-text{
              span{
                font-size:28px;
              }
              em{
                font-size:20px;
              }
            }
          }
        }
      }
    }
  }
}
@include mb {
  .wallet-container{
    .wallet-wrapper{
      .user-wrap-cont{
        padding:20px 0 8px;
        flex-direction: column;
        .user-info-left{
          padding-right:0;
          position:relative;
          width:100%;
          &:after{
            display:none;
          }
          .account-head{
            width:40px;
            height:40px;
            border-radius:6px;
            margin-right:8px;
            font-size:20px;
            text-align:center;
            line-height:40px;
            font-weight:bold;
            @include bg-color(theme);
            @include color(tc-primary);
          }
          .account-email{
            font-size:24px;
            @include color(tc-primary);
            svg{
              font-weight:normal;
              font-size:16px !important;
              margin-left:8px;
              cursor:pointer;
              @include color(tc-secondary);
            }
          }
        }
        .user-info-right{
          width:100%;
          padding-top:20px;
          &.flex-box{
            display:block;
          }
          .item-cont{
            flex:1;
            padding:0;
            position:relative;
            display:flex;
            align-items:center;
            justify-content: space-between;
            .item-value{
              font-size:14px;
            }
          }
        }
        .user-device-info{
          margin-top:20px;
          flex:1;
          width:100%;
          padding:8px 12px;
          border-radius:6px;
          border:1px solid;
          justify-content: space-between;
          @include border-color(border);
          @include bg-color(bg-quaternary);
        }
      }
      .warn-tips-cont{
        margin-top:8px;
      }
      .wallet-assets-box{
        margin-bottom:20px;
        &.mg-0{
          margin-top:20px;
        }
        .wallet-assets{
          padding:16px;
          flex-direction: column;
          position: relative;
          &.wallet-asset-all{
            .left{
              width:100%;
              align-items: flex-start;
            }
            .right{
              padding-top:14px;
              display:flex;
              a{
                flex:1;
                text-align:center;
                margin-left:0;
                margin-right:8px;
                &:last-child{
                  margin-right:0;
                }
              }
              .el-button{
                flex:1;
              }
            }
          }
          &.wallet-asset-index{
            .left{
              .title-cont{
                font-size:16px;
                padding-bottom:8px;
                font-weight:normal;
                @include color(tc-secondary);
              }
              .info-text{
                span{
                  font-size:16px;
                }
                em{
                  font-size:12px;
                }
              }
              .info-small-text{
                font-size:12px;
              }
              .info-min-text{
                font-size:12px;
                padding-top:8px;
                @include color(tc-secondary);
              }
            }
          }
          .left{
            width:100%;
            &.flex-box{
              flex-direction: column;
            }
            .title-cont{
              font-size:20px;
              font-weight:500;
              padding-bottom:16px;
              @include color(tc-primary);
              .icon{
                width:28px;
                height:28px;
                margin-right:8px;
                background-size:100% auto;
                @include get-img('@/assets/images/common/icon_zichan-light.png', '@/assets/images/common/icon_zichan-dark.png');
              }
              svg{
                font-weight:normal;
                font-size:16px !important;
                margin-left:8px;
                cursor:pointer;
                @include color(tc-secondary);
              }
            }
            .info-text{
              padding-bottom:8px;
              span{
                font-size:24px;
                font-weight:500;
                margin-right:8px;
              }
              em{
                font-size:14px;
                font-style:inherit;
              }
            }
            .info-small-text{
              font-size:16px;
            }
            .symbol-info{
              margin-right:0;
              width:100%;
            }
            .info-items{
              width:100%;
              flex-wrap: wrap;
              align-items: flex-start;
              justify-content: flex-start;
              .items{
                flex:1;
                width:50%;
                max-width: 50%;
                min-width: 50%;
                margin-right:0;
                margin-bottom:12px;
                p{
                  font-size:14px;
                  @include color(tc-primary);
                  &:first-child{
                    @include color(tc-secondary);
                  }
                }
              }
            }
          }
          .right{
            padding-top:14px;
            .coin-btn{
              position: absolute;
              top:24px;
              right:20px;
            }
            a{
              flex:1;
              text-align:center;
              margin-left:0;
              margin-right:8px;
              &:last-child{
                margin-right:0;
              }
            }
            .el-button{
              flex:1;
            }
          }
        }
      }
      .wallet-table-cont{
        margin-top:0;
        border-radius:0;
        border:0;
        .wallet-table-wrap{
          .table-header{
            padding:0 0 12px;
            border-bottom:1px solid;
            @include border-color(border);
            &.flex-box{
              align-items: flex-start;
            }
            .head-left{
              font-size:16px;
              line-height:36px;
              &.none-text{
                display:none;
              }
            }
            .head-right{
              &.none-text{
                width:100%;
                .el-input{
                  margin-right:12px;
                }
              }
              &.flex-box{
              }
              .el-input{
                margin-right:0;
              }
              .el-checkbox{
                margin-top:4px;
              }
              .orders-select{
                margin-right:0;
                margin-top:0;
              }
            }
          }
          .table-body{
            height:auto;
            .el-table{
              .cell{
                padding:0;
              }
              tr{
                td, th{
                  &:first-child{
                    padding:0px;
                  }
                  &:last-child{
                    padding:0px;
                  }
                }
              }
            }
            .symbol-box{
              .iconImg{
                font-size:30px;
                margin-right:12px;
              }
              .symbolTxt{
                h3{
                  font-weight:500;
                  @include color(tc-primary);
                }
                p{
                  font-size:14px;
                  @include color(tc-secondary);
                }
              }
            }
            .more-box{
              .more-cont-list{
                width:98px;
              }
            }
          }
          .trade-title{
            padding:0 0 12px;
            font-size:16px;
            @include color(tc-primary);
          }
          .trade-cont{
            padding:0;
            margin-right:-20px;
            ul{
              li{
                height:105px;
                h3{
                  font-size:16px;
                }
                p{
                  font-size:14px;
                }
              }
            }
          }
        }
      }
    }
  }
}