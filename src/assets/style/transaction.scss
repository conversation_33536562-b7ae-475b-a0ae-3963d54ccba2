@media (min-width: 740px) and (max-width: 1220px) {
  .transaction-container {
    min-height: calc(100vh - 74px);
    margin: 12px 16px 0 16px;
  }

  .transaction-content {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }

  .left-title-box {
    display: none;
  }

  .supported-currencies-box{
    margin-top: 12px;
    display: grid;
    grid-template-columns: repeat(4, 1fr)!important;
    gap: 8px;
  }

  .transaction-content-right::before {
    content: '';
    display: block;
    width: 100%;
  }

  .trading-title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-bottom: 20px;
    width: 100%;
  }

  .trading-main-title {
    font-size: 28px;
    color: #414655;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;

    .dark & {
      color: #fff;
    }
  }

  .trading-sub-title {
    font-size: 14px;
    color: #414655;
    text-align: center;
    margin-bottom: 8px;

    .dark & {
      color: #fff;
    }
  }
  .transaction-content-left-bg{
    margin: 0 auto;
  }
}

@media (min-width: 1221px) and (max-width: 10000px) {
  .transaction-container {
    max-width: 1400px;
    margin: 12px auto 0 auto;
    min-height: calc(100vh - 74px);
    padding: 0 16px;
    box-sizing: border-box;
  }

  .trading-title-container {
    display: none;
  }
}

.modal-box {
  width: 100%;
  height: 700px;
  background-color: rgba(0, 0, 0, .5);
  position: absolute;
  z-index: 10000;
  bottom: 0;
  left: 0;
  border-radius: 20px;
}

.no-payment {
  width: 400px;
  color: #e62e2e;
  font-size: 13px;
  margin-top: 10px;
  margin-left: 50px;
}

.no-payment-v1 {
  color: #e62e2e;
  font-size: 13px;
  padding-top: 5px;
  padding-bottom: 10px;
}

.no-payment-mobile {
  width: 400px;
  color: #e62e2e;
  font-size: 13px;
  margin-top: 10px;
  margin-left: 20px;
}

.modal-box-content {
  width: 100%;
  height: 640px;
  background-color: #fff;
  position: absolute;
  z-index: 10000;
  bottom: 0;
  left: 0;
  border-radius: 0 0 20px 20px;
  animation: smoothAccordionUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
  transform-origin: bottom center;
  border: 1px solid #EEEEEE;

  .dark & {
    background-color: #26282C;
    border: 1px solid transparent;
  }
}

@keyframes smoothAccordionUp {
  0% {
    transform: translateY(50px) scaleY(0.1);
    opacity: 0;
  }

  100% {
    transform: translateY(0%) scaleY(1);
    opacity: 1;
  }
}

.modal-box-content-list {
  height: 520px;
  border-radius: 0 0 20px 20px;
  overflow-y: auto;
  padding-top: 20px;
  box-sizing: border-box;
  cursor: pointer;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border: none;
  }

  &::-webkit-scrollbar-thumb {
    background: #DDD;

    .dark & {
      background: #414655 !important;
    }
  }

  .dark & {
    &::-webkit-scrollbar-track {
      background: transparent;
      border: none;
    }

    &::-webkit-scrollbar-thumb {
      background: #DDD;

      .dark & {
        background: #414655 !important;
      }
    }
  }
}

.modal-box-content-item {
  display: flex;
  align-items: center;
  padding-left: 24px;
  padding-right: 24px;
  box-sizing: border-box;
  line-height: 28px;
}

.modal-box-content-item:hover {
  background-color: #F9F9F9;

  .dark & {
    background-color: #363A45;
  }
}

.no-results {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}

.modal-box-content-item-text {
  margin-left: 10px;
  padding: 10px 0;
  box-sizing: border-box;
}

.modal-box-content-item-text-v1 {
  display: flex;
  align-items: center;
  margin-left: 10px;
  padding: 17px 0;
  box-sizing: border-box;
}

.modal-box-content-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  margin-right: 16px;
  position: relative;
}

.modal-box-content-header-title {
  font-size: 16px;
  color: #414655;

  .dark & {
    color: #fff;
    font-size: 16px;
  }
}

.close-icon {
  display: block;
  color: #414655;
  font-size: 24px;
  cursor: pointer;
  position: absolute;
  right: 0;

  .dark & {
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    position: absolute;
    right: 0;
  }
}

.modal-box-content-input {
  display: flex;
  width: 90%;
  margin: 0 auto;
  margin-top: 26px;
}

.el-dropdown-menu__item {
  justify-content: center;
}

.transaction-title-box {
  display: flex;
  justify-content: end;
  cursor: pointer;


  .order-box {
    display: flex;
    align-items: center;
  }

  .title-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin-right: 8px;
  }

  .title {
    font-size: 16px;
    color: #414655;
    position: relative;

    .dark & {
      color: #fff;
    }
  }

  .tips-box {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #FF6262;
    position: absolute;
    bottom: 0;
    right: -5px;
  }
}

.transaction-content {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.left-title {
  width: 550px;
  font-size: 40px;
  color: #414655;
  font-weight: 600;
  margin-bottom: 12px;

  .dark & {
    color: #fff;
  }
}

.left-info {
  font-size: 14px;
  color: #414655;

  .dark & {
    color: #fff;
  }
}


.support-box {
  margin-top: 40px;
  margin-bottom: 40px;
}

.support-title {
  font-size: 18px;
  font-weight: 600;
  color: #414655;

  .dark & {
    color: #fff;
  }
}

.supported-currencies-box {
  margin-top: 12px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.item-supported-currencies {
  width: 170px;
  height: 44px;
  background-color: #F9F9F9;
  border-radius: 8px;
  display: flex;
  align-items: center;

  .dark & {
    background-color: #25282F;
  }
}

.item-currencies-icon {
  width: 20px;
  height: 20px;
  display: block;
  margin-left: 12px;
}

.item-currencies-label {
  font-size: 16px;
  font-weight: bold;
  color: #414655;
  margin-left: 7px;
  margin-right: 2px;

  .dark & {
    color: #fff;
  }
}

.item-currencies-value {
  font-size: 12px;
  font-weight: 500;
  color: #9497A0;
  margin-left: 4px;
}

.transaction-content-left-bg {
  width: 484px;
  height: 700px;
  background: url('@/assets/images/transaction/navbar-bg-left.png') no-repeat top center;
  background-size: 100%, 100%;
  position: relative;

  .dark & {
    width: 484px;
    height: 700px;
    background: url('@/assets/images/transaction/navbar-bg-left-dark.png') no-repeat top center;
    background-size: 100%, 100%;
    position: relative;
  }
}

.transaction-content-right-bg {
  width: 484px;
  height: 700px;
  background: url('@/assets/images/transaction/navbar-bg-right.png') no-repeat top center;
  background-size: 100%, 100%;
  position: relative;

  .dark & {
    width: 484px;
    height: 700px;
    background: url('@/assets/images/transaction/navbar-bg-left-dark.png') no-repeat top center;
    background-size: 100%, 100%;
    position: relative;
  }
}

.support-right-title {
  font-size: 24px;
  font-weight: 600;
  color: #414655;
  padding-top: 32px;
  margin-left: 28px;
  box-sizing: border-box;
}

.support-right-box {
  width: 428px;
  min-height: 92px;
  border-radius: 20px;
  border: 1px solid #EEEEEE;
  margin: 50px auto 0 auto;

  .dark & {
    border: 1px solid #363A45;
  }
}

.support-right-box_v1 {
  width: 428px;
  height: 92px;
  border-radius: 20px;
  border: 1px solid #EEEEEE;
  margin: 16px auto 0 auto;
  cursor: pointer;

  .dark & {
    border: 1px solid #363A45;
  }
}

.payment-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 10px;
}

.transaction-right-payment-text {
  font-size: 14px;
  color: #414655;
  font-weight: 500;
  padding-top: 16px;
  margin-left: 20px;
  box-sizing: border-box;

  .dark & {
    color: #fff;
  }
}

.input-style {
  border: none !important;
  font-size: 24px;
  font-weight: 500;
  padding: 0;
  color: #414655;
  line-height: 25px;

  &:disabled {
    background-color: transparent !important;
    opacity: 1;

    .dark & {
      background-color: transparent !important;
    }
  }

  .dark & {
    background-color: transparent;
    color: #fff;
  }

  &::placeholder {
    font-size: 24px !important;
    color: #CBCED8 !important;
    font-weight: 500;

    .dark & {
      color: #4D5362 !important;
    }
  }
}

.trade {
  width: 40%;
  font-size: 24px;
  font-weight: 600;
  color: #414655;
  cursor: pointer;
  text-align: center;
  background-color: transparent;

  .dark & {
    color: #fff;
  }
}

.isTrade {
  width: 40%;
  font-size: 24px;
  font-weight: 400;
  color: #9497A0;
  cursor: pointer;
  text-align: center;
  background-color: transparent;
}

.el-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.active-icon {
  margin-left: 8px;
  color: #9497A0;
}

.position::before {
  content: '';
  padding: 0 20px;
}

.active-icon-v1 {
  margin-left: 4px;
  color: #9497A0;
}

.active-text {
  font-weight: bold;
  font-size: 16px;
  color: #414655;
  margin-left: 4px;

  .dark & {
    color: #fff;
  }
}

.navbar-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-top: 10px;
  box-sizing: border-box;
}

.footer-bg {
  width: 100%;
  height: auto;
  max-height: 350px;
  object-fit: contain;
  display: block;
  margin-top: 80px;
  margin-bottom: 106px;
}

.channel-box {
  display: flex;
  align-items: center;
}

.channel-icon {
  width: 28px;
  height: 28px;
  display: block;
  margin-right: 12px;
}

.estimated-price {
  margin-left: 28px;
  font-size: 14px;
  color: #9497A0;
  font-weight: 400;
  margin-top: 26px;

  span {
    font-size: 14px;
    color: #414655;
    font-weight: 500;

    .dark & {
      color: #fff;
    }
  }
}

.technical-support {
  margin-top: 12px;
  margin-left: 28px;
  font-size: 14px;
  color: #9497A0;
  font-weight: 400;

  a {
    color: #F0B90B;
  }
}

.submit-btn {
  width: 428px;
  height: 56px;
  line-height: 53px;
  margin: 12px auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  color: #414655;
  // background: #f0b90b;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.submit-btn-disabled {
  width: 428px;
  height: 56px;
  line-height: 53px;
  margin: 12px auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  color: #414655;
  // background: #25282F;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.popover-box {
  width: 428px;
  background: #FDFDFD;
  border-bottom: 1px solid #EEEEEE;
  display: none;
}

.item-support-right-icon {
  width: 20px !important;
  height: 20px !important;
  display: block;
  margin-right: 4px;
}

.item-support-flex {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-support-right-icon_v1 {
  width: 28px !important;
  height: 28px !important;
  display: block;
}

.item-channel-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-left: 25px;
  padding-right: 24px;
  box-sizing: border-box;
  height: 65px;
}

.item-channel-list-v1 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-channel-list:hover {
  background-color: #F9F9F9;

  .dark & {
    background-color: #363A45;
  }
}

.item-channel-title {
  display: flex;
  align-items: center;
}

.item-channel-text {
  margin-left: 12px;
}

.quotation {
  font-size: 12px;
  color: #414655;
  font-weight: 400;
  margin-left: 4px;

  .dark & {
    color: #9497A0;
  }
}

.receive-text {
  font-size: 12px;
  color: #9497A0;
  font-weight: 400;
  text-align: right;
}

.price-text {
  font-size: 14px;
  color: #414655;
  font-weight: 500;
  text-align: right;

  .dark & {
    color: #fff;
  }
}

.exchange-rate {
  color: #FF6262;
  font-size: 12px;
  font-weight: 500;
}

.item-label-title {
  font-size: 18px;
  color: #414655;
  font-weight: bold;

  .dark & {
    color: #fff;
  }
}

.quotation-text {
  font-size: 14px;
  color: #9497A0;
  font-weight: 500;
  margin-left: 5px;
}

.dialog-footer {
  display: flex;
  align-items: center;
  padding-bottom: 15px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  margin-top: 24px;
}

.item-dialog {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.border-box {
  border-bottom: 1px solid #EEEEEE;
  margin-bottom: 16px;

  .dark & {
    border-bottom: 1px solid #9497A0;
  }
}

.buy-text {
  font-size: 14px;
  font-weight: 400;
  color: #9497A0;
}

.buy-text-info {
  font-size: 12px;
  font-weight: 400;
  color: #9497A0;
}

.amount-text {
  font-size: 16px;
  font-weight: 500;
  color: #414655;

  .dark & {
    color: #fff;
  }
}

.amount-text-num {
  font-size: 20px;
  font-weight: bold;
  color: #414655;

  .dark & {
    color: #fff;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #414655;

    .dark & {
      color: #fff;
    }
  }
}

.buy-footer-box {
  margin-bottom: 10px;
}

.dialog-content-title {
  width: 100%;
  height: 44px;
  border-radius: 6px;
  background-color: #F6F6F6;
  display: flex;
  align-items: center;

  .dark & {
    background-color: #26282C;
  }
}

.secure-icon {
  width: 16px;
  height: 16px;
  display: block;
  margin-left: 20px;
}

.secure-text {
  font-size: 14px;
  font-weight: 400;
  color: #414655;
  margin-left: 4px;

  .dark & {
    color: #fff;
  }
}

.dialog-box-title {
  margin-top: 16px;
  font-size: 18px;
  font-weight: 500;
  color: #414655;

  .dark & {
    color: #fff;
  }

  span {
    color: #F0B90B;
  }
}

.dialog-box-text {
  margin-top: 16px;
  font-size: 12px;
  color: #9497A0;
  font-weight: 400;
}

.transaction-bg {
  width: 162px;
  height: 110px;
  display: block;
  margin: 16px auto 0 auto;
}

.footer-content {
  margin-top: 80px;
  width: 100%;
  height: 275px;
  border: 1px solid #EEEEEE;
  border-radius: 20px;
  margin-bottom: 106px;

  .dark & {
    border: 1px solid #363A45;
  }
}

.footer-content-mobile {
  margin-top: 24px;
  width: 100%;
  height: 432px;
  border: 1px solid #EEEEEE;
  border-radius: 20px;
  margin-bottom: 70px;

  .dark & {
    border: 1px solid #363A45;
  }
}

.footer-content-title {
  font-size: 24px;
  font-weight: 600;
  color: #414655;
  margin-left: 40px;
  padding-top: 40px;
  box-sizing: border-box;

  .dark & {
    color: #fff;
  }
}

.footer-content-title-mobile {
  font-size: 18px;
  font-weight: 600;
  color: #414655;
  margin-left: 18px;
  padding-top: 24px;
  box-sizing: border-box;

  .dark & {
    color: #fff;
  }
}

.footer-content-box-item {
  margin-left: -20px;
}

.footer-content-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-top: 40px;
  margin-left: 40px;
}

.footer-content-box-mobile {
  display: flex;
  flex-direction: column;
}

.footer-content-box-title {
  margin-top: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #414655;

  .dark & {
    color: #fff;
  }
}

.footer-content-box-title-mobile {
  margin-top: 12px;
  margin-left: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #414655;

  .dark & {
    color: #fff;
  }
}

.footer-content-box-info {
  font-size: 14px;
  font-weight: 400;
  color: #9497A0;
  margin-top: 4px;
}

.footer-content-box-info-mobile {
  font-size: 12px;
  font-weight: 400;
  color: #9497A0;
  margin-top: 4px;
  margin-left: 16px;
}

.footer-content-icon {
  width: 60px;
  height: 60px;
  display: block;
}

.footer-content-icon-mobile {
  width: 40px;
  height: 40px;
  display: block;
  margin-top: 20px;
  margin-left: 16px;
}

// 移动端

/* 在移动端隐藏买入卖出盒子上的文案 */
@media (max-width: 739px) {
  .trading-title-container {
    display: none;
  }
}

.transaction-container-mobile {
  width: 95%;
  margin: 0 auto;
  min-height: calc(100vh - 74px);
  margin-top: 15px;
}

.transaction-content-mobile {
  margin-top: 24px;
  display: flex;
  flex-direction: column-reverse;
}

.left-title-mobile-box {
  margin-bottom: 24px;
}

.left-title-mobile {
  width: 270px;
  margin: 0 auto;
  font-size: 20px;
  color: #414655;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;

  .dark & {
    color: #fff;
  }
}

.left-info-mobile {
  margin: 0 auto;
  font-size: 12px;
  color: #414655;
  text-align: center;
  font-weight: 400;

  .dark & {
    color: #fff;
  }
}

.transaction-content-left-bg-mobile {
  width: 100%;
  height: 534px;
  background: url('@/assets/images/transaction/navbar-bg-left.png') no-repeat top center;
  background-size: 100% 100%;

  .dark & {
    width: 100%;
    height: 534px;
    background: url('@/assets/images/transaction/navbar-bg-left-dark.png') no-repeat top center;
    background-size: 100% 100%;
  }
}

.transaction-content-right-bg-mobile {
  width: 100%;
  height: 534px;
  background: url('@/assets/images/transaction/navbar-bg-right.png') no-repeat top center;
  background-size: 100% 100%;

  .dark & {
    width: 100%;
    height: 534px;
    background: url('@/assets/images/transaction/navbar-bg-left-dark.png') no-repeat top center;
    background-size: 100% 100%;
  }
}

.trade-mobile {
  width: 40%;
  height: 35px;
  padding-top: 6px;
  font-size: 18px;
  font-weight: 600;
  color: #414655;
  text-align: center;
  box-sizing: border-box;
  background-color: transparent;

  .dark & {
    color: #fff;
  }
}

.isTrade-mobile {
  width: 40%;
  height: 35px;
  padding-top: 6px;
  font-size: 18px;
  font-weight: 400;
  color: #9497A0;
  text-align: center;
  box-sizing: border-box;
  background-color: transparent;
}

.support-right-box-mobile {
  width: 95%;
  min-height: 74px;
  border-radius: 12px;
  border: 1px solid #EEEEEE;
  margin: 24px auto 0 auto;

  .dark & {
    border: 1px solid #363A45;
  }
}

.support-right-box_v1-mobile {
  width: 95%;
  height: 74px;
  border-radius: 12px;
  border: 1px solid #EEEEEE;
  margin: 12px auto 0 auto;

  .dark & {
    border: 1px solid #363A45;
  }
}

.transaction-right-payment-text-mobile {
  font-size: 12px;
  color: #414655;
  font-weight: 500;
  padding-top: 12px;
  margin-left: 12px;
  box-sizing: border-box;

  .dark & {
    color: #fff;
  }
}

.payment-box-mobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 12px;
  margin-right: 15px;
  margin-top: 7px;
}

.input-style-mobile {
  border: none !important;
  font-size: 16px;
  font-weight: 500;
  padding: 0;
  color: #414655;

  .dark & {
    background-color: transparent;
    color: #fff;
  }

  &::placeholder {
    font-size: 16px !important;
    color: #CBCED8 !important;
    font-weight: 500;

    .dark & {
      color: #4D5362 !important;
    }
  }
}

.channel-icon-mobile {
  width: 20px;
  height: 20px;
  display: block;
  margin-right: 12px;
}

.item-label-title-mobile {
  font-size: 16px;
  color: #414655;
  font-weight: bold;

  .dark & {
    color: #fff;
  }
}

.active-text-mobile {
  font-weight: bold;
  font-size: 14px;
  color: #414655;
  margin-left: 4px;

  .dark & {
    color: #fff;
  }
}

.estimated-price-mobile {
  margin-left: 12px;
  font-size: 14px;
  color: #9497A0;
  font-weight: 500;
  margin-top: 16px;

  span {
    font-size: 14px;
    color: #414655;

    .dark & {
      color: #fff;
    }
  }
}

.submit-btn-mobile {
  width: 95%;
  height: 40px;
  margin: 12px auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #414655;
}

.submit-btn-mobile-disabled {
  width: 95%;
  height: 40px;
  margin: 12px auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #414655;
}

.technical-support-mobile {
  margin-top: 7px;
  margin-left: 12px;
  font-size: 14px;
  color: #9497A0;
  font-weight: 400;

  a {
    color: #F0B90B;
  }
}

.supported-currencies-box-mobile {
  margin-top: 12px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.item-supported-currencies-mobile {
  width: 100%;
  height: 44px;
  background-color: #F9F9F9;
  border-radius: 8px;
  display: flex;
  align-items: center;

  .dark & {
    background-color: #25282F;
  }
}

.item-currencies-label-mobile {
  font-size: 14px;
  font-weight: bold;
  color: #414655;
  margin-left: 8px;

  .dark & {
    color: #fff;
  }
}

.item-currencies-value-mobile {
  font-size: 12px;
  font-weight: 500;
  color: #9497A0;
  margin-left: 2px;
}

.support-box-mobile {
  margin-top: 24px;
}

.footer-bg-mobile {
  width: 100%;
  height: auto;
  display: block;
  margin-top: 24px;
  margin-bottom: 24px;
}

.navbar-box-mobile {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-top: 3px;
  box-sizing: border-box;
}

.content-mobile {
  padding: 0px 16px 30px 16px;
  height: 400px;
  overflow-y: auto;

  .dark & {
    background-color: #181A1F;
  }
}

.content-mobile-v2 {
  padding: 15px 16px 30px 16px;

  .dark & {
    background-color: #181A1F;
  }
}

// 弹窗高度
.content-mobile-v1 {
  padding: 0 16px 30px 16px;
  height: 400px;
  overflow-y: auto;

  .dark & {
    background-color: #181A1F;
  }
}

.dialog-text-mobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dialog-text-mobile-flex {
  display: flex;
  align-items: center;
}

.dialog-text-mobile-column {
  display: flex;
  flex-direction: column;
}

.item-dialog-mobile {
  display: flex;
  margin-bottom: 24px;
}

.action-sheet-icon-mobile {
  width: 24px;
  height: 24px;
  display: block;
  margin-right: 12px;
}

.dialog-content-mobile {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0 25px;
}

.dialog-content-mobile-v1 {
  display: flex;
  flex-direction: column;
}

.submit-mobile {
  width: 47%;
  height: 40px;
  margin: 0 auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #414655;

  .dark & {
    color: #f0b90b;
  }
}

.submit-mobile-v1 {
  width: 100%;
  height: 40px;
  margin: 24px auto 0 auto;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #414655;

  .dark & {
    color: #9497A0;
  }
}

.dialog-footer-mobile {
  display: flex;
  align-items: center;
  margin-top: 18px;
  padding-bottom: 20px;
}

:deep(.van-action-sheet__header) {
  text-align: left !important;
  padding-left: 16px !important;
  box-sizing: border-box;

  .dark & {
    background: #181A1F !important;
  }
}

.input-style-mobile-pop {
  margin-top: 40px;
  margin-bottom: 10px;
}

.input-style-mobile-pop-v1 {
  margin-bottom: 10px;
}
.coming-soon-box{
  position: relative;
}
.coming-soon-icon{
  position: absolute;
  top: 0;
  width: max-content;
  min-width: 60px;
  height: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('./../images/transaction/coming-soon.png') no-repeat center center;
  background-size: 100% 100%;
  font-size: 12px;
  color: #fff;
  padding: 0 8px 0 15px;
  box-sizing: border-box;
  white-space: nowrap;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.1s ease, left 0.1s ease;
}

.coming-soon-icon.loaded {
  opacity: 1;
}

/* 针对中文"卖出"的定位 */
[lang="zh"] .coming-soon-icon,
[lang="zh-CN"] .coming-soon-icon {
  left: calc(50% + 30px); /* "卖出"宽度约36px，所以50% + 18px + 8px间距 */
}

/* 针对英文"Sell"的定位 */
[lang="en"] .coming-soon-icon {
  left: calc(50% + 30px); /* "Sell"宽度约28px，所以50% + 14px + 8px间距 */
}

/* 针对其他语言的默认定位 */
[lang="ko"] .coming-soon-icon,
[lang="ja"] .coming-soon-icon,
[lang="zh-Hant"] .coming-soon-icon {
  left: calc(50% + 30px);
}
.coming-soon-v1{
  display: block;
}
