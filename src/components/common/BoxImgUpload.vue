<template>
  <el-upload
    class="upload-icon-cont"
    :show-file-list="false"
    :http-request="upload"
    action>
    <div v-if="status === 0 || status === 1 || status === 3" class="upload-cont-img flex-box flex-column">
      <img src="~/assets/images/common/upload-icon.png" />
      <p v-if="status === 0" class="fit-tc-primary font-size-14">{{ $t('将图片拖到此处，') }}{{ $t('或') }}<span class="fit-theme">{{ $t('点击上传') }}</span></p>
      <p v-if="status === 1" class="fit-tc-secondary font-size-14">{{ $t('上传中...') }}</p>
      <p v-if="status === 3" class="fit-warn font-size-14">{{ $t('上传失败, 请重新上传') }}</p>
    </div>
    <div v-else-if="status === 2" class="upload-cont-img flex-box flex-column">
      <img :src="getImgUrl(imgUrl)" class="img-cont-input" />
    </div>
  </el-upload>
</template>
<script lang="ts" setup>
  import { ElUpload } from 'element-plus'
  import { UploadFile } from '~/utils/UploadFile'
  import { getDomainF } from '~/utils'
  import { imgDmain } from '~/config'
  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const status = ref(0) // 未上传:0; 上传中:1; 上传成功:2; 上传失败:3;
  const getImgUrl = (url) => {
    return `${getDomainF().includes('tonetou') || getDomainF().includes('ktx.one') ? 'https://res-test.tonetou.com' : imgDmain}${url}`
  }
  const imgUrl = ref('')
  const upload = (fileData) => {
    status.value = 1
    const upload = new UploadFile({
      purpose: 'resource'
    }, fileData, getDomainF().includes('tonetou') ? `https://ma-tapi${getDomainF()}/v1/upload/uploadfront` : getDomainF().includes('ktx.one') ? `https://tapi${getDomainF()}/v1/upload/uploadfront` : `https://api${getDomainF()}/v1/upload/uploadfront`)
    upload.upload((res, name) => {
      const photoAfter = name ? name.split('.').pop() : 'png'
      const before = res.startsWith
      const path = `${before}ktx_instruction_${Date.now()}.${photoAfter}`
      return path
    }, true).then((res) => {
      status.value = 2
      emit('update:modelValue', res)
      imgUrl.value = res
      console.log(status.value, res, 'djueijdijeijdijiejij')
      const reader = new FileReader()
      reader.readAsDataURL(fileData.file)
    }).catch((err, imgUrl) => {
      status.value = 3
      setTimeout(() => {
        status.value = 0
      }, 5000)
      console.log(err)
    })
  }
  watch(() => props.modelValue, (val) => {
    if (!val) {
      status.value = 0
    }
  })
</script>
<style lang="scss">
  .upload-icon-cont{
    width:100%;
    border:1px solid;
    border-radius:4px;
    height:150px;
    overflow:hidden;
    @include border-color(border);
    .el-upload{
      width:100%;
      height:150px;
      .upload-cont-img{
        img{
          width:80px;
          height:auto;
        }
      }
      p{
        margin-top:10px;
        padding:0 24px;
      }
      .img-cont-input{
        height:auto !important;
        width:100% !important;
      }
    }
  }
</style>