<template>
  <el-dialog v-model="visible" :title="$t('修改时区')" width="480px">
    <el-select v-model="curTimeZone">
      <el-option v-for="(item, index) in timeZoneList" :key="item" :value="item.value" :label="item.label">{{ item.label }}</el-option>
    </el-select>
    <el-button type="primary" :disabled="curTimeZone === ''" class="mg-t24" @click="changeTimeZone()">{{ $t('确认') }}</el-button>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElSelect, ElOption, ElButton } from 'element-plus'
  import { editProfileAPI } from '~/api/user.ts'
  import { TIME_ZONE_MAP } from '~/config'
  import { commonStore } from '~/stores/commonStore'
  const props = defineProps({
    isShow: {
      type: Boolean,
      default: false
    },
    defaultTimeZone: {
      type: String,
      default: ''
    },
    useCommon: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'request'])
  const visible = ref(false)
  const timeZoneList = computed(() => {
    return Object.values(TIME_ZONE_MAP)
  })
  const curTimeZone = ref('')
  const curItem = ref({})
  watch(() => props.defaultTimeZone, (val) => {
    curTimeZone.value = val
    curItem.value = TIME_ZONE_MAP[val]
  })
  watch(() => curTimeZone.value, (val) => {
    curItem.value = TIME_ZONE_MAP[val]
  })
  const changeTimeZone = async() => {
    if (curTimeZone.value === '') {
      return false
    }
    const { data, error } = await editProfileAPI({
      utc_zone: curItem.value.num,
      utc_area: curTimeZone.value
    })
    if (data) {
      localStorage.setItem('KTXutcZone', curItem.value.num)
      emit('close')
      emit('request')
    } else {
      props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
    }
  }
  onMounted(() => {
    visible.value = props.isShow
    curTimeZone.value = props.defaultTimeZone
    curItem.value = TIME_ZONE_MAP[props.defaultTimeZone]
  })
</script>
<style lang="scss" scoped>
</style>