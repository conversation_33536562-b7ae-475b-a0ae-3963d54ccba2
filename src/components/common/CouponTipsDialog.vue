<template>
  <el-dialog v-model="isShow" :title="$t('重要提示')" width="480px" @close="emit('close')">
    {{ $t('目前您有 {amount} USDT 的 {couponTxt}，{couponTxt}未消耗完之前，资产转出会导致{couponTxt}清零', {
      amount: format(curCouponAssets.total, 4, true),
      couponTxt: $t(CouponTxtObj[curCouponAssets.asset]),
    }) }}
    <div class="flex-box mg-t24">
      <el-button type="primary" @click="emit('request')">{{ $t('我已知晓') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElButton } from 'element-plus'
  import { commonStore } from '~/stores/commonStore'
  const store = commonStore()
  const { tradeAssetObj } = storeToRefs(store)
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close', 'request'])
  const isShow = ref(false)
  const CouponTxtObj = ref({
    'KtxQ1': '体验金',
    'KtxQ2': '抵扣金'
  })
  const curCouponAssets = computed(() => {
    return Object.values(tradeAssetObj.value).filter((item) => {
      return (item.asset === 'KtxQ1' || item.asset === 'KtxQ2') && item.total * 1 > 0
    })[0]
  })
  onMounted(() => {
    isShow.value = props.visible
  })
</script>