<template>
  <div class="original-kline-wrap">
    <div
      v-if="isShowTool"
      class="line-tool"
      >
      <el-scrollbar
        class="line-tool-scroll-wrap"
        view-class="flex-box flex-column"
        tag="ul"
        >
        <el-tooltip
          v-for="item in drawlinedata"
          :key="item.label"
          :close-delay="0"
          :disabled="item.line"
          placement="right"
          :content="item.label"
          >
          <li
            v-if="item.line"
            class="tool-split-line"
          ></li>
          <template v-else>
            <li
              v-if="!item.line"
              :class="[
                {
                  'active': item.value === '' ? (!drawLine || drawLine === '') : (drawLine === item.value || item.active)
                }
              ]"
              class="line-tool-item mg-t4 ts-24"
              @click="drawlineChange(item.value)"
            >
              <MonoCross
                size="24"
                v-if="item.icon === 'icon-my-cross'"
              />
              <MonoFibonacci
                size="24"
                v-else-if="item.icon === 'icon-FibonacciRetracements'"
              />
              <MonoHorizonta
                size="24"
                v-else-if="item.icon === 'icon-my-horizontal-extended'"
              />
              <MonoParallel
                size="24"
                v-else-if="item.icon === 'icon-my-parallel-lines'"
              />
              <MonoPriceLine
                size="24"
                v-else-if="item.icon === 'icon-my-rice-line'"
              />
              <MonoRay
                size="24"
                v-else-if="item.icon === 'icon-my-ray'"
              />
              <MonoVerticalLine
                size="24"
                v-else-if="item.icon === 'icon-my-vertical-line'"
              />
              <MonoDelete
                size="24"
                v-else-if="item.icon === 'icon-my-delete'"
              />
            </li>
          </template>
        </el-tooltip>
      </el-scrollbar>
    </div>
    <div
      id="chart"
      class="charts-origin-box"
      :class="isShowTool ? 'offset' : ''"
    />
    <div
      v-if="isLoading"
      v-loading="isLoading"
      class="loadingBox"
    >
    </div>
  </div>
  <TechniCalInDicator
    v-if="isShowSetting"
    :originalTechnicalIndicatorSettings="originalTechnicalIndicatorSettings"
    :isShow="isShowSetting"
    @close="emit('closeSetting')"
    @updateOriginalTechnicalIndicatorSettings="updateSetting"
  />
</template>
<script lang="ts" setup>
import { ElScrollbar, ElTooltip } from 'element-plus'
import { init, dispose, registerLocale } from 'klinecharts'
import useOriginal, { baseTechnicalIndicatorMap } from '~/composables/useOriginal'
import MonoCross from '~/components/common/icon-svg/MonoCross.vue'
import MonoFibonacci from '~/components/common/icon-svg/MonoFibonacci.vue'
import MonoHorizonta from '~/components/common/icon-svg/MonoHorizonta.vue'
import MonoParallel from '~/components/common/icon-svg/MonoParallel.vue'
import MonoPriceLine from '~/components/common/icon-svg/MonoPriceLine.vue'
import MonoRay from '~/components/common/icon-svg/MonoRay.vue'
import MonoVerticalLine from '~/components/common/icon-svg/MonoVerticalLine.vue'
import TechniCalInDicator from './TechniCalInDicator.vue'
import { commonStore } from '~/stores/commonStore'
import { getKlinesApi } from '~/api/order'
import { nextTick } from 'vue'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const store = commonStore()
const { klineList, klineTicker, ticker, pairInfo, isPairDetail } = storeToRefs(store)
const { cancelKline } = store
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  isShowOriginalSetting: {
    type: Boolean,
    default: false
  },
  isShowDrawLine: {
    type: Boolean,
    default: true
  },
  resolution: {
    type: String,
    default: '15m'
  }
})
const isLoading = ref(false)
const emit = defineEmits(['closeSetting'])
let chart: any
const isShowTool = ref(false)
const isShowSetting = ref(false)
const drawlinedata = computed(() => {
  return [
    { label: t('十字线'), value: '', icon: 'icon-my-cross' },
    { line: true },
    { label: t('水平线'), value: 'horizontalStraightLine', icon: 'icon-my-horizontal-extended' },
    { label: t('垂直线'), value: 'verticalStraightLine', icon: 'icon-my-vertical-line' },
    { label: t('射线'), value: 'rayLine', icon: 'icon-my-ray' },
    // { label: t('平行射线'), value: 'horizontalRayLine', icon: 'icon-my-parallel-rays' },
    { label: t('平行直线'), value: 'parallelStraightLine', icon: 'icon-my-parallel-lines' },
    // { label: t('箭头'), value: 'JT', icon: 'icon-my-arrow' },
    { label: t('价格通道线'), value: 'priceLine', icon: 'icon-my-rice-line' },
    { label: t('斐波那契回调直线'), value: 'fibonacciLine', icon: 'icon-FibonacciRetracements' },
    { line: true },
    // { label: t('测量工具'), value: 'CL', icon: 'icon-Tools' },
    // { line: true },
    // { label: t('隐藏已画线'), value: 'yc', icon: 'icon-my-cover', active: !this.isShowLine },
    { label: t('清除画线'), value: 'del', icon: 'icon-my-delete' }
  ]
})
const drawLine = ref('')
watch(() => props.isShowOriginalSetting, (settingVal) => {
  isShowSetting.value = settingVal
})
watch(() => isShowTool.value, (val) => {
  nextTick(() => {
    chart && chart.resize()
  })
})
watch(() => props.isShowDrawLine, (val) => {
  isShowTool.value = val
}, { immediate: true })
watch(() => colorMode.preference, (val) => {
  chart.setStyles(val)
  if (val === 'light') {
    document.getElementById('chart').style.backgroundColor = '#ffffff'
  } else if (val === 'dark') {
    document.getElementById('chart').style.backgroundColor = '#1b1b1f'
  }
})

watch(() => locale.value, (lang) => {
  chart.setLocale(lang)
})
const drawlineChange = (shapeName: string) => {
  drawLine.value = shapeName
  if (shapeName === 'del') {
    chart.removeOverlay()
    drawLine.value = ''
  } else {
    chart.createOverlay({
      name: shapeName,
      points: [],
      styles: {
        text: {
          offset: [-2, 0]
        },
        line: {
          size: 2
        }
      },
      onRightClick() {
        return true
      },
      onDrawEnd({ overlay: { points } }: any) {
        drawLine.value = ''
      }
    })
  }
}
const originalTechnicalIndicatorSettings = ref({
  checkedIndicators: { // 基础版k线当前选中的技术指标
    main: ['MA'],
    sub: ['VOL']
  },
  technicalIndicatorSettings: baseTechnicalIndicatorMap
})
// 设置主指标参数和样式
const mainSettingFunc = () => {
  // 主指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: [],
      circles: []
    }
    const basicSettings = settings[name].setting

    if (['MA', 'EMA', 'SMMA'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'BOLL') {
      calcParams = [basicSettings.cycle.value, basicSettings.deviation.value]
      styles.lines = [{
        size: 1,
        color: basicSettings.BOLL.color
      }, {
        size: 1,
        color: basicSettings.UB.color
      }, {
        size: 1,
        color: basicSettings.DB.color
      }]
    } else if (name === 'SAR') {
      calcParams = [
        basicSettings.start.value,
        basicSettings.step.value,
        basicSettings.max.value
      ]
      styles.circles = [{
        upColor: basicSettings.SAR.color,
        downColor: basicSettings.SAR.color,
        noChangeColor: basicSettings.SAR.color
      }]
    }
    chart.overrideIndicator({
      name,
      visible: true,
      calcParams,
      styles
    })
  })
}
// 设置副指标参数和样式
const subSettingFunc = () => {
  // 副指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: []
    }
    const basicSettings = settings[name].setting
    if (['VOL', 'RSI'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'MACD') {
      calcParams = [basicSettings.MACD1.value, basicSettings.MACD2.value, basicSettings.MACD3.value]
      styles.line.colors = [basicSettings.DIF.color, basicSettings.DEA.color]
    } else if (name === 'KDJ') {
      calcParams = [basicSettings.KDJ1.value, basicSettings.KDJ2.value, basicSettings.KDJ3.value]
      styles.line.colors = [basicSettings.K.color, basicSettings.D.color, basicSettings.J.color]
    } else if (name === 'OBV') {
      calcParams = [basicSettings.MAOBV.value]
      styles.line.colors = [basicSettings.OBV.color, basicSettings.MAOBV.color]
    } else if (name === 'CCI') {
      calcParams = [basicSettings.CCI.value]
      styles.line.colors = [basicSettings.CCI.color]
    } else if (name === 'WR') {
      calcParams = [basicSettings.WR.value]
      styles.line.colors = [basicSettings.WR.color]
    } else if (name === 'DMI') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.PDI.color, basicSettings.MDI.color, basicSettings.ADX.color, basicSettings.ADXR.color]
    } else if (name === 'MTM') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.MTM.color, basicSettings.MAMTM.color]
    } else if (name === 'EMV') {
      calcParams = [basicSettings.EMV1.value, basicSettings.EMV2.value]
      styles.line.colors = [basicSettings.EMV.color, basicSettings.MAEMV.color]
    }
    chart.overrideIndicator({
      name,
      calcParams,
      styles
    })
  })
}
const paneMap: any = {}
const initTechnicalIndicator = () => {
  const nowPane: any = []
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true, { id: 'candle_pane' })
      paneMap[name] = paneId
    }
  })
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true)
      paneMap[name] = paneId
    }
  })
  Object.keys(paneMap).forEach(name => {
    if (!nowPane.includes(name)) {
      chart.removeIndicator(paneMap[name], name)
      delete paneMap[name]
    }
  })
}
const isReady = ref(false)
const lastPriceUpdate = ref(0)
const lastUpdateTime = ref(0)
const isLoadingInitialData = ref(false) // 防止重复加载初始数据
const currentLoadingResolution = ref('') // 当前正在加载的周期
const isViewingHistory = ref(false) // 跟踪用户是否在查看历史数据
const lastScrollPosition = ref(null) // 记录最后的滚动位置
const updateChart = val => {
  if (val.length > 0 && JSON.stringify(pairInfo.value[props.pair]) !== '{}') {
    isLoading.value = false
    chart && chart.setPrecision({
      price: (pairInfo.value[props.pair] || {}).price_scale,
      volume: 2
    })
  }
  if (val.length > 0) {
    const timer = setTimeout(() => {
      // 创建数据深拷贝，避免污染 store 数据
      let chartData = JSON.parse(JSON.stringify(val))

      // 限制基本版K线数组长度（2000根，KLineCharts推荐值）
      const maxKlineCount = 2000
      if (chartData.length > maxKlineCount) {
        // 保留最新的2000根K线
        chartData = chartData.slice(-maxKlineCount)
      }

      const latestKline = chartData[chartData.length - 1]
      const currentTime = Date.now()

      // 同步最新ticker价格到最后一根K线
      if (ticker.value[props.pair]) {
        const tickerPrice = Number(ticker.value[props.pair].last)
        if (tickerPrice > 0) {
          latestKline.close = tickerPrice
          // 更新最高价和最低价（如果需要）
          if (tickerPrice > latestKline.high) {
            latestKline.high = tickerPrice
          }
          if (tickerPrice < latestKline.low) {
            latestKline.low = tickerPrice
          }
        }
        // 确保时间戳是最新的（对于未收盘K线）
        latestKline.time = latestKline.time || currentTime
      }

      // 智能选择更新方法：查看历史数据时保持位置，否则更新到最新
      if (chart) {
        if (isViewingHistory.value) {
          // 用户正在查看历史数据，使用updateData保持当前视图位置
          chart.updateData(latestKline)
        } else {
          // 初始加载或用户查看最新数据，使用applyNewData
          chart.applyNewData(chartData, false)
        }
      }

      // 强制刷新 SMMA 指标，确保它延伸到最新蜡烛
      if (originalTechnicalIndicatorSettings.value.checkedIndicators.main.includes('SMMA')) {
        const smmaSettings = originalTechnicalIndicatorSettings.value.technicalIndicatorSettings.SMMA.setting
        const calcParams = []
        const styles = { lines: [] }

        // 根据设置构建参数
        for (const key in smmaSettings) {
          const setting = smmaSettings[key]
          if (setting.show) {
            calcParams.push(setting.value)
            styles.lines.push({
              size: 1,
              color: setting.color
            })
          }
        }

        // 重新应用 SMMA 指标配置
        chart && chart.overrideIndicator({
          name: 'SMMA',
          visible: true,
          calcParams,
          styles
        })
      }

      lastUpdateTime.value = currentTime

      clearTimeout(timer)
    }, 0)
  }
}

watch(klineList, (val) => {
  // 只有当数据真正变化时才更新图表
  if (val && val.length > 0) {
    updateChart(val)
  }
}, { deep: false })

watch(() => ticker.value[props.pair], (currentTicker, previousTicker) => {
  // 强化的ticker价格实时更新：确保ticker价格变化立即反映到K线图
  if (currentTicker && chart && klineList.value.length > 0) {
    const hasNewUpdate = currentTicker._lastUpdate &&
                        currentTicker._lastUpdate !== lastPriceUpdate.value
    const hasPriceChange = !previousTicker ||
                          previousTicker.last !== currentTicker.last

    // 只要有价格变化或者明确的更新标记，就执行更新
    if ((hasNewUpdate || hasPriceChange) && currentTicker.last) {
      lastPriceUpdate.value = currentTicker._lastUpdate || Date.now()

      // 使用 updateChart 函数统一处理，确保 SMMA 等指标能正确更新
      updateChart(klineList.value)
    }
  }
}, { immediate: false, deep: true })

// 检查和加载数据的函数 - 移到顶层作用域
const checkAndLoadData = async (forceReload = false) => {
  const isValidPair = props.pair && props.pair.length > 0
  const isValidResolution = props.resolution && props.resolution.length > 0

  if (!isValidPair || !isValidResolution) {
    return
  }

  // 对于1w和1M周期，需要调用API加载数据
  if (props.resolution === '1w' || props.resolution === '1M') {
    // 防止重复加载相同周期的数据
    if (!forceReload && isLoadingInitialData.value && currentLoadingResolution.value === props.resolution) {
      console.log('[BasicKline] Already loading data for:', props.resolution)
      return
    }

    console.log('[BasicKline] Loading data for:', props.resolution, 'forceReload:', forceReload)
    isLoadingInitialData.value = true
    currentLoadingResolution.value = props.resolution

    try {
      const initialData = await request(Date.now())
      if (initialData.length > 0) {
        console.log(`[BasicKline] Loaded ${initialData.length} candles for ${props.resolution}`)

        // 如果是周期切换（forceReload=true），直接替换数据
        // 如果是初始加载，也直接设置数据
        klineList.value = initialData

        // 更新图表
        if (chart) {
          chart.applyNewData(initialData, false)
        }

        isLoading.value = false
      } else {
        console.warn('[BasicKline] No data returned for:', props.resolution)
        // 即使没有数据，也要清空旧数据
        if (forceReload) {
          klineList.value = []
          if (chart) {
            chart.applyNewData([], false)
          }
        }
      }
    } catch (error) {
      console.error('[BasicKline] Error loading data:', error)
    } finally {
      isLoadingInitialData.value = false
      currentLoadingResolution.value = ''
    }
  } else if (props.resolution === '1d') {
    // 1d周期只使用WebSocket，确保订阅
    console.log('[BasicKline] 1d period - WebSocket only mode')
    if (!klineList.value.length || forceReload) {
      // 清空数据，等待WebSocket推送
      if (forceReload) {
        klineList.value = []
        if (chart) {
          chart.applyNewData([], false)
        }
      }
      nextTick(() => {
        store.getKlineSocket(props.pair, props.resolution)
      })
    }
  } else {
    // 其他周期，如果是强制重载，清空数据
    if (forceReload) {
      klineList.value = []
      if (chart) {
        chart.applyNewData([], false)
      }
    }
    // 通过store获取数据
    nextTick(() => {
      if (props.resolution === '1w' || props.resolution === '1M') {
        // 基础版1w/1M：订阅前重置“历史未就绪”标记，避免增量直接落地
        try { store?.resetBasicKlineHistoryReady?.(props.pair, props.resolution) } catch (e) {}
      }
      store.getKlineSocket(props.pair, props.resolution)
    })
  }
}

// 监听周期变化
watch(() => props.resolution, async (newResolution, oldResolution) => {
  if (newResolution && newResolution !== oldResolution) {
    console.log('[BasicKline] Resolution changed from', oldResolution, 'to', newResolution)
    isLoading.value = true

    // 周期切换时，重置查看历史状态
    isViewingHistory.value = false
    lastScrollPosition.value = null

    // 立即清空旧周期数据，避免旧周期在UI上短暂残留
    try {
      klineList.value = []
      if (chart) {
        chart.applyNewData([], false)
      }
    } catch (e) {
      // silence
    }

    // 周期切换时，强制重新加载数据
    await checkAndLoadData(true)
  }
})

// 监听交易对变化：基本版在任意周期先拉一次历史API，再由上层WS补充实时
watch(() => props.pair, async (newPair, oldPair) => {
  if (!newPair || newPair === oldPair) return
  try {
    isLoading.value = true
    // 清空旧数据，避免跨交易对残留
    klineList.value = []
    if (chart) chart.applyNewData([], false)
    const { data } = await getKlinesApi({
      symbol: newPair,
      market: newPair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: props.resolution,
      before: Date.now(),
      limit: 300
    })
    if (data && data.e) {
      const initialData = data.e.map(item => transChartData(item))
      klineList.value = initialData
      if (chart) chart.applyNewData(initialData, false)
      // 基础版：标记历史已就绪，冲洗缓存增量（1w/1M 防闪屏）
      try {
        store?.markBasicKlineHistoryReady?.(newPair, props.resolution)
      } catch (e) {}

    }
  } catch (e) {
    // silence
  } finally {
    isLoading.value = false
  }
})


watch([ticker, klineTicker], ([val1,val2]) => {
  const last = (val1[props.pair] || {}).last
  if (last && klineList.value.length > 0) {
    const currentTickerPrice = Number(last) < 0 ? -Number(last) : Number(last)

    // 检查是否有价格变化，用于强制更新判断
    const lastKlinePrice = chart && chart.getDataByIndex ?
      chart.getDataByIndex(-1)?.close : null
    const hasPriceChange = !lastKlinePrice ||
      Math.abs(lastKlinePrice - currentTickerPrice) > 0.001

    // 如果有价格变化，使用统一的 updateChart 函数处理
    if (hasPriceChange) {
      // 使用 updateChart 确保 SMMA 等指标能正确更新
      updateChart(klineList.value)
    }
  }
}, {
  deep: true
})
const updateSetting = (data) => {
  originalTechnicalIndicatorSettings.value = data
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
}
const transChartData = (obj) => {
  const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
  const finalItem = {
    time: Number(obj[0])
  }
  obj.forEach((v, i) => {
    finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
  })
  return finalItem
}
const request = async (end?: number, callback?: any, retryCount = 3) => {
  // 仅基本版1日周期禁用API调用，1w和1M需要正常调用
  if (props.resolution === '1d') {
    console.log('[BasicKline] 1d period - API disabled, using WebSocket only')
    return []
  }

  // 1w和1M正常调用API
  console.log('[BasicKline] Calling API for period:', props.resolution)

  try {
    const { data } = await getKlinesApi({
      symbol: props.pair,
      market: props.pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: props.resolution,
      before: end,
      limit: 300
    });

    if (data?.e) {
      const klineData = data.e.map(item => transChartData(item));
      console.log(`[BasicKline] API returned ${klineData.length} candles for ${props.resolution}`)
      return klineData;
    } else {
      console.warn('[BasicKline] API returned no data for', props.resolution)
      return [];
    }
  } catch (error) {
    console.error('[BasicKline] API error for', props.resolution, error)
    if (error.response?.status === 504 && retryCount > 0) {
      // 如果是504错误且还有重试次数，等待一段时间后重试
      const delay = (4 - retryCount) * 1000; // 重试延迟时间递增：1s, 2s, 3s
      await new Promise(resolve => setTimeout(resolve, delay));
      return request(end, callback, retryCount - 1);
    }
    return [];
  }
}
const chartInit = () => {
  const subscriptNumbers = {
    0: '₀',
    1: '₁',
    2: '₂',
    3: '₃',
    4: '₄',
    5: '₅',
    6: '₆',
    7: '₇',
    8: '₈',
    9: '₉',
    10: '₁₀',
    11: '₁₁',
    12: '₁₂',
    13: '₁₃',
    14: '₁₄',
    15: '₁₅',
    16: '₁₆',
    17: '₁₇',
    18: '₁₈',
    19: '₁₉',
    20: '₂₀',
    21: '₂₁',
    22: '₂₂',
    23: '₂₃',
    24: '₂₄',
    25: '₂₅',
    26: '₂₆',
    27: '₂₇',
    28: '₂₈',
    29: '₂₉',
    30: '₃₀',
    31: '₂₁',
    32: '₃₂',
    33: '₃₃'
  }
  chart = init('chart', {
    thousandsSeparator: '',
    decimalFoldThreshold: 8,
    loadMore: {
      isLoadMore: true, // 启用加载更多
    }
  }) as any
  chart.setLoadMoreDataCallback(async ({ type, data, callback }) => {
    if (type === 'forward') {
      // 仅1d禁用历史数据加载，1w和1M正常加载
      if (props.resolution === '1d') {
        console.log('[BasicKline] 1d period - loadMore disabled')
        callback([], false);
        return;
      }

      // 防止初始化时自动触发
      if (!isReady.value) {
        console.log('[BasicKline] Preventing initial loadMore trigger')
        callback([], false);
        return;
      }

      // 对于1w和1M周期，即使在初始数据加载中也允许加载更多
      if (isLoadingInitialData.value && props.resolution !== '1w' && props.resolution !== '1M') {
        console.log('[BasicKline] Initial data loading, skip loadMore')
        callback([], false);
        return;
      }

      // 标记用户正在查看历史数据
      isViewingHistory.value = true;

      // 1w和1M正常加载历史数据
      console.log('[BasicKline] Loading more data for:', props.resolution)
      const end = data ? data.timestamp : Date.now();
      const klineData = await request(end);
      callback(klineData, klineData.length === 300);
    } else {
      callback([], false);
    }
  });
  chart && chart.applyNewData(klineList.value, klineList.value.length === 1000)
  chart.setDecimalFold({
    format: value => {
      let vl = `${value}`;
      const [integer, decimalPart] = vl.split('.');
      const trimmedDecimalPart = decimalPart ? decimalPart.replace(/0+$/, '') : '';
      vl = integer + (trimmedDecimalPart ? '.' + trimmedDecimalPart : '');
      const reg = new RegExp('\\.0{8,}[1-9][0-9]*$');
      if (reg.test(vl)) {
        const result = vl.split('.');
        const lastIndex = result.length - 1;
        const v = result[lastIndex];
        const match = /0*/.exec(v);
        if (match) {
          const count = match[0].length;
          result[lastIndex] = v.replace(/0*/, `0${subscriptNumbers[count]}`);
          return result.join('.')
        }
      }
      return vl;
    }
  })
  chart.setStyles(useOriginal(colorMode.preference, 'green-up'))
  chart.setLocale(locale.value)
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
  registerLocale('zh', {
    time: '时间：',
    open: '开：',
    high: '高：',
    low: '低：',
    close: '收：',
    volume: '成交量：'
  })
  registerLocale('en', {
    time: 'Time：',
    open: 'Open：',
    high: 'High：',
    low: 'Low：',
    close: 'Close：',
    volume: 'Volume：'
  })
  registerLocale('ja', {
    time: '時間：',
    open: '始値：',
    high: '高値：',
    low: '安値：',
    close: '終値：',
    volume: '出来高：'
  })
  registerLocale('ko', {
    time: '시간：',
    open: '시가：',
    high: '고가：',
    low: '저가：',
    close: '종가：',
    volume: '거래량：'
  })
  registerLocale('zh-Hant', {
    time: '時間：',
    open: '開盤：',
    high: '最高：',
    low: '最低：',
    close: '收盤：',
    volume: '成交量：'
  })

  // 监听图表滚动/缩放事件，判断用户是否在查看历史数据
  chart.subscribeAction('onVisibleRangeChange', (params) => {
    try {
      const dataList = chart.getDataList()
      if (!dataList || dataList.length === 0) return

      const visibleRange = chart.getVisibleRange()
      if (visibleRange) {
        const lastDataIndex = dataList.length - 1
        const visibleTo = visibleRange.to

        // 如果可见区域的结束位置不是最后一根K线，说明用户在查看历史数据
        // 给一个小的容差（比如最后5根K线内都算查看最新）
        const isAtLatest = visibleTo >= lastDataIndex - 5

        if (!isAtLatest) {
          // 用户正在查看历史数据
          isViewingHistory.value = true
          lastScrollPosition.value = visibleTo
        } else {
          // 用户查看最新数据
          isViewingHistory.value = false
          lastScrollPosition.value = null
        }
      }
    } catch (error) {
      console.warn('[BasicKline] Error in scroll listener:', error)
    }
  })

  // 监听图表点击/拖动事件，设置查看历史标志
  chart.subscribeAction('onScroll', () => {
    // 用户手动滚动时，暂时标记为查看历史
    isViewingHistory.value = true

    // 设置一个定时器，如果3秒内没有新的WebSocket更新，则保持历史查看状态
    // 否则根据位置判断
    clearTimeout(window.klineScrollTimer)
    window.klineScrollTimer = setTimeout(() => {
      const dataList = chart.getDataList()
      const visibleRange = chart.getVisibleRange()
      if (dataList && visibleRange) {
        const isAtLatest = visibleRange.to >= dataList.length - 5
        isViewingHistory.value = !isAtLatest
      }
    }, 3000)
  })

  isReady.value = true
}
onMounted(() => {
  console.log('[BasicKline] Component mounted with resolution:', props.resolution)
  console.log('[BasicKline] Initial klineList length:', klineList.value.length)

  isLoading.value = true
  window.addEventListener(
    'resize',
    () => {
      setTimeout(() => {
        chart && chart.resize()
      }, 300)
    }
  )
  chartInit()

  // 初始数据加载
  if (props.resolution === '1w' || props.resolution === '1M') {
    try { store?.resetBasicKlineHistoryReady?.(props.pair, props.resolution) } catch (e) {}
  }
  checkAndLoadData(false)

  // 延迟二次检查，仅对非1w/1M周期
  setTimeout(() => {
    if (klineList.value.length === 0 && !isLoadingInitialData.value &&
        props.resolution !== '1w' && props.resolution !== '1M' && props.resolution !== '1d') {
      console.log('[BasicKline] Secondary data check')
      checkAndLoadData(false)
    }
  }, 1000)

  // 初始化技术指标
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
})
onUnmounted(() => {
  // 清理滚动定时器
  if (window.klineScrollTimer) {
    clearTimeout(window.klineScrollTimer)
    delete window.klineScrollTimer
  }

  // 销毁图表实例
  if (chart) {
    try {
      dispose('chart')
    } catch (error) {
      console.warn('[BasicKline] Error disposing chart:', error)
    }
    chart = null
  }

  // 重置状态
  isViewingHistory.value = false
  lastScrollPosition.value = null
})
</script>
<style lang="scss" scoped>
.original-kline-wrap {
  width: 100%;
  height: calc(100% - 42px);
  position: relative;
  overflow: hidden; // 添加这行
  .line-tool {
    position: relative;
    height: 100%;
    width: 52px;
    z-index: 1;

    .line-tool-scroll-wrap {
      position: relative;
      height: 100%;
      width: 52px;
    }

    .line-tool-item {
      width: 34px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      @include color(tc-secondary);

      &:hover {
        @include color(theme);
        @include bg-color(bg-quaternary);
      }

      &.active {
        @include color(theme);
      }

      &:not(:first-of-type) {
        margin-top: 4px;
      }
    }

    .tool-split-line {
      height: 1px;
      width: 100%;
      margin: 4px 0;
      @include bg-color(border);
    }
  }

  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
}

.charts-origin-box {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  transform: translateZ(0); // 添加硬件加速
  &.offset {
    left: 52px;
    width: calc(100% - 52px);
  }
}
@include mb {
  .original-kline-wrap {
    width: 100%;
    height: calc(100% - 44px);
  }
}
</style>