<template>
  <div class="madEx-footer">
    <div class="footer-wrapper">
      <div class="m-Logo-wrap">
        <div class="m-logo">
          <img src="~/assets/images/common/logo.png" />
        </div>
      </div>
      <div class="foot-list flex-box align-start space-between">
        <div class="download-wrap">
          <div class="download-cont">
            <div class="download-title">
              <h2 class="fit-tc-primary font-size-28">{{ $t('安全交易，信赖之选') }}</h2>
              <p class="fit-tc-secondary font-size-14">{{ $t('随时随地，畅享交易') }}</p>
            </div>
            <dl class="flex-box align-start">
              <dt>
                <div class="dt-left">
                  <a href="https://apps.apple.com/app/KTX-pro/id6745578008" target="_blank">
                    <div class="icon-box apple"></div>
                    App Store
                  </a>
                  <a href="https://testflight.apple.com/join/6vANbBYb" target="_blank">
                    <div class="icon-box apple"></div>
                    Test Flight
                  </a>
                  <a  class="disabled-btn">
                    <div class="icon-box google"></div>
                    GooglePlay
                  </a>
                </div>
                <div class="dt-right">
                  <a :href="androidUrl ? `${androidUrl}?${new Date().getTime()}` : 'javascript:;'" :class="androidUrl === '' ? 'disabled-btn' : ''" :target="androidUrl === '' ? '' : '_blank'" rel="noopener noreferrer">
                    <div class="icon-box android"></div>
                    Android
                  </a>
                  <a :href="`https://ktx-private.github.io/api-${locale === 'zh' ? 'zh' : 'en'}/`" target="_blank">
                    <div class="icon-box api"></div>
                    API
                  </a>
                </div>
              </dt>
              <dd>
                <div class="code-wrap">
                  <BoxQrcode :size="160" :value="`https://www.ktx.com/${locale}/download`" style="margin:0 auto;" />
                </div>
                <p class="font-size-14 fit-tc-secondary pd-t12">{{ $t('立即扫描下载') }}</p>
                <p class="font-size-18 fit-tc-primary">iOS & Android</p>
              </dd>
            </dl>
          </div>
        </div>
        <div class="flex-1 flex-box flex-wrap more-list align-start space-between">
          <ul class="mg-b40" style="white-space:nowrap;">
            <li class="item-title" :class="{'showMenu': isShowMenuObj[0]}" @click="changeMenu(0)">{{ $t('帮助支持') }}</li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <NuxtLink :to="`/${locale}/about-us`" class="fit-tc-primary">
                {{ $t('关于我们') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/articles/4733818896670`" target="_blank" class="fit-tc-primary">
                {{ $t('使用条款') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/articles/4662556551966`" target="_blank" class="fit-tc-primary">
                {{ $t('隐私条款') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <NuxtLink :to="`/${locale}/join-us`" class="fit-tc-primary">
                {{ $t('职业机会') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/articles/4662623122462`" target="_blank" class="fit-tc-primary">
                {{ $t('联系我们') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[0]" class="item-li">
              <NuxtLink :to="`/${locale}/reserve-fund`" class="fit-tc-primary">
                {{ $t('储备金证明') }}
              </NuxtLink>
            </li>
          </ul>
          <ul class="mg-b40" style="white-space:nowrap;">
            <li class="item-title" :class="{'showMenu': isShowMenuObj[1]}" @click="changeMenu(1)">{{ $t('产品') }}</li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <a class="fit-tc-secondary cursor-default">
                {{ $t('购买加密货币') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <a class="fit-tc-secondary cursor-default">
                {{ $t('闪兑') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <NuxtLink :to="`/${locale}/exchange/BTC_USDT`" class="fit-tc-primary">
                {{ $t('交易') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <a class="fit-tc-secondary cursor-default">
                {{ $t('赚币') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <NuxtLink :to="`/${locale}/nft/my/assets/nft-list`" class="fit-tc-primary">
                {{ $t('NFT') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[1]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/sections/4662292159134`" target="_blank" class="fit-tc-primary">
                {{ $t('活动') }}
              </a>
            </li>
          </ul>
          <ul class="mg-b40" style="white-space:nowrap;">
            <li class="item-title" :class="{'showMenu': isShowMenuObj[2]}" @click="changeMenu(2)">{{ $t('服务') }}</li>
            <li v-if="isShowMenuObj[2] && (!userInfo || JSON.stringify(userInfo) === '{}' || JSON.stringify(userInfo) !== '{}' && userInfo.is_paper_trader * 1 === 0)" class="item-li">
              <NuxtLink :to="`/${locale}/invite`" class="fit-tc-primary">
                {{ $t('邀请奖励') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[2]" class="item-li">
              <NuxtLink :to="`/${locale}/nodePlanApply`" class="fit-tc-primary">
                {{ $t('节点计划') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[2]" class="item-li">
              <a :href="`https://ktx-private.github.io/api-${locale === 'zh' ? 'zh' : 'en'}/`" target="_blank" class="fit-tc-primary">
                {{ $t('API文档') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[2]" class="item-li">
              <NuxtLink :to="`/${locale}/tokenApply`" class="fit-tc-primary">
                {{ $t('上币申请') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[2]" class="item-li">
              <NuxtLink :to="`/${locale}/rate-list`" class="fit-tc-primary">
                {{ $t('费率表') }}
              </NuxtLink>
            </li>
          </ul>
          <ul class="mg-b40" style="white-space:nowrap;">
            <li class="item-title" :class="{'showMenu': isShowMenuObj[3]}" @click="changeMenu(3)">{{ $t('帮助') }}</li>
            <li v-if="isShowMenuObj[3]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/categories/4662266924574`" target="_blank" class="fit-tc-primary">
                {{ $t('帮助中心') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[3]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/requests/new?ticket_form_id=4663234192542`" target="_blank" class="fit-tc-primary">
                {{ $t('产品反馈与建议') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[3]" class="item-li">
              <NuxtLink :to="`/${locale}/official-verification`" class="fit-tc-primary">
                {{ $t('官方渠道验证') }}
              </NuxtLink>
            </li>
            <li v-if="isShowMenuObj[3]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/categories/4662292158366`" target="_blank" class="fit-tc-primary">
                {{ $t('公告中心') }}
              </a>
            </li>
            <li v-if="isShowMenuObj[3]" class="item-li">
              <a :href="`${useCommon.zendeskUrl(locale)}/articles/4732855192990`" class="fit-tc-primary">
                {{ $t('执法请求') }}
              </a>
            </li>
          </ul>
          <ul style="white-space:nowrap;">
            <li class="item-title" :class="{'showMenu': isShowMenuObj[4]}" @click="changeMenu(4)">{{ $t('联系方式') }}</li>
            <li v-if="isShowMenuObj[4]" class="item-li">
              <span class="fit-tc-primary mg-r4">{{ $t('机构/渠道合作') }}:</span>
              <span class="fit-tc-secondary"><EMAIL></span>
            </li>
            <li v-if="isShowMenuObj[4]" class="item-li">
              <span class="fit-tc-primary mg-r4">{{ $t('市场合作') }}:</span>
              <span class="fit-tc-secondary"><EMAIL></span>
            </li>
            <li v-if="isShowMenuObj[4]" class="item-li">
              <span class="fit-tc-primary mg-r4">{{ $t('上币申请') }}:</span>
              <span class="fit-tc-secondary"><EMAIL></span>
            </li>
            <li v-if="isShowMenuObj[4]" class="item-li">
              <span class="fit-tc-primary mg-r4">{{ $t('客户支持') }}:</span>
              <span class="fit-tc-secondary"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>
      <div class="foot-media-wrap flex-box space-between">
        <div class="logo">
          <img src="~/assets/images/common/logo.png" />
        </div>
        <div class="media-title">{{ $t('社区') }}</div>
        <ul class="media-list-box flex-box">
          <!-- <li>
            <a href="" class="fit-tc-secondary">
              <MonoFacebook />
            </a>
          </li> -->
          <li>
            <a href="https://x.com/ktxchange" target="_blank" class="fit-tc-secondary">
              <MonoTwitter />
            </a>
          </li>
          <li>
            <a href="https://t.me/+I97Eok9Aqzo3ODNl" target="_blank" class="fit-tc-secondary">
              <MonoTelegram />
            </a>
          </li>
          <!-- <li>
            <a href="" class="fit-tc-secondary">
              <MonoContactTwitter />
            </a>
          </li>
          <li>
            <a href="" class="fit-tc-secondary">
              <MonoYouTube />
            </a>
          </li>
          <li>
            <a href="" class="fit-tc-secondary">
              <MonoLinkedin />
            </a>
          </li>
          <li>
            <a href="" class="fit-tc-secondary">
              <MonoTelegram />
            </a>
          </li> -->
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import MonoFacebook from '~/components/common/icon-svg/MonoFacebook.vue'
import MonoTelegram from '~/components/common/icon-svg/MonoTelegram.vue'
import MonoTwitter from '~/components/common/icon-svg/MonoTwitter.vue'
import MonoYouTube from '~/components/common/icon-svg/MonoYouTube.vue'
import MonoContactTwitter from '~/components/common/icon-svg/MonoContactTwitter.vue'
import MonoLinkedin from '~/components/common/icon-svg/MonoLinkedin.vue'
import { useCommonData } from '~/composables/index'
import { commonStore } from '~/stores/commonStore'
import { useUserStore } from '~/stores/useUserStore'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const store = commonStore()
const { getDownLoadInfo } = store
const { downLoadInfo } = storeToRefs(store)
const { locale, t } = useI18n()
const useCommon = useCommonData()
const isShowMenuObj = ref({
  0: true,
  1: true,
  2: true,
  3: true,
  4: true
})
const screenWidth = ref(0)
const isMobile = ref(false)
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
  isMobile.value = screenWidth.value <= 1024
}
const changeMenu = (val) => {
  if (!isMobile.value) {
    return false
  }
  isShowMenuObj.value[val] = !isShowMenuObj.value[val]
}
watch(() => isMobile.value, (val) => {
  if (val) {
    isShowMenuObj.value = {
      0: false,
      1: false,
      2: false,
      3: false,
      4: false
    }
  } else {
    isShowMenuObj.value = {
      0: true,
      1: true,
      2: true,
      3: true,
      4: true
    }
  }
}, {
  immediate: true
})
const androidUrl = computed(() => {
  const url = downLoadInfo.value && downLoadInfo.value[4] && downLoadInfo.value[4].download_url || ''
  return url || ''
})
onMounted(() => {
  updateScreenWidth()
  window.addEventListener('resize', updateScreenWidth)
})
onBeforeMount(() => {
  getDownLoadInfo()
})
</script>
<style lang="scss">
img{
  width:100%;
  height:auto;
}
.madEx-footer{
  position:relative;
  border-top:1px solid;
  @include border-color(border);
  .footer-wrapper{
    padding-top:135px;
    .m-Logo-wrap{
      display:none;
    }
    .foot-list{
      border-top:1px solid;
      padding:40px;
      @include border-color(border);
      .item-title{
        font-size:18px;
        padding-bottom:12px;
        @include color(tc-primary);
      }
      .item-li{
        padding-bottom:12px;
        font-size:14px;
        line-height:22px;
        font-weight:400;
        @include color(tc-secondary);
      }
      .download-wrap{
        margin-right:8%;
        .download-cont{
          .download-title{
            margin-top:-4px;
          }
          dl{
            padding-top:20px;
            dt{
              margin-right:40px;
              display:flex;
              .dt-right{
                margin-left:12px;
              }
              a{
                display:flex;
                margin-bottom:12px;
                height:52px;
                align-items:center;
                padding:0 16px;
                width:158px;
                border-radius:8px;
                @include bg-color(bg-secondary);
                @include color(tc-primary);
                &.disabled-btn{
                  opacity:0.5;
                  cursor:default;
                }
                .icon-box{
                  width:32px;
                  height:32px;
                  margin-right:12px;
                  &.apple{
                    @include get-img('~/assets/images/download/ios-gray-icon.png', '~/assets/images/download/ios-white-icon.png');
                    background-size:100% 100%;
                  }
                  &.google{
                    background: url('~/assets/images/download/anzhuo-color-icon.png') no-repeat center;
                    background-size:100% 100%;
                  }
                  &.android{
                    background: url('~/assets/images/download/anzhuo-icon.png') no-repeat center;
                    background-size:100% 100%;
                  }
                  &.api{
                    @include get-img('~/assets/images/download/api-black-icon.png', '~/assets/images/download/api-white-icon.png');
                    background-size:100% 100%;
                  }
                }
              }
            }
            dd{
              text-align:center;
              .code-wrap{
                // border:1px solid;
                // border-radius:12px;
                // padding:12px;
                // @include border-color(border);
              }
            }
          }
        }
      }
      .more-list{
        &.space-between{
          justify-content:flex-end;
        }
        ul{
          margin-right:120px;
          &:last-child{
            margin-right:0;
          }
        }
      }
    }
    .foot-media-wrap{
      position:absolute;
      top:0;left:0;
      right:0;
      height:135px;
      padding:0 40px;
      .logo{
        width:134px;
        height:40px;
      }
      .media-title{
        display:none;
      }
      .media-list-box{
        li{
          cursor:pointer;
          margin-left:24px;
          @include color(tc-secondary);
          @include pc-hover {
            &:hover{
              @include color(theme);
            }
          }
        }
      }
    }
  }
}
@include md1280 {
  .madEx-footer{
    .footer-wrapper{
      .foot-list{
        .download-wrap{
          .download-cont{
            dl{
              padding-top:20px;
              dt{
                margin-right:40px;
                display:flex;
                flex-direction: column;
                .dt-right{
                  margin-left:0;
                }
              }
            }
          }
        }
        .more-list{
          &.space-between{
            justify-content: space-between;
          }
          ul{
            margin-right:0px;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
@include md1164 {
  .madEx-footer{
    .footer-wrapper{
      .foot-list{
        .download-wrap{
          .download-cont{
            dl{
              padding-top:20px;
              dt{
                margin-right:40px;
                display:flex;
                flex-direction: column;
                .dt-right{
                  margin-left:0;
                }
              }
            }
          }
        }
        .more-list{
          &.space-between{
            justify-content: space-between;
          }
          ul{
            margin-right:40px;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
@include md {
  .madEx-footer{
    .footer-wrapper{
      .foot-list{
        .download-wrap{
          .download-cont{
            dl{
              padding-top:20px;
              dt{
                margin-right:40px;
                display:flex;
                flex-direction: column;
                .dt-right{
                  margin-left:0;
                }
              }
            }
          }
        }
        .more-list{
          &.space-between{
            justify-content: space-between;
          }
          ul{
            margin-right:20px;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
@include md1423 {
  .madEx-footer{
    .footer-wrapper{
      .foot-list{
        .download-wrap{
          .download-cont{
            dl{
              padding-top:20px;
              dt{
                margin-right:40px;
                display:flex;
                flex-direction: column;
                .dt-right{
                  margin-left:0;
                }
              }
            }
          }
        }
        .more-list{
          &.space-between{
            justify-content: space-between;
          }
          ul{
            margin-right:40px;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
@include md1920 {
  .madEx-footer{
    .footer-wrapper{
      .foot-list{
        .download-wrap{
        }
        .more-list{
          &.space-between{
            justify-content: space-between;
          }
          ul{
            margin-right:40px;
            &:last-child{
              margin-right:0;
            }
          }
        }
      }
    }
  }
}
@include mb {
  .madEx-footer{
    .footer-wrapper{
      padding-top:0px;
      padding:0 20px;
      .m-Logo-wrap{
        display:block;
        padding:18px 0;
        .m-logo{
          width:80px;
          height:24px;
        }
      }
      .foot-list{
        border-top:0;
        border-bottom:1px solid;
        padding:0;
        padding-bottom:12px;
        @include border-color(border);
        &.flex-box{
          display:block !important;
        }
        .more-list{
          display:flex;
          flex-direction: column;
          ul{
            width:100%;
            &.mg-b40{
              margin-bottom:0!important;
            }
          }
        }
        .item-title{
          font-size:16px;
          padding-bottom:0;
          height:54px;
          line-height:54px;
          position:relative;
          &:after{
            content: '';
            display:block;
            position:absolute;
            right:0px;
            top:50%;
            margin-top:-6px;
            height:12px;
            width:12px;
            background-size:100% auto;
            background-position:center;
            @include get-img('@/assets/images/common/selected-icon-light.png', '@/assets/images/common/selected-icon-dark.png');
          }
          &.showMenu{
            &:after{
              transform: rotate(180deg);
            }
          }
        }
        .item-li{
          padding-bottom:0;
          font-size:14px;
          line-height:20px;
          padding:8px 0px;
        }
        .download-wrap{
          margin-right:0;
          .download-cont{
            .download-title{
              margin-top:-4px;
            }
            dl{
              dt{
                flex-direction: column;
                .dt-right{
                  margin-left:0;
                }
                margin-right:0;
                width:100%;
                a{
                  width:100%;
                }
              }
              dd{
                display:none;
              }
            }
          }
        }
      }
      .foot-media-wrap{
        position:static;
        height:auto;
        padding:0 0 20px;
        &.flex-box{
          display:flex;
        }
        .logo{
          display:none;
        }
        .media-title{
          display:block;
          font-size:16px;
          height:54px;
          line-height:54px;
          @include color(tc-primary);
        }
        .media-list-box{
          li{
            flex:1;
            margin-left:20px;
            display:flex;
            align-items:center;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
