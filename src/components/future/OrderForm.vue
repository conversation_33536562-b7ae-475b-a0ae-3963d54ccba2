<template>
  <div class="orderform-container animate__animated" :class="{'animate__fadeOutDown': !isShowTrade, 'animate__fadeInUp': isShowTrade}">
    <div class="orderform-wrapper">
      <div class="orderform-title flex-box space-between">
        {{ $t('合约交易') }}
        <MonoClose @click="isShowTrade = false" />
      </div>
      <div class="order-cont-box">
        <FuturesTradeForm
          :isLogin="isLogin"
          :cbuCrossBalance="totalAvailable"
          :cbcCrossBalance="cbcAvailableBalance"
          :pairInfo="pairInfo"
          :ticker="ticker"
          :pair="pair"
          :coinInfo="coinInfo"
          :futuresType="futuresType"
          :isCBCUnitUSD="isCBCUnitUSD"
          :isCBUUnitUSDT="isCBUUnitUSDT"
          :priceScale="priceScale"
          :quantityScale="quantityScale"
          :canCloseBuyNum="canCloseBuyNum"
          :canCloseSellNum="canCloseSellNum"
          :price="price"
          :amount="amount"
          @updateAssets="emit('updateAssets')"
        />
        <div v-if="futuresType === 'lpc' && isLogin && (mode[pair] === 1 || mode[pair] === 3)" class="risk-container">
          <div class="flex-box space-between">
            <div class="relative">
              <div class="font-size-14 fit-tc-primary">USDT{{ $t('全仓') }}</div>
              <span class="font-size-14 fit-tc-secondary">
                <el-tooltip placement="left" class="tooltips">
                  <template #content>
                    <div style="width:400px" class="pd-lr4 pd-tb4 font-size-14 fit-tc-button tw-4">
                      <div class="tw-6 mg-b8">{{ $t('保证金率 = 维持保证金 / 保证金余额') }}</div>
                      <div class="mg-b8">{{ $t('保证金率越高越容易平仓, 保证金率 100% 时, 所有全仓仓位将受到强平') }}</div>
                      <div>{{ $t('保证金余额') }} = {{ format(leftMaintain, 4, true) }} USDT</div>
                      <div>{{ $t('维持保证金') }} = {{ format(cbuMt, 4, true) }} USDT</div>
                    </div>
                  </template>
                  <span class="border-dotted cursor-pointer">{{ $t('风险率') }}</span>
                </el-tooltip>
                <span v-if="persentRate < 60" class="fit-rise">{{ format(persentRate, 2, true) }}%</span>
                <span v-if="persentRate < 80 && persentRate >= 60" class="fit-warn">{{ format(persentRate, 2, true) }}%</span>
                <span v-if="persentRate >= 80" class="fit-fall">{{ format(persentRate, 2, true) }}%</span>
              </span>
            </div>
            <div class="flex-box align-end" style="margin-top: -12px;">
              <span class="font-size-14 fit-tc-primary">0</span>
              <div class="risk-chart">
                <img class="chart-line" src="~/assets/images/future/margin_chart_line.png" />
                <img :style="`transform: rotate(${riskRotate}deg)`" class="chart-point" src="~/assets/images/future/margin_chart_pint.png" />
              </div>
              <span class="font-size-14 fit-tc-primary">100</span>
            </div>
          </div>
        </div>
        <div v-if="isLogin" class="assets-container">
          <div class="assets-wrapper">
            <div class="assets-title flex-box space-between">
              <h2 class="fit-tc-secondary tw-4 flex-box align-start" :class="{'flex-column': locale === 'en'}" style="white-space:nowrap;">
                {{ $t('合约资产') }}
                <div class="fit-theme font-size-12 flex-box mg-l4 cursor-pointer" @click="collatralFun()">
                  {{ $t('资产抵押') }}
                  <MonoWarn size="12" class="mg-l4" />
                </div>
              </h2>
              <div class="right-cont flex-box font-size-14">
                <a class="fit-theme flex-box cursor-pointer" @click="isShowTransfer = true">
                  <span style="white-space:nowrap;" class="font-size-14">{{ $t('划转') }}</span>
                  <MonoAssetsRansferIcon size="16" class="fit-theme mg-l4 cursor-pointer" />
                </a>
                <!-- <el-tooltip :content="$t('资金划转')" placement="top">
                </el-tooltip> -->
              </div>
            </div>
            <ul>
              <li class="flex-box space-between">
                <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('账户权益') }}</div>
                <div class="right-text flex-box">
                  <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                    <template v-if="futuresType === 'ipc'">{{ format(cbcTotalBalance, quantityScale, true, true) }}</template>
                    <template v-else>{{ format(cbuTotalBalance, 4, true, true) }}</template>
                  </b>
                  <span class="font-size-14 fit-tc-secondary tw-4">{{ futuresType === 'ipc' ? coinSymbol : currencySymbol }}</span>
                </div>
              </li>
              <li class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('未实现盈亏') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      <template v-if="futuresType === 'ipc'">{{ format(unprofit, quantityScale, true, true) }}</template>
                      <template v-else>{{ format(unprofit, 4, true, true) }}</template>
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">{{ futuresType === 'ipc' ? coinSymbol : currencySymbol }}</span>
                  </div>
                </li>
                <li class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('可用保证金') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      <template v-if="futuresType === 'ipc'">{{ format(cbcAvailableBalance, quantityScale, true, true) }}</template>
                      <template v-else>{{ format(cbuAvailableBalance, 4, true, true) }}</template>
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">{{ futuresType === 'CBC' ? coinSymbol : currencySymbol }}</span>
                  </div>
                </li>
                <li class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('持仓保证金') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      <template v-if="futuresType === 'ipc'">{{ format((posMapObj[coinSymbol] || {}).posMargin, quantityScale, true, true) }}</template>
                      <template v-else>{{ format((posMapObj[currencySymbol] || {}).posMargin, 4, true, true) }}</template>
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">{{ futuresType === 'ipc' ? coinSymbol : currencySymbol }}</span>
                  </div>
                </li>
                <li class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('冻结保证金') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      <template v-if="futuresType === 'ipc'">{{ format((posMapObj[coinSymbol] || {}).orderMargin, quantityScale, true, true) }}</template>
                      <template v-else>{{ format((posMapObj[currencySymbol] || {}).orderMargin, 4, true, true) }}</template>
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">{{ futuresType === 'ipc' ? coinSymbol : currencySymbol }}</span>
                  </div>
                </li>
                <li v-if="(mode[pair] * 1 === 1 || mode[pair] * 1 === 3) && tradingBonusShow * 1 > 0" class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('抵扣金') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      {{ format(tradingBonusShow, 4, true, true) }}
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">USDT</span>
                  </div>
                </li>
                <li v-if="(mode[pair] * 1 === 1 || mode[pair] * 1 === 3) && tyjBonus * 1 > 0" class="flex-box space-between">
                  <div class="left-text fit-tc-secondary font-size-14 tw-4">{{ $t('体验金') }}</div>
                  <div class="right-text flex-box">
                    <b class="tw-5 fit-tc-primary mg-r4 font-size-14">
                      {{ format(tyjBonus, 4, true, true) }}
                    </b>
                    <span class="font-size-14 fit-tc-secondary tw-4">USDT</span>
                  </div>
                </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="orderform-btn-box flex-box space-between">
    <template v-if="isLogin">
      <div class="flex-1">
        <el-button type="primary" class="rise-btn" @click="showTrade('buy')">{{ $t('开多') }}</el-button>
      </div>
      <div class="flex-1">
        <el-button type="primary" class="fall-btn" @click="showTrade('sell')">{{ $t('开空') }}</el-button>
      </div>
    </template>
    <div v-else class="no-login-btn flex-1">
      <NoLoginCont />
    </div>
  </div>
  <TransferDialog v-if="isShowTransfer" :visibleDialog="isShowTransfer" @close="isShowTransfer = false" />
  <CollateralDialog v-if="isShowCollateral" :isShowCollateral="isShowCollateral" :dataAssetsList="collatralListAssets" @close="isShowCollateral = false" />
</template>
<script lang="ts" setup>
  import BigNumber from 'bignumber.js'
  import TransferDialog from '~/components/common/TransferDialog.vue'
  import { format } from '~/utils'
  import { ElTooltip } from 'element-plus'
  import { cookies } from '~/utils/cookies'
  import MonoWarn from '~/components/common/icon-svg/MonoWarn.vue'
  import MonoSetting from '~/components/common/icon-svg/MonoSetting.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import MonoAssetsRansferIcon from '~/components/common/icon-svg/MonoAssetsRansferIcon.vue'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import FuturesTradeForm from '~/components/future/orderForm/FuturesTradeForm.vue'
  import CollateralDialog from '~/components/future/orderForm/CollateralDialog.vue'
  import { futureStore } from '~/stores/futureStore'
  import { useCommonData } from '~/composables/index'
  import { commonStore } from '~/stores/commonStore'
  const publicStore = commonStore()
  const { COLLATERALSymbol } = storeToRefs(publicStore)
  const store = futureStore()
  const { setModeInfoState, getPositionMode, subFutureInfoSocket } = store
  const { mode, leverage, cbuPositions, userRate, futureInfo, positionMode } = storeToRefs(store)
  const useCommon = useCommonData()
  const router = useRouter()
  const { locale, t } = useI18n()
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    price: {
      type: String,
      default: ''
    },
    amount: {
      type: [Number, String],
      default: ''
    },
    pairInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    futuresType: {
      type: String,
      default: ''
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    isLogin: {
      type: Boolean,
      default: false
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    },
    tradeAssetObj: {
      type: Object,
      default () {
        return {}
      }
    },
    tradeArr: {
      type: Array,
      default () {
        return []
      }
    },
    posMapObj: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const symbolLast = ref({})
  const emit = defineEmits(['updateAssets'])
  const isShowTransfer = ref(false)
  const isShowSetting = ref(false)
  const isShowTrade = ref(false)
  const type = ref('')
  const isShowcollateral = computed(() => {
    return true
    // return props.tradeArr.filter((item) => {
    //   return item.asset === 'USDT'
    // })[0].collateral
  })
  const currencySymbol = computed(() => {
    return props.pair.split('_')[1]
  })
  const coinSymbol = computed(() => {
    return props.pair.split('_')[0]
  })
  const unprofit = computed(() => { // 未实现盈亏
    let num = 0
    let longNum = 0
    let shortNum = 0
    const posTs = cbuPositions.value
    posTs && posTs.forEach(v => {
      const markPrice = ((futureInfo.value[v.symbol] || {}).markPrice || 0)
      console.log((futureInfo.value[v.symbol] || {}).markPrice, v.symbol, 'dhedhudedjiejdijeijeiij1')
      if (Number(v.quantity) > 0) {
        // 多仓(标记价格-开仓均价)*持仓量
        longNum += markPrice > 0 ? Number(new BigNumber(new BigNumber(markPrice).minus(v.entryPrice)).times(Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity))) : 0
      } else if (Number(v.quantity) < 0) {
        // 空仓(开仓均价-标记价格)*持仓量
        shortNum += markPrice > 0 ? Number(new BigNumber(new BigNumber(v.entryPrice).minus(markPrice)).times(Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity))) : 0
      }
    })
    console.log(longNum, shortNum, 'dhedhudedjiejdijeijeiij1')
    num = new BigNumber(longNum).plus(shortNum)
    return Number(num)
  })
  const crossUnprofit = computed(() => { // 全仓未实现盈亏
    const posTs = cbuPositions.value
    let Unprofit = 0
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') {
        if (Number(v.quantity) < 0) {
          Unprofit += ((-v.quantity * v.entryPrice) - (-v.quantity * (futureInfo.value[v.symbol] || {}).markPrice)) || 0
        } else {
          Unprofit += ((v.quantity * (futureInfo.value[v.symbol] || {}).markPrice) - (v.quantity * v.entryPrice)) || 0
        }
      }
    })
    return Unprofit
  })
  const cbcTotalBalance = computed(() => {
    const banlance = props.tradeAssetObj && props.tradeAssetObj[coinSymbol.value] && props.tradeAssetObj[coinSymbol.value].withdrawable || 0
    return new BigNumber(banlance * 1).plus(unprofit.value * 1)
  })
  const leftMaintain = computed(() => { // 保证金金额
    // (可用余额 + ∑所有全仓仓位持仓保证金 + ∑所有全仓仓位未实现盈亏）
    let mg = 0
    const banlance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].withdrawable || 0
    const posTs = cbuPositions.value
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') {
        mg += v.posMargin * 1
      }
    })
    return new BigNumber(banlance * 1).plus(mg * 1).plus(crossUnprofit.value * 1)
  })
  const cbuAvailableBalance = computed(() => {
    if ((mode.value[props.pair] * 1 === 1 || mode.value[props.pair] * 1 === 3)) {
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].balance || 0
      let num = 0
      if (props.tradeArr.length > 0) {
        props.tradeArr.forEach((item) => {
          if (item.collateral && item.asset !== 'USDT' && item.asset !== 'KtxQ1' ) {
            num += item.balance * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
          }
        })
      }
      console.log('withdrawable: ' + balance, 'collateral' + num, 'crossUnprofit: ' + crossUnprofit.value, 'crossMargin: ' + (props.posMapObj[currencySymbol.value] || {}).crossMargin, 'dhduehduheudhuehduheuhdue')
      return Math.max(0, (balance * 1 + num * 1 + crossUnprofit.value - (props.posMapObj[currencySymbol.value] || {}).crossMargin * 1 ))
    } else {
      const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].withdrawable || 0
      return Math.max(0, balance * 1)
    }
  })
  const tradingBonusShow = computed(() => {
    let num = 0
    if (props.tradeArr.length > 0) {
      props.tradeArr.forEach((item) => {
        if (item.asset === 'KtxQ2') {
          num = item.total
        }
      })
    }
    return num
  })
  const tradingBonus = computed(() => {
    let addVal = 0
    if (props.tradeArr.length > 0) {
      props.tradeArr.forEach((item) => {
        if (item.asset === 'KtxQ2') {
          addVal = Math.min(item.openRate * cbuAvailableBalance.value, item.total)
        }
      })
    }
    return addVal
  })
  const tyjBonus = computed(() => {
    let num = 0
    if (props.tradeArr.length > 0) {
      props.tradeArr.forEach((item) => {
        if (item.asset === 'KtxQ1') {
          num = item.total
        }
      })
    }
    return num
  })
  const totalAvailable = computed(() => {
    if ((mode.value[props.pair] * 1 === 1 || mode.value[props.pair] * 1 === 3)) {
      return new BigNumber(tradingBonus.value).plus(tyjBonus.value).plus(cbuAvailableBalance.value)
    } else {
      return cbuAvailableBalance.value
    }
  })
  const cbuAvailableTotal = computed(() => {
    const balance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].total || 0
    if (props.tradeArr.length > 0) {
      let num = 0
      props.tradeArr.forEach((item) => {
        if (item.collateral && item.asset !== 'USDT' && item.asset !== 'KtxQ1') {
          num += item.total * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
        }
      })
      return Math.max(0, (balance * 1 + num * 1))
    }
    return Math.max(0, balance * 1)
  })
  const cbuTotalBalance = computed(() => {
    const banlance = cbuAvailableTotal.value || 0
    if ((mode.value[props.pair] * 1 === 1 || mode.value[props.pair] * 1 === 3)) {
      return Math.max(0, (new BigNumber(banlance * 1).plus(unprofit.value * 1).plus(tradingBonusShow.value * 1).plus(tyjBonus.value * 1)))
    } else {
      return Math.max(0, (new BigNumber(banlance * 1).plus(unprofit.value * 1)))
    }
  })
  const cbcAvailableBalance = computed(() => {
    return 0
  })
  const cbuMt = computed(() => {
    let mt = 0 // 持仓维持保障金
    const posTs = cbuPositions.value
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') { // 全仓
        // mt = 持仓数量*开仓均价*维持保证金率
        const quantity = Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity)
        const t = userRate.value[props.pair] && userRate.value[props.pair].taker_fee || 0 // (this.cbuUserRate[v.pi] && this.cbuUserRate[v.pi].taker_fee) * 1
        mt += quantity * v.entryPrice * v.maintMargin
      }
    })
    return mt
  })
  const persentRate = computed(() => {
    /*
    风险率= ∑所有全仓仓位维持保证金/ (可用余额 + ∑所有全仓仓位持仓保证金 + ∑所有全仓仓位未实现盈亏）*100%
    */
    const mt = cbuMt.value // 持仓维持保障金
    return Math.max(0, new BigNumber(mt * 1).dividedBy(leftMaintain.value).multipliedBy(100).toFixed(2, 1))
  })
  const riskRotate = computed(() => {
    const MAX = 100
    const MIN = 0
    const MAX_ANGLE = 112
    const singlePercentRotate = MAX_ANGLE / (MAX - MIN)
    let result = (persentRate.value - MIN) * singlePercentRotate - (MAX_ANGLE / 2)
    if (result > (MAX_ANGLE / 2)) {
      result = MAX_ANGLE / 2
    } else if (result < (MAX_ANGLE / -2)) {
      result = MAX_ANGLE / -2
    }
    return result
  })
  const curPositions = computed(() => {
    return cbuPositions.value.filter((v) => {
      return v.symbol === props.pair
    })
  })
  const canCloseBuyNum = computed(() => {
    let crossAndBuyNum = 0 // 全并空
    let crossOrBuyNum = 0 // 全分空
    let isolateAndBuyNum = 0 // 逐并空
    let isolateOrBuyNum = 0 // 逐并空
    curPositions.value.forEach((v) => {
      const closableQty = Number(v.closableQty) < 0 ? -Number(v.closableQty) : Number(v.closableQty)
      const isAdd = v.mergeMode === 'long' || v.mergeMode === 'short'
      const isOr = v.mergeMode === 'none'
      const isShort = Number(v.closableQty) < 0
      if (v.marginMethod === 'cross' && isAdd && isShort) { // 全并空
        crossAndBuyNum += closableQty
      } else if (v.marginMethod === 'cross' && isOr && isShort) { // 全分空
        crossOrBuyNum += closableQty
      }  else if (v.marginMethod === 'isolate' && isAdd && isShort) { // 逐并空
        isolateAndBuyNum += closableQty
      } else if (v.marginMethod === 'isolate' && isOr && isShort) { // 逐分空
        isolateOrBuyNum += closableQty
      }
    })
    return mode.value[props.pair] === 1 ? crossAndBuyNum : (mode.value[props.pair] === 2 ? isolateAndBuyNum : (mode.value[props.pair] === 3 ? crossOrBuyNum : isolateOrBuyNum))
  })
  const canCloseSellNum = computed(() => {
    let crossAndSellNum = 0 // 全并多
    let crossOrSellNum = 0 // 全分多
    let isolateAndSellNum = 0 // 逐并多
    let isolateOrSellNum = 0 // 逐分多
    curPositions.value.forEach((v) => {
      const closableQty = Number(v.closableQty) < 0 ? -Number(v.closableQty) : Number(v.closableQty)
      const isAdd = v.mergeMode === 'long' || v.mergeMode === 'short'
      const isOr = v.mergeMode === 'none'
      const isLong = Number(v.closableQty) > 0
      if (v.marginMethod === 'cross' && isAdd && isLong) { // 全并多
        crossAndSellNum += closableQty
      } else if (v.marginMethod === 'cross' && isOr && isLong) { // 全分多
        crossOrSellNum += closableQty
      } else if (v.marginMethod === 'isolate' && isAdd && isLong) { // 逐并多
        isolateAndSellNum += closableQty
      } else if (v.marginMethod === 'isolate' && isOr && isLong) { // 逐分多
        isolateOrSellNum += closableQty
      }
    })
    return mode.value[props.pair] === 1 ? crossAndSellNum : (mode.value[props.pair] === 2 ? isolateAndSellNum : (mode.value[props.pair] === 3 ? crossOrSellNum : isolateOrSellNum))
  })
  const setModeFun = async(pair) => {
    await getPositionMode()
    const modeTxt = positionMode.value && positionMode.value[pair] && positionMode.value[pair].current.split('_')[0]
    const mode = modeTxt === 'cross' ? 1 : 2
    const leverage = positionMode.value && positionMode.value[pair] && positionMode.value[pair][modeTxt] && positionMode.value[pair][modeTxt].long
    setModeInfoState(pair, mode, leverage)
    const modeInfo = cookies.get('modeInfo')[pair]
    if (!modeInfo) {
      setModeInfoState(pair, mode, leverage)
    } else {
      setModeInfoState(pair, modeInfo.mode, modeInfo.leverage)
    }
  }
  const isShowCollateral = ref(false)
  const collatralListAssets = computed(() => {
    return Object.values(props.tradeAssetObj).filter((item) => {
      return item.collateral && item.asset !== 'USDT' && item.asset !== 'KtxQ1' && item.asset !== 'KtxQ2'
    })
  })
  const collatralFun = () => {
    if (collatralListAssets.value.length > 0) {
      isShowCollateral.value = true
    } else {
      router.push(`/${locale.value}/my/assets/trade`)
    }
  }
  watch(() => props.isLogin, async(val) => {
    if (val && props.pair) {
      setModeFun(props.pair)
    }
  }, {
    immediate: true
  })
  watch(() => props.pair, (val) => {
    if (props.isLogin && val) {
      setModeFun(val)
    }
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  const showTrade = (typeP) => {
    isShowTrade.value = true
    type.value = typeP
  }
  const requestAnimationFrameInterval = ref(null)
  const socketDateAnimation = () => {
    symbolLast.value = COLLATERALSymbol.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onBeforeMount(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss" scoped>
  .risk-container {
    padding:4px 16px;
    .border-dotted {
      border-bottom: 1px dotted;
      @include border-color(tc-secondary);
    }
    .risk-chart {
      position: relative;
      z-index: 1;
      margin: 0 12px;
      display: flex;
      .chart-line {
        width: 64px;
        height: 27px;
      }
      .chart-point {
        position: absolute;
        z-index: 1;
        width: 40px;
        height: 40px;
        left: 50%;
        margin-left: -20px;
        top: 18px;
        transform: rotate(-56deg);
        transition: all 0.3s linear;
      }
    }
  }
  .assets-container {
    margin: 0 16px;
    padding-bottom:12px;
    .assets-wrapper {
      height: auto;
      overflow: hidden;
      .assets-title {
        h2 {
          font-size: 14px;
          padding: 0;
          margin: 0;
        }
      }
      ul {
        li {
          line-height: 24px;
          .left-text {
            @include border-color(tc-secondary);
          }
        }
      }
    }
  }
  .orderform-btn-box{
    display:none;
  }
  .orderform-container{
    animation-fill-mode:none !important;
  }
  @include mb {
    .orderform-btn-box{
      display:flex;
      padding:12px 6px;
      .no-login-btn{
        margin-top:0;
      }
      .flex-1{
        padding:0 6px;
      }
      .el-button{
        width:100%;
        height:40px;
        &.el-button--primary{
          color:#ffffff !important;
        }
        &.rise-btn{
          @include bg-color(rise);
          @include border-color(rise);
        }
        &.fall-btn{
          @include bg-color(fall);
          @include border-color(fall);
        }
      }
    }
    .orderform-container{
      animation-fill-mode: both !important;
    }
  }
</style>
<style lang="scss">
@import url('@/assets/style/animate.scss');
.orderform-container{
  height:100%;
  &.animate__animated{
    animation-duration: 0s;
  }
  &.animate__fadeOutDown{
    animation-name: fadeInUp;
  }
  .orderform-wrapper{
    height:100%;
    .orderform-title{
      height:46px;
      line-height:46px;
      border-bottom:1px solid;
      padding:0 16px;
      font-size:14px;
      @include border-color(border);
      @include color(tc-primary);
      svg{
        display:none;
        &.setting-cont{
          display:block;
          cursor:pointer;
        }
      }
    }
    .order-cont-box{
      height:calc(100% - 44px);
      overflow:hidden;
    }
    .orderform-cont-box{
      .orderform-tab{
        padding:12px 16px 0;
        display:flex;
        flex:1 1;
        li{
          height:40px;
          line-height:40px;
          text-align:center;
          border-radius:4px;
          flex:1;
          font-size:14px;
          cursor:pointer;
          @include bg-color(tc-tertiary);
          @include color(tc-button);
          &:first-child{
            margin-right:6px;
            &.active{
              @include bg-color(rise);
            }
          }
          &:last-child{
            margin-left:6px;
            &.active{
              @include bg-color(fall);
            }
          }
        }
      }
      .orderform-subTab{
        height:46px;
        line-height:46px;
        display:flex;
        padding:0 16px;
        border-bottom:1px solid;
        @include border-color(border);
        li{
          font-size:14px;
          margin-right:20px;
          cursor:pointer;
          height:46px;
          line-height:46px;
          position:relative;
          @include color(tc-secondary);
          &.active{
            @include color(tc-primary);
            &:after{
              display:block;
              content: '';
              position:absolute;
              bottom:0;
              left:50%;
              margin-left:-8px;
              width:16px;
              height:2px;
              @include bg-color(theme);
            }
          }
        }
      }
      .input-orderform-cont{
        padding:10px 16px 0;
        .banlance-box{
          font-size:14px;
          position:relative;
          .banlance-right{
            position:absolute;
            right:0;
            bottom:-30px;
            cursor:pointer;
            @include color(theme);
          }
        }
        .el-form{
          &.input-orderform-wrap{
            .el-form-item{
              margin-bottom:0;
              .number-cont{
                width:100%;
              }
              .el-form-item__label{
                font-size:14px;
                margin:0;
                display:block;
                width:100%;
                height:36px;
                line-height:36px;
                padding:0;
              }
              .el-input-group--append>.el-input__wrapper{
                border-radius:4px;
              }
              .el-input{
                .el-input__wrapper{
                  .el-input__inner{
                    height:42px !important;
                    padding-right:56px;
                  }
                }
                .el-input-group__append{
                  width:56px;
                  padding-left:12px;
                  border-left:1px solid;
                  @include border-color(border);
                }
              }
            }
            .el-button {
              &.input-btn, &.el-button--primary{
                margin-top:38px;
                width:100%;
                color:#ffffff !important;
                height:40px;
                &.rise{
                  @include bg-color(rise);
                  @include border-color(rise);
                }
                &.fall{
                  @include bg-color(fall);
                  @include border-color(fall);
                }
              }
            }
          }
        }
      }
    }
  }
}
.orderform-btn-box{
  display:none;
}
@include mb {
  .orderform-container{
    display:flex;
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    background-color: rgba(0,0,0,0.4);
    align-items: flex-end;
    z-index:1900 !important;
    &.animate__animated{
      animation-duration: 0s;
    }
    &.animate__fadeOutDown{
      animation-name: fadeOutDown;
    }
    .orderform-wrapper{
      width:100%;
      border-radius: 20px 20px 0px 0px;
      height:80%;
      @include bg-color(bg-primary);
      .orderform-title{
        svg{
          display:block;
          font-size:16px;
          @include color(tc-primary);
        }
      }
      .order-cont-box{
        overflow:auto;
      }
    }
  }
  .orderform-btn-box{
    display:flex;
    padding:12px 6px;
    .no-login-btn{
      margin-top:0;
    }
    .flex-1{
      padding:0 6px;
    }
    .el-button{
      width:100%;
      height:40px;
      &.el-button--primary{
        color:#ffffff !important;
      }
      &.rise-btn{
        @include bg-color(rise);
        @include border-color(rise);
      }
      &.fall-btn{
        @include bg-color(fall);
        @include border-color(fall);
      }
    }
  }
}
</style>
