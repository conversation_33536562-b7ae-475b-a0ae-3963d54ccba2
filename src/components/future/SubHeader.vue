<template>
  <div class="sub-header flex-box">
    <div class="header-left flex-box">
      <div class="flex-box">
        <BoxCoinIcon :icon="coinInfo.icon_url" class="icon-box"/>
        <ElPopover ref="popoverRef" trigger="click" popper-class="search-popover-cont-wrap" :width="460" popper-append-to-body>
          <template #reference>
            <div class="symbol-text cursor-pointer">
              <div class="symbol-title flex-box">
                <h2 style="white-space:nowrap;" class="flex-box">
                  {{ pair.replace('_', '').replace('_SWAP', '') }}
                  <span class="mobile-item font-size-12 fit-tc-secondary mg-l8">{{ $t('永续') }}</span>
                </h2>
                <div class="arrow-down-box flex-box space-center m">
                  <MonoDownArrowMin size="16" class="fit-tc-primary" />
                </div>
                <div class="arrow-down-box flex-box space-center pc">
                  <MonoDownArrowMin size="16" class="fit-tc-primary" style="display:block;margin:0 auto;" />
                </div>
              </div>
              <p>{{ $t('永续') }}</p>
            </div>
          </template>
          <ExchangeMarketsDropdown :pair="pair" :isFuture="true" :favoriteMap="favoriteMap" @selectCollect="selectCollect" @close="closePopover" @changePair="changePair" />
        </ElPopover>
        <div class="collect-box flex-box space-center mg-b20" @click.stop="selectCollect(pair, 'head')">
          <MonoCollected v-if="favoriteMap[pair]" size="16" class="fit-theme" />
          <MonoCollect v-else size="16" class="fit-tc-primary" />
        </div>
      </div>
      <div class="collect-box mobile-item flex-box space-center" @click.stop="selectCollect(pair, 'head')">
        <MonoCollected v-if="favoriteMap[pair]" size="16" class="fit-theme" />
        <MonoCollect v-else size="16" class="fit-tc-primary" />
      </div>
    </div>
    <div class="pc-item">
      <div class="def-li">
        <span :class="className((ticker || {}).change)">{{ format(((ticker || {}).last), priceScale, true) || '--' }}</span>
        <em>≈{{ useCommon.convert((ticker || {}).last, 'USDT') }}</em>
      </div> 
    </div>
    <BoxXOverflow class="pc-item">
      <div class="scroll-item">
        <span>{{ $t('24H涨跌') }}</span>
        <em :class="Number((ticker || {}).change) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(((ticker || {}).change * 100), 2, true)  || '--' }}%</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24H最高') }}</span>
        <em>{{ format((ticker || {}).high, priceScale, true) || '--' }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24H最低') }}</span>
        <em>{{ format((ticker || {}).low, priceScale, true) || '--' }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24H成交量') }}</span>
        <em>{{ format((Number((ticker || {}).volume) < 0 ? -Number((ticker || {}).volume) : Number((ticker || {}).volume)), quantityScale, true) || '--' }} {{ futuresType === 'lpc' ? pair.split('_')[0] : pair.split('_')[1] }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('24H成交额') }}</span>
        <em>{{ format((ticker || {}).amount, priceScale, true) || '--' }} {{ futuresType === 'lpc' ? pair.split('_')[1] : pair.split('_')[0] }}</em>
      </div>
      <div class="scroll-item">
        <span>{{ $t('资金费率') }}</span>
        <div class="flex-box">
          <em>{{ format(((futureInfo[pair] || {}).fundingRate * 100), 4, true) }}%</em>
          <em>({{ timeLeft.h }}:{{ timeLeft.m }}:{{ timeLeft.s }})</em>
        </div>
      </div>
      <div class="scroll-item">
        <span>{{ $t('预测资金费率') }}</span>
        <em>{{ format(((futureInfo[pair] || {}).predictedFundingRate * 100), 2, true) }}%</em>
      </div>
    </BoxXOverflow>
    <div class="header-right flex-box mobile-item align-start space-between">
      <div class="def-li mobile-item flex-box flex-column space-start align-start flex-1">
        <span :class="className((ticker || {}).change)">{{ format(((ticker || {}).last), priceScale, true) || '--' }}</span>
        <em>≈{{ useCommon.convert((ticker || {}).last, 'USDT') }}</em>
        <em :class="className((ticker || {}).change)">{{ format((ticker || {}).change * 100, 2, true) || '--' }} %</em>
      </div>
      <div class="flex-2">
        <div class="scroll-item">
          <span>{{ $t('24小时最高') }}</span>
          <em>{{ format((ticker || {}).high, priceScale, true) || '--' }}</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('24小时涨跌幅') }}</span>
          <em :class="Number((ticker || {}).change) < 0 ? 'fit-fall' : 'fit-rise' ">{{ format(((ticker || {}).change * 100), 2, true) || '--' }}%</em>
        </div>
         <div class="scroll-item">
          <span>{{ $t('24小时最低') }}</span>
          <em>{{ format((ticker || {}).low, priceScale, true) || '--' }}</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('24小时交易额(USDT)') }}</span>
          <em>{{ format(((ticker || {}).volume * (ticker || {}).last), quantityScale, true) || '--' }}</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('资金费率') }}</span>
          <em>{{ format(((futureInfo[pair] || {}).fundingRate * 100), 4, true) }}%</em>
          <em>({{ timeLeft.h }}:{{ timeLeft.m }}:{{ timeLeft.s }})</em>
        </div>
        <div class="scroll-item">
          <span>{{ $t('预测资金费率') }}</span>
          <em>{{ format(((futureInfo[pair] || {}).predictedFundingRate * 100), 2, true) }}%</em>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import { ElPopover } from 'element-plus'
  import MonoInfo from '~/components/common/icon-svg/MonoInfo.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
  import ExchangeMarketsDropdown from '~/components/exchange/ExchangeMarketsDropdown.vue'
  import { favoriteAddOrDel, getFavoriteList } from '~/api/public'
  import { useCommonData } from '~/composables/index'
  import { futureStore } from '~/stores/futureStore'
  import { getTimeLeft } from '~/utils'
  const store = futureStore()
  const { futureInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    futuresType: {
      type: String,
      default: ''
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    },
    ticker: {
      type: Object,
      default(){
        return {}
      }
    }
  })
  const emit = defineEmits(['changePair'])
  const changePair = (pair) => {
    emit('changePair', pair)
  }
  const className = (c) => {
    if (c * 1 < 0) {
      return 'fit-fall'
    } else {
      return 'fit-rise'
    }
  }
  const useCommon = useCommonData()
  const visible = ref(false)
  const isShowMenu = ref(false)
  const favoriteMap = ref({})
  const timeLeft = ref({
    d: '--',
    h: '--',
    m: '--',
    s: '--'
  })
  const timer = ref(null)
  const selectCollect = async(item, type='') => { // 取消或增加收藏
    const pair = type === 'head' ? item : item.pair
    const { data, error } = await favoriteAddOrDel({
      add_str: favoriteMap.value[pair] ? undefined : pair,
      del_str: favoriteMap.value[pair] ? pair : undefined
    })
    if (data) {
      useCommon.showMsg('success', favoriteMap.value[pair] ? t('取消收藏') : t('收藏成功'))
      await getCollectList()
    } else if (error.code * 1 === 2013) {
      useCommon.openLogin()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  // 获取collectList
  const getCollectList = async() => {
    const { data, error} = await getFavoriteList()
    if (data) {
      favoriteMap.value = {}
      data.forEach((item) => {
        favoriteMap.value[item.pair] = true
      })
    }
  }
  const popoverRef = ref(null)
  const closePopover = () => {
    if (popoverRef.value) {
      nextTick(() => {
        popoverRef.value.hide()
      })
    }
  }
  const getTimeLeftFun = () => {
    const time = (futureInfo.value[props.pair] || {}).nextFundingTime || 0
    if (time) {
      const startTime = time
      const result = startTime - Date.now()
      timeLeft.value = getTimeLeft(result)
    }
  }
  onMounted(() => {
    timer.value && clearInterval(timer.value)
    timer.value = setInterval(() => {
      getTimeLeftFun()
    }, 1000)
    getCollectList()
  })
</script>
<style lang="scss">
.pc{
  display:flex;
}
.m{
  display:none;
}
.el-popover{
  &.search-popover-cont-wrap{
    padding:0 !important;
    height:calc(100% - 206px);
    min-height:460px;
  }
}
@include mb {
  .pc{
    display:none;
  }
  .m{
    display:block;
  }
}
</style>
