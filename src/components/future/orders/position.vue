<template>
  <div class="ordersCont position">
    <div v-if="isLogin">
      <BoxLoading v-if="isLoadingS" />
      <el-table v-if="!isMobile && !isLoadingS" ref="tableRef" :data="cbuPositions" :row-class-name="({row}) => stopProfitLossLenMap[row.id] ? 'is-have-list' : ''" default-expand-all :height="340" class="position-table" style="width:100%;">
        <el-table-column type="expand" align="left" width="1px" fixed="left">
          <template #default="props">
            <stop-profit-loss
              :orders="orders"
              :scopeRow="props.row"
              @updateOrderLength="length => updateOrderLength(props.row.id, length)"
            />
          </template>
        </el-table-column>
        <template #empty>
          <BoxNoData :text="$t('暂无数据')" />
        </template>
        <el-table-column prop="pi" :label="$t('合约')" align="left" :width="160">
          <template #default="scope">
            <div class="fit-tc-primary border-left-cont" :class="{'fall': Number(scope.row.quantity) < 0, 'rise': Number(scope.row.quantity) >= 0}">
              <div class="flex-box">
                <span v-if="Number(scope.row.quantity) < 0" class="side-tag fit-fall">{{ $t('空') }}</span>
                <span v-else class="side-tag fit-rise">{{ $t('多') }}</span>
                <NuxtLink :to="`/${locale}/future/${scope.row.symbol}`" class="fit-tc-primary">
                  <span class="font-size-14 mg-l4">{{ scope.row.symbol.replace('_SWAP', '').replace('_', '') }}</span>
                </NuxtLink>
              </div>
              <div class="lever-block font-size-12 tw-5 mg-t2 cursor-pointer fit-tc-primary" @click="changeLevel(scope.row)">
                <!--<template v-if="scope.row.marginMethod === 'cross' && scope.row.mergeMode !== 'none'">{{ $t('全仓') }}</template>
                <template v-if="scope.row.marginMethod === 'isolate' && scope.row.mergeMode !== 'none'">{{ $t('逐仓') }}</template>
                <template v-if="scope.row.marginMethod === 'cross' && scope.row.mergeMode === 'none'">{{ $t('全仓') }}/{{ $t('分仓') }}</template>
                <template v-if="scope.row.marginMethod === 'isolate' && scope.row.mergeMode === 'none'">{{ $t('逐仓') }}/{{ $t('分仓') }}</template>-->
                {{ (Math.max(1, scope.row.leverage)).toFixed(0) }}x
                <MonoNomalEdit class="fit-tc-secondary mg-l4" :size="14" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="entryPrice" :label="$t('开仓均价')" align="right" min-width="100">
          <template #header>
            <el-popover placement="top" width="80" trigger="click" popper-class="tip-content">
              <template #reference>
                <p class="mg-tb0 inline-block cursor">
                  <span class="position-header-dashed">{{ $t('开仓均价') }}</span>
                </p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('开仓的成本价') }}</div>
              </div>
            </el-popover>
            <el-popover placement="top" trigger="click" popper-class="tip-content">
              <template #reference>
                <p class="mg-tb0 inline-block cursor">
                  <span class="position-header-dashed">{{ $t('标记价格') }}</span>
                  <i class="icon-my-USDT"></i>
                </p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('根据各大交易所加权平均算出的价格指数，强制减仓、结算、强制平仓等都采用标记价格') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <div>
              <span class="font-size-14 fit-tc-primary">{{ format(scope.row.entryPrice, 4, true) }}</span>
            </div>
            <div>
              <span class="font-size-14 fit-tc-primary">{{ format((futureInfo[scope.row.symbol] || {}).markPrice, 4, true) || '--' }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="entryPrice" :label="$t('标记价格')" align="right" min-width="110">
          <template #header>
            <el-popover placement="top" trigger="click" popper-class="tip-content">
              <template #reference>
                <p class="mg-tb0 inline-block cursor">
                  <span class="position-header-dashed">{{ $t('标记价格') }}</span>
                  <i class="icon-my-USDT"></i>
                </p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('根据各大交易所加权平均算出的价格指数，强制减仓、结算、强制平仓等都采用标记价格') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <span class="font-size-14 fit-tc-primary">{{ format((futureInfo[scope.row.symbol] || {}).markPrice, 4, true) || '--' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="pf" :label="$t('强平价格')" align="right" min-width="100">
          <template #header>
            <el-popover placement="top" width="80" trigger="click" popper-class="tip-content">
              <template  #reference>
                <p class="mg-tb0 inline-block cursor">
                  <span class="position-header-dashed">{{ $t('强平价格') }}</span>
                  <i class="icon-my-USDT"></i>
                </p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('系统接管仓位的价格') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <span
              class="font-size-14 fit-tc-primary"
            >{{ format(lPrice(scope.row), 4, true) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="maintMargin" :label="$t('保证金')" align="right" min-width="110">
          <template #header>
            <el-popover placement="top" width="80" trigger="click" popper-class="tip-content">
              <template #reference>
                <p class="mg-tb0 inline-block cursor">
                  <span class="position-header-dashed">{{ $t('保证金') }}</span>
                  <i class="icon-my-USDT"></i>
                </p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('当前仓位的保证金') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <span class="font-size-14 fit-tc-primary flex-box space-end">
              {{ format(scope.row.posMargin, 4, true) }}
              <MonoIsolateEdit v-if="scope.row.marginMethod === 'isolate'" class="fit-theme cursor-pointer mg-l4" size="14" @click="changeMargin(scope.row)" />
            </span>
            <div>
              <template v-if="scope.row.marginMethod === 'cross' && scope.row.mergeMode !== 'none'">{{ $t('全仓') }}</template>
              <template v-if="scope.row.marginMethod === 'isolate' && scope.row.mergeMode !== 'none'">{{ $t('逐仓') }}</template>
              <template v-if="scope.row.marginMethod === 'cross' && scope.row.mergeMode === 'none'">{{ $t('全仓') }}/{{ $t('分仓') }}</template>
              <template v-if="scope.row.marginMethod === 'isolate' && scope.row.mergeMode === 'none'">{{ $t('逐仓') }}/{{ $t('分仓') }}</template>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="pt" :label="$t('收益')" align="right" min-width="110">
          <template #header>
            <el-popover placement="top" width="80" trigger="click" popper-class="tip-content">
              <template #reference>
                <span class="position-header-dashed cursor">{{ $t('收益') }}</span>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('当前未平仓仓位的浮动盈亏') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="{row}">
            <span
              v-if="Number(row.quantity) < 0"
              slot-scope="{ row }"
              class="font-size-14 tw-4 fit-tc-secondary"
              :class="((-Number(row.quantity) * row.entryPrice) - (-Number(row.quantity) * (marketsObj[row.symbol] || {}).last)) < 0 ? 'fit-fall' : 'fit-rise'"
            >
              <div>{{ format(((-Number(row.quantity) * row.entryPrice) - (-Number(row.quantity) * (marketsObj[row.symbol] || {}).last)), 4, true) }} USDT</div>
              <div>({{ format((((-Number(row.quantity) * row.entryPrice) - (-Number(row.quantity) * (marketsObj[row.symbol] || {}).last)) / row.posMargin * 100), 2, true) }} %)</div>
            </span>
            <span
              v-else-if="(marketsObj[row.symbol] || {}).last !== '--'"
              slot-scope="{ row }"
              class="ts-14 tw-4 fit-tc-secondary"
              :class="((row.quantity * (marketsObj[row.symbol] || {}).last) - (row.quantity * row.entryPrice)) < 0 ? 'fit-fall' : 'fit-rise'"
            >
              <div>{{ format(((row.quantity * (marketsObj[row.symbol] || {}).last) - (row.quantity * row.entryPrice)), 4, true) }} USDT</div>
              <div>({{ format((((row.quantity * (marketsObj[row.symbol] || {}).last) - (row.quantity * row.entryPrice)) / row.posMargin * 100), 2, true) }} %)</div>
            </span>
            <span v-else class="tw-4 fit-tc-secondary">--</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('仓位｜可平')" align="right" min-width="110">
          <template #header>
            <el-popover placement="top" width="80" trigger="click" popper-class="tip-content">
              <template #reference>
                <p><span class="position-header-dashed cursor">{{ $t('仓位') }}</span></p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('当前持仓的仓位数量') }}</div>
              </div>
            </el-popover>
            <el-popover placement="top" trigger="click" popper-class="tip-content">
              <template #reference>
                <p><span class="position-header-dashed cursor">{{ $t('可平') }}</span></p>
              </template>
              <div class="font-size-12 fit-tc-primary">
                <div>{{ $t('当前仓位可平仓的数量（可平=持仓数量-当前委托冻结数量）') }}</div>
              </div>
            </el-popover>
          </template>
          <template #default="{row}">
            <div>{{ format(useCommon.cbuConver((Number(row.quantity) < 0 ? -Number(row.quantity) : Number(row.quantity)), isCBUUnitUSDT, row.entryPrice || (futureInfo[row.symbol] || {}).markPrice), isCBUUnitUSDT ? (pairInfo[row.symbol] || {}).price_scale : (pairInfo[row.symbol] || {}).quantity_scale, true) }}</div>
            <div>{{ format(useCommon.cbuConver((Number(row.closableQty) < 0 ? -Number(row.closableQty) : Number(row.closableQty)), isCBUUnitUSDT, row.entryPrice || (futureInfo[row.symbol] || {}).markPrice), isCBUUnitUSDT ? (pairInfo[row.symbol] || {}).price_scale : (pairInfo[row.symbol] || {}).quantity_scale, true) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="lc" :label="$t('平仓价格')" align="right" width="104">
          <template #default="{ row, $index }">
            <el-input v-model="row.order_price" type="number" :decimal="(pairInfo[row.symbol] || {}).price_scale" @focus="focusItem($index)" class="small-position-input" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('平仓数量')" align="left" width="220">
          <template #header>
            <span class="fit-tc-secondary font-size-12">{{ $t('数量') }}</span>
          </template>
          <template #default="{ row, $index }">
            <div class="tw-4 flex-box fit-tc-secondary space-end">
              <el-input v-model="row.order_amount" clearable :decimal="(pairInfo[row.symbol] || {}).quantity_scale" @focus="focusItemAmount($index)" class="small-position-input mg-r8">
                <template #suffix>
                  <el-dropdown size="small" @command="v => choisePercent(row, v)">
                    <MonoDownArrowMin size="14" class="fit-tc-primary cursor-pointer" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item v-for="(v, i) in positionPercent" :key="i" :command="v" style="width: 100px;" class="pd-lr8 pd-tb4 item cursor-pointer ts-12">{{ v.label }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-input>
              <el-button
                type="primary"
                class="position-btn mg-r8"
                :class="Number(row.quantity) < 0 ? 'rise-btn' : 'fall-btn'"
                size="small"
                @click="closePosition(row, $index)"
              >{{ Number(row.quantity) < 0 ? $t('平空') : $t('平多') }}</el-button>
              <MonoFastIcon class="fast-icon-btn mg-r8"  @click="fastFinish(row, $index)" />
              <!-- <MonoReserveIcon class="reserve-icon-btn" /> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('止盈止损')" align="right" :min-width="(locale === 'zh' || locale === 'ko') ? 96 : locale === 'ru' ? 150 : 104">
          <template #default="scope">
            <span class="fit-theme cursor-pointer" @click="checkOrder(scope.row)">{{ $t('止盈止损') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="isMobile && !isLoadingS" class="position-m-cont">
        <div v-if="cbuPositions.length > 0" class="position-m-wrap">
          <div v-for="(item, index) in cbuPositions" :key="index" class="position-list-cont">
            <div class="symbol-cont flex-box font-size-18">
              <div class="fit-tc-primary flex-box">
                <span v-if="Number(item.quantity) < 0" class="side-tag fit-fall">{{ $t('空') }}</span>
                <span v-else class="side-tag fit-rise">{{ $t('多') }}</span>
                {{ item.symbol.replace('_SWAP', '').replace('_', '') }}
              </div>
              <span class="lever-block font-size-12 tw-5 mg-l4" :class="{'fit-fall': Number(item.quantity) < 0, 'fit-rise': Number(item.quantity) > 0}" @click="changeLevel(item)">
                <template v-if="item.marginMethod === 'cross' && item.mergeMode !== 'none'">{{ $t('全仓') }}</template>
                <template v-if="item.marginMethod === 'isolate' && item.mergeMode !== 'none'">{{ $t('逐仓') }}</template>
                <template v-if="item.marginMethod === 'cross' && item.mergeMode === 'none'">{{ $t('全仓') }}/{{ $t('分仓') }}</template>
                <template v-if="item.marginMethod === 'isolate' && item.mergeMode === 'none'">{{ $t('逐仓') }}/{{ $t('分仓') }}</template>
                {{ (Math.max(1, item.leverage)).toFixed(0) }}x
                <MonoNomalEdit class="fit-tc-secondary mg-l4" :size="14" />
              </span>
            </div>
            <div class="symbol-info-box flex-box space-between">
              <div class="text-left">
                <p class="fit-tc-secondary">{{ $t('收益') }}(USDT)</p>
                <span 
                  v-if="Number(item.quantity) < 0"
                  :class="((-Number(item.quantity) * item.entryPrice) - (-Number(item.quantity) * (marketsObj[item.symbol] || {}).last)) < 0 ? 'fit-fall' : 'fit-rise'"
                  >
                  {{ format(((-Number(item.quantity) * item.entryPrice) - (-Number(item.quantity) * (marketsObj[item.symbol] || {}).last)), 4, true) }}
                </span>
                <span
                  v-else-if="(marketsObj[item.symbol] || {}).last !== '--'"
                  :class="((item.quantity * (marketsObj[item.symbol] || {}).last) - (item.quantity * item.entryPrice)) < 0 ? 'fit-fall' : 'fit-rise'"
                  >
                  {{ format(((item.quantity * (marketsObj[item.symbol] || {}).last) - (item.quantity * item.entryPrice)), 4, true) }}
                </span>
              </div>
              <div class="text-right">
                <p class="fit-tc-secondary">{{ $t('收益率') }}</p>
                <span 
                  v-if="Number(item.quantity) < 0"
                  :class="((-Number(item.quantity) * item.entryPrice) - (-Number(item.quantity) * (marketsObj[item.symbol] || {}).last)) < 0 ? 'fit-fall' : 'fit-rise'"
                  >
                  {{ format((((-Number(item.quantity) * item.entryPrice) - (-Number(item.quantity) * (marketsObj[item.symbol] || {}).last)) / item.posMargin * 100), 2, true) }} %
                </span>
                <span
                  v-else-if="(marketsObj[item.symbol] || {}).last !== '--'"
                  :class="((item.quantity * (marketsObj[item.symbol] || {}).last) - (item.quantity * item.entryPrice)) < 0 ? 'fit-fall' : 'fit-rise'"
                  >
                  {{ format((((item.quantity * (marketsObj[item.symbol] || {}).last) - (item.quantity * item.entryPrice)) / item.posMargin * 100), 2, true) }} %
                </span>
              </div>
            </div>
            <div class="symbol-info-box flex-box space-between">
              <div class="text-left">
                <p class="fit-tc-secondary">{{ $t('仓位') }}({{ useCommon.getFuturesSymbol(item.symbol, isCBUUnitUSDT) }})</p>
                <span>{{ format(useCommon.cbuConver((Number(item.quantity) < 0 ? -Number(item.quantity) : Number(item.quantity)), isCBUUnitUSDT, (futureInfo[item.symbol] || {}).markPrice), !isCBUUnitUSDT ? (pairInfo[item.symbol] || {}).quantity_scale : (pairInfo[item.symbol] || {}).price_scale, true) }}</span>
              </div>
              <div class="text-left">
                <p class="fit-tc-secondary">{{ $t('保证金') }}({{ item.symbol.split('_')[0] }})</p>
                <span class="font-size-14 fit-tc-primary">
                  {{ format(item.posMargin, 4, true) }}
                  <MonoIsolateEdit v-if="item.marginMethod === 'isolate'" class="fit-theme cursor-pointer mg-l4" size="14" @click="changeMargin(item)" />
                </span>
              </div>
              <div class="text-right">
                <p class="fit-tc-secondary">{{ $t('标记价格') }}</p>
                <span>{{ format((futureInfo[item.symbol] || {}).markPrice, 4, true) || '--' }}</span>
              </div>
            </div>
            <div class="symbol-info-box flex-box space-between">
              <div class="text-left">
                <p class="fit-tc-secondary">{{ $t('开仓均价') }}({{ item.symbol.split('_')[1] }})</p>
                <span>{{ format(item.entryPrice, 4, true) }}</span>
              </div>
              <div class="text-left">
                <p class="fit-tc-secondary">{{ $t('强平价格') }}({{ item.symbol.split('_')[1] }})</p>
                <span>{{ format(lPrice(item), 4, true) }}</span>
              </div>
              <div class="text-right">
                <p class="fit-tc-secondary">{{ $t('可平仓位') }}({{ useCommon.getFuturesSymbol(item.symbol, isCBUUnitUSDT) }})</p>
                <span>{{ format(useCommon.cbuConver((Number(item.closableQty) < 0 ? -Number(item.closableQty) : Number(item.closableQty)), isCBUUnitUSDT, item.entryPrice || (futureInfo[item.symbol] || {}).markPrice), !isCBUUnitUSDT ? (pairInfo[item.symbol] || {}).quantity_scale : (pairInfo[item.symbol] || {}).price_scale, true) }}</span>
              </div>
            </div>
            <div class="position-btn-box flex-box">
              <div class="pos-btn" @click="checkOrder(item)">{{ $t('止盈止损') }}</div>
              <div class="pos-btn mg-lr12" @click="fastFinish(item, index)">{{ $t('闪电平仓') }}</div>
              <div class="pos-btn" @click="limitCloseOrder(item)">{{ $t('平仓') }}</div>
            </div>
            <stop-profit-loss
              :orders="orders"
              :scopeRow="item"
            />
          </div>
        </div>
        <div style="height:320px;" v-else>
        <BoxNoData :text="$t('暂无数据')" />
        </div>
      </div>
    </div>
    <div v-else class="no-login-cont-box">
      <NoLoginCont />
    </div>
  </div>
  <ChangeLevelageDialog 
    v-if="isShowLevelDialog"
    :isShow="isShowLevelDialog"
    :priceScale="priceScale"
    :tradeAssetObj="tradeAssetObj"
    :tradeArr="tradeArr"
    :quantityScale="quantityScale"
    :cbuPositions="cbuPositions"
    :data="checkData"
    :ticker="marketsObj[checkData.symbol]"
    :marketPrice="(futureInfo[checkData.symbol] || {}).markPrice"
    @close="isShowLevelDialog = false"
  />
  <ChangeMarginDialog
    v-if="isChangeMargin"
    :isShow="isChangeMargin"
    :data="checkData"
    :curLprice="curLprice"
    :takerFee="curTakeFee"
    @closeDialog="isChangeMargin = false"
  />
  <StopProfitDialog
    v-if="isCheckfullstop"
    :isShow="isCheckfullstop"
    :data="checkData"
    :coinInfo="coinInfo"
    :isCBCUnitUSD="isCBCUnitUSD"
    :isCBUUnitUSDT="isCBUUnitUSDT"
    @close="isCheckfullstop = false"
  />
  <LimitCloseDialog
    v-if="isShowLimitClose"
    :isShow="isShowLimitClose"
    :data="curDataItem"
    :isCBUUnitUSDT="isCBUUnitUSDT"
    :last="(futureInfo[curDataItem.symbol] || {}).markPrice"
    :futuresType="futuresType"
    @close="isShowLimitClose = false"
    @closePosition="closePositionM"
  />
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import BigNumber from 'bignumber.js'
  import { ElTable, ElTableColumn, ElPopover, ElInput, ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage, ElMessageBox } from 'element-plus'
  import NoLoginCont from '~/components/common/NoLoginCont.vue'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import MonoIsolateEdit from '~/components/common/icon-svg/MonoIsolateEdit.vue'
  import ChangeMarginDialog from '~/components/future/orders/ChangeMarginDialog.vue'
  import MonoNomalEdit from '~/components/common/icon-svg/MonoNormalEdit.vue'
  import MonoFastIcon from '~/components/common/icon-svg/MonoFastIcon.vue'
  import MonoReserveIcon from '~/components/common/icon-svg/MonoReserveIcon.vue'
  import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
  import StopProfitLoss from '~/components/future/orders/stop-profit-loss.vue'
  import StopProfitDialog from '~/components/future/orders/StopProfitDialog.vue'
  import LimitCloseDialog from '~/components/future/orders/LimitCloseDialog.vue'
  import ChangeLevelageDialog from '~/components/future/orders/ChangeLevelageDialog.vue'
  import { useCommonData } from '~/composables/index'
  import { futureStore } from '~/stores/futureStore'
  import { openOrder, getCurrenOrderList, fastOrderOpen } from '~/api/order'
  import { commonStore } from '~/stores/commonStore'
  const store = futureStore()
  const { userRate, futureInfo } = storeToRefs(store)
  const publicStore = commonStore()
  const { isChangeFutureOrder, COLLATERALSymbol, pairInfo, marketsObj } = storeToRefs(publicStore)
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    isLogin: {
      type: Boolean,
      default: false
    },
    price: {
      type: String,
      default: ''
    },
    amount: {
      type: [Number, String],
      default: ''
    },
    coinInfo: {
      type: Object,
      default () {
        return {}
      }
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    isShowOnlyPair: {
      type: Boolean,
      default: false
    },
    pair: {
      type: String,
      default: ''
    },
    cbuPositions: {
      type: Array,
      default () {
        return []
      }
    },
    curPosition: {
      type: Array,
      default () {
        return []
      }
    },
    tradeArr: {
      type: Array,
      default () {
        return []
      }
    },
    tradeAssetObj: {
      type:Object,
      default(){
        return{}
      }
    },
    futuresType: {
      type: String,
      default: ''
    },
    ticker: {
      type: Object,
      default () {
        return {}
      }
    },
    isCBCUnitUSD: {
      type: Boolean,
      default: false
    },
    isCBUUnitUSDT: {
      type: Boolean,
      default: false
    },
    quantityScale: {
      type: [String, Number],
      default: 0
    },
    priceScale: {
      type: [String, Number],
      default: 0
    }
  })
  const emit = defineEmits(['getPositions'])
  const symbolLast = ref({})
  const isShowLimitClose = ref(false)
  const curDataItem = ref({})
  const positionPercent = computed(() => {
    return [
      {
        label: '10%',
        type: 0.1
      },
      {
        label: '20%',
        type: 0.2
      },
      {
        label: '50%',
        type: 0.5
      },
      {
        label: '100%',
        type: 1
      }
    ]
  })
  const checkData = ref({})
  const isChangeMargin = ref(false)
  const focusedIndex = ref(null)
  const focusItem = (index) => {
    focusedIndex.value = index
  }
  const focusedAmountIndex = ref(null)
  const focusItemAmount = (index) => {
    focusedAmountIndex.value = index
  }
  watch(() => props.price, (val) => {
    if (focusedIndex.value !== null) {
      props.cbuPositions[focusedIndex.value].order_price = val
      // focusedIndex.value = null
    }
  })
  watch(() => props.amount, (val) => {
    if (focusedAmountIndex.value !== null) {
      props.cbuPositions[focusedAmountIndex.value].order_amount = val
      // focusedAmountIndex.value = null
    }
  })
  const currencySymbol = computed(() => {
    return props.pair.split('_')[1]
  })
  const coinSymbol = computed(() => {
    return props.pair.split('_')[0]
  })
  const canUseBanlance = computed(() => { // 可用保证金
    const banlance = props.tradeAssetObj && props.tradeAssetObj[currencySymbol.value] && props.tradeAssetObj[currencySymbol.value].balance || 0
    let num = 0
    if (props.tradeArr.length > 0) {
      props.tradeArr.forEach((item) => {
        if (item.collateral && item.asset !== 'USDT') {
          num += item.balance * item.discountForMargin * (symbolLast.value && (symbolLast.value[item.asset] || {}).last || 1)
        }
      })
    }
    console.log(banlance, num, Math.max(0, (banlance * 1 + num * 1)), 'deidjeijdiejdiejiejie')
    return Math.max(0, (banlance * 1 + num * 1))
  })
  const MaintainAll = computed(() => { // 可用保证金 + 全仓持仓保证金
    let mg = 0
    const posTs = props.cbuPositions
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') {
        mg += v.posMargin * 1
      }
    })
    // new BigNumber(canUseBanlance.value * 1).plus(mg * 1)
    return new BigNumber(canUseBanlance.value * 1)
  })
  const cbuMt = computed(() => {
    let mt = 0 // 持仓维持保障金
    const posTs = props.cbuPositions
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') { // 全仓
        let fundNumber = 0
        // mt = 持仓数量*开仓均价*维持保证金率+相同方向的资金费用累加
        console.log((futureInfo.value[v.symbol] || {}).fundingRate, Math.sign(v.quantity) === Math.sign((futureInfo.value[v.symbol] || {}).fundingRate), 'djejidjeidjiejeijie')
        const quantity = Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity)
        if (Math.sign(v.quantity) === Math.sign((futureInfo.value[v.symbol] || {}).fundingRate)) {
          fundNumber += Number(v.entryPrice) * quantity * Math.abs((futureInfo.value[v.symbol] || {}).fundingRate)
        }
        mt += quantity * v.entryPrice * v.maintMargin + fundNumber
      }
    })
    return mt
  })
  const takeFeeCross = computed(() => {
    let takerFee = 0 // 持仓维持保障金
    const posTs = props.cbuPositions
    posTs && posTs.forEach(v => {
      if (v.marginMethod === 'cross') { // 全仓
        takerFee += v.entryPrice * (Number(v.quantity) < 0 ? -Number(v.quantity) : Number(v.quantity)) * (userRate.value[props.pair] && userRate.value[props.pair].taker_fee) || 0
      }
    })
    return takerFee
  })
  const lPrice = (row) => {
    // 逐仓多仓：开仓均价-(持仓保证金-维持保证金)/持仓量
    // 逐仓空仓：开仓均价+(持仓保证金-维持保证金)/持仓量
    // 全仓多仓：开仓均价-(可用保证金 + 全仓持仓保证金 + 全仓未实现盈亏 + 其他交易对的全仓未实现盈亏 - 全仓维持保证金)/净持仓量
    let fundNumber = 0
    props.cbuPositions.forEach((item) => {
      if (item.symbol === row.symbol && Math.sign(row.quantity) === Math.sign((futureInfo.value[item.symbol] || {}).fundingRate)) {
        fundNumber += Number(item.entryPrice) * Math.abs(item.quantity) * Math.abs((futureInfo.value[item.symbol] || {}).fundingRate)
      }
    })
    const symbol = row.symbol
    const entryPrice = row.entryPrice || 0 // 逐仓开仓均价
    let entryPriceCross = 0
    const posMargin = row.posMargin // 持仓保证金
    const quantity = Number(row.quantity) < 0 ? -Number(row.quantity) : Number(row.quantity) // 持仓量
    const fundValue = fundNumber || 0 // 相同方向的资金费用累加
    const maintMargin = row.entryPrice * quantity * row.maintMargin + fundValue || 0 // 维持保证金 = 开仓均价*持仓数量*维持保证率+相同方向的资金费用累加
    // console.log(maintMargin, 'fundValuefundValuefundValuefundValue')
    const takerFee = row.entryPrice * quantity * (userRate.value[row.symbol] && userRate.value[row.symbol].taker_fee) || 0 // 手续费
    let closableQty = 0 // 净持仓量
    let longEntryPrice = 0 // 多开仓均价
    let longQuantity = 0 // 多仓的数量总和
    let shortEntryPrice = 0 // 空开仓均价
    let shortQuantity = 0 // 多仓的数量总和
    let Hl = 0 // 持有多仓
    let Hs = 0 // 持有空仓
    let Pl = 0 // 多仓开仓均价
    let Ps = 0 // 空仓开仓均价
    let otherCrossUnfrite = 0 // 其他交易对的全仓未实现盈亏
    props.cbuPositions.forEach((item) => {
      if (item.symbol === row.symbol) {
        closableQty += Number(item.closableQty)
        if (Number(item.quantity) < 0 && item.marginMethod === 'cross') {
          shortEntryPrice += (Number(item.entryPrice) * Number(item.quantity)) || 0
          shortQuantity += Number(item.quantity) || 0
          Hs += Math.abs(item.quantity) * 1 || 0
          Ps += item.entryPrice * 1 || 0
        } else if (Number(item.quantity) > 0 && item.marginMethod === 'cross') {
          longEntryPrice += (Number(item.entryPrice) * -Number(item.quantity)) || 0
          longQuantity += -Number(item.quantity) || 0
          Hl += Math.abs(item.quantity)
          Pl += item.entryPrice * 1 || 0
        }
      } else {
        if (item.marginMethod === 'cross') {
          if (Number(item.quantity) < 0) {
            otherCrossUnfrite += (Math.abs(item.quantity) * item.entryPrice) - (Math.abs(item.quantity) * (futureInfo.value[item.symbol] || {}).markPrice)
          } else {
            otherCrossUnfrite += (Math.abs(item.quantity) * (futureInfo.value[item.symbol] || {}).markPrice) - (item.quantity * item.entryPrice)
          }
        }
      }
    })
    const curUprifit = Math.min(Hl, Hs) * (Ps - Pl)
    const cwf = new BigNumber(new BigNumber(posMargin).minus(maintMargin)).div(quantity)// 持仓保证金-维持保证金
    const endLongEntryPrice = longEntryPrice / longQuantity
    const endShortEntryPrice = shortEntryPrice / shortQuantity
    entryPriceCross = closableQty < 0 ? endShortEntryPrice : endLongEntryPrice
    let num = 0
    if (row.marginMethod === 'isolate' && Number(row.quantity) > 0) { // 逐仓多仓
      num = new BigNumber(entryPrice).minus(cwf)
    } else if (row.marginMethod === 'isolate' && Number(row.quantity) < 0) { // 逐仓空仓
      num = new BigNumber(entryPrice).plus(cwf)
    } else if (row.marginMethod === 'cross') { // 全仓
      num = new BigNumber(entryPriceCross).minus(new BigNumber(new BigNumber(MaintainAll.value).plus(curUprifit).plus(otherCrossUnfrite).minus(cbuMt.value)).div(closableQty))
    }
    return num < 0 || !num ? 0 : num
  }
  const tableRef = ref(null)
  const stopProfitLossLenMap = ref({})
  const updateOrderLength = (id, length) => {
    stopProfitLossLenMap.value[id] = length
    nextTick(() => {
      tableRef.value.doLayout()
    })
  }
  const choisePercent = (data, v) => {
    data.order_percent = v.type
    data.order_amount = v.label
  }
  const generateDecimal = (precision) => {
    return Math.pow(10, -precision);
  }
  const validata = (row, orderAmount) => {
    if (!row.order_price) {
      useCommon.showMsg('error', t('请输入平仓价格'))
      return false
    }
    const val = pairInfo.value && pairInfo.value[row.symbol].min_order_size || 0
    if (!row.order_amount) {
      useCommon.showMsg('error', t('请输入平仓数量'))
      return false
    }
    const precision = pairInfo.value && pairInfo.value[row.symbol].quantity_scale || 0
    if (orderAmount * 1 < generateDecimal(precision) * 1) {
      useCommon.showMsg('error', t('最小') + ' ' + generateDecimal(precision))
      return false
    }
    return true
  }
  const closePosition = (row, index) => { // 平多平空
    const calculateAmount = row.order_percent ? new BigNumber(Number(row.closableQty) < 0 ? -Number(row.closableQty) : Number(row.closableQty)).multipliedBy(row.order_percent) : row.order_amount
    const amount = props.isCBUUnitUSDT && !row.order_percent ? new BigNumber(calculateAmount).dividedBy((futureInfo.value[row.symbol] || {}).markPrice).toFixed((pairInfo.value[row.symbol] || {}).price_scale) : calculateAmount
    if (!validata(row, Number(amount))) {
      return false
    }
    const params = {
      positionId: row.id,
      symbol: row.symbol,
      quantity: Number(row.quantity) > 0 ? format(new BigNumber(-amount), (pairInfo.value[row.symbol] || {}).quantity_scale, 1).replace(/,/g, '') : format(new BigNumber(amount), (pairInfo.value[row.symbol] || {}).quantity_scale, 1).replace(/,/g, ''),
      price: row.order_price,
      type: 'limit',
      market: props.futuresType,
      positionMerge: row.mergeMode,
      marginMethod: row.marginMethod,
      leverage: Number(row.leverage),
      close: true
    }
    openOrderFun(params, index, row)
  }
  const closePositionM = (params) => {
    openOrderFun(params)
  }
  const fastOpen = async(row, instance, done) => {
    const { data, error } = await fastOrderOpen({
      pair: row.symbol,
      position_id: row.id,
      quantity: row.quantity
    })
    if (data) {
      useCommon.showMsg('success', t('下单成功'))
      instance.confirmButtonLoading = false
      done()
      setTimeout(() => {
        emit('getPositions')
      }, 1000)
    } else {
      instance.confirmButtonLoading = false
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const fastFinish = (row, index) => { // 闪电平仓
    if (row.closableQty !== row.quantity) {
      ElMessageBox({
        title: t('提示'),
        showCancelButton: true,
        confirmButtonText: t('确定'),
        cancelButtonText: t('取消'),
        message: t('是否撤销当前委托单，直接进行闪电平仓'),
        beforeClose: async(action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const params = {
              symbol: row.symbol,
              quantity: -Number(row.closableQty),
              type: 'market',
              market: props.futuresType,
              positionMerge: row.mergeMode,
              marginMethod: row.marginMethod,
              leverage: Number(row.leverage),
              close: true
            }
            await fastOpen(row, instance, done)
          } else {
            done()
          }
        }
      })
      // useCommon.showMsg('error', t('当前存在平仓委托单，请撤销后再使用闪电平仓功能'))
      return false
    }
    ElMessageBox({
      title: t('闪电平仓'),
      customClass: 'sd-message-dialog',
      showCancelButton: true,
      confirmButtonText: t('闪电平仓'),
      cancelButtonText: t('取消'),
      message: h('div', { style:'width:100%;' }, [
        h('div', { class: 'flex-box sd-title-box' }, [
          h('div', { class: 'flex-box li-box mg-r8' }, [
            h('span', { class: 'fit-tc-primary font-size-16 tw-7 mg-r4' }, row.symbol.replace('_SWAP', '').replace('_', '')),
            h('span', { class: 'fit-tc-secondary font-size-12' }, t('永续'))
          ]),
          h('div', { class: 'flex-box mt-bx' }, [
            h('div', { class: 'flex-box li-box mode-bx mg-r8' }, [
              h('span', { class: 'fit-tc-secondary font-size-14' }, `${row.marginMethod === 'cross' ? t('全仓') : t('逐仓')} ${Math.max(1, row.leverage).toFixed(0)}x`)
            ]),
            h('div', { class: `flex-box li-box side-bx ${Number(row.quantity) < 0 ? 'fit-rise' : 'fit-fall'}` }, [
              h('span', { class: `fit-tc-primary font-size-14 ${Number(row.quantity) < 0 ? 'fit-rise' : 'fit-fall'}` }, Number(row.quantity) < 0 ? t('平空') : t('平多'))
            ])
          ])
        ]),
        h('div', { class: 'flex-box sd-cont-box' }, [
          h('span', { class: 'fit-tc-primary font-size-14 mg-r8' }, t('可平仓数量')),
          h('span', { class: 'fit-tc-primary tw-7 font-size-14' }, `${format(useCommon.cbuConver((Number(row.closableQty) < 0 ? -Number(row.closableQty) : Number(row.closableQty)), props.isCBUUnitUSDT, (futureInfo.value[row.symbol] || {}).markPrice), (props.isCBUUnitUSDT ? (pairInfo.value[row.symbol] || {}).price_scale : (pairInfo.value[row.symbol] || {}).quantity_scale), true)} ${useCommon.getFuturesSymbol(row.symbol, props.futuresType === 'ipc' ? props.isCBCUnitUSD : props.isCBUUnitUSDT)}`)
        ])
      ]),
      beforeClose: async(action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          const params = {
            symbol: row.symbol,
            quantity: -Number(row.closableQty),
            type: 'market',
            market: props.futuresType,
            positionMerge: row.mergeMode,
            marginMethod: row.marginMethod,
            leverage: Number(row.leverage),
            close: true
          }
          await fastOpen(row, instance, done)
        } else {
          done()
        }
      }
    }).then(() => {
    })
  }
  const openOrderFun = async(params, index, row, instance, done) => {
    // props.cbuPositions[index] && props.cbuPositions[index]['isRowShowLoading'] = true
    // setTimeout(() => {
    //   props.cbuPositions[index] && props.cbuPositions[index]['isRowShowLoading'] = false
    // }, 10000)
    console.info(params, 'dhhdueuheuh')
    const { data, error } = await openOrder(params)
    if (data) {
      useCommon.showMsg('success', t('下单成功'))
      isShowLimitClose.value = false
      // props.cbuPositions[index] && props.cbuPositions[index]['isRowShowLoading'] = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      // props.cbuPositions[index] && props.cbuPositions[index]['isRowShowLoading'] = false
    }
  }
  const orders = ref([])
  const getOrderList = async () => {
    const { data } = await getCurrenOrderList({
      status: 'unsettled',
      market: 'lpc',
      type: '16,17,32,33'
    })
    if (data) {
      orders.value = data
    }
  }
  const curLprice = ref(0)
  const curTakeFee = ref(0)
  const changeMargin = (row) => {
    checkData.value = row
    const quantity = Number(row.quantity) < 0 ? -Number(row.quantity) : Number(row.quantity)
    curLprice.value = lPrice(row)
    curTakeFee.value = row.entryPrice * quantity * (userRate.value[row.symbol] && userRate.value[row.symbol].taker_fee) || 0 // 手续费
    isChangeMargin.value = true
  }
  const isCheckfullstop = ref(false)
  const checkOrder = (row) => {
    checkData.value = row
    isCheckfullstop.value = true
  }
  watch(() => props.cbuPositions, (val) => {
    if (val.length > 0) {
      getOrderList()
    }
  })
  const requestAnimationFrameInterval = ref(null)
  const isLoadingS = ref(true)
  const isMobile = ref(false)
  const screenWidth = ref(0)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  watch(() =>isChangeFutureOrder.value, val => {
    if (val) {
      isChangeFutureOrder.value = false
      setTimeout(() => {
        getOrderList()
      }, 500)
    }
  })
  const socketDateAnimation = () => {
    if (isChangeFutureOrder.value) {
      isChangeFutureOrder.value = false
      setTimeout(() => {
        getOrderList()
      }, 500)
    }
    symbolLast.value = COLLATERALSymbol.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  const limitCloseOrder = (row) => {
    isShowLimitClose.value = true
    curDataItem.value = row
  }
  const isShowLevelDialog = ref(false)
  const changeLevel = (row) => {
    checkData.value = row
    isShowLevelDialog.value = true
  }
  onMounted(() => {
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
    isLoadingS.value = true
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    isLoadingS.value = false
  })
</script>
<style lang="scss">
.ordersCont{
  &.position{
    height: 100%;
    position:relative;
  }
}
.border-left-cont{
  border-left:3px solid;
  padding-left:8px;
  &.rise{
    @include border-color(rise);
  }
  &.fall{
    @include border-color(fall);
  }
}
.el-table {
  &.position-table {
    .el-table__expand-column{
    }
    th.el-table__cell{
      font-size:12px !important;
    }
    .el-table__cell.el-table__expanded-cell{
      padding:0 !important;
    }
    .el-table__expand-icon--expanded {
      display: none;
    }
    .el-table {
      font-size: 12px;
      .el-table__body-wrapper::-webkit-scrollbar {
          width: 6px; /*滚动条宽度*/
          height: 6px; /*滚动条高度*/
        }
      .el-table__body-wrapper::-webkit-scrollbar-track {
          cursor: pointer;
          border-radius: 10px; /*滚动条的背景区域的圆角*/
          background-color: rgba(0, 0, 0, 0);
        }
      .el-table__body-wrapper::-webkit-scrollbar-thumb {
        cursor: pointer;
        [data-theme^="light"] & {
          background-color: #d6d7d8;
        }
        [data-theme^="dark"] & {
          background-color: #242a35;
        }
      }
      tr {
        &.hover-row {
          td {
            background-color: transparent !important;
          }
        }
        &.is-have-list {
          td {
            border-bottom: 0 !important;
          }
        }
        &:hover {
          td {
            background: transparent !important;
          }
        }
      }
      td {
        &.el-table__expanded-cell {
          padding: 0 !important;
          border-bottom: 0 !important;
        }
      }
      th {
        padding: 5px 0 !important;
        &:first-child {
          .cell {
            padding-left: 12px !important;
          }
        }
        &:last-child {
          .cell {
            padding-right: 12px !important;
          }
        }
        &.is-leaf {
          border: none;
        }
        .filter-side {
          &:hover {
            .icon-my-filter {
              @include color(theme, 1);
            }
          }
        }
      }
      td {
        &:first-child {
          .cell {
            padding-left: 12px !important;
          }
        }
        &:last-child {
          .cell {
            padding-right: 12px !important;
          }
        }
        @include border-color(border, 1);
        padding: 4px 0 !important;
        .el-table {
          &.stopLoss-table {
            tr {
              &.hover-row {
                td {
                  background-color: transparent !important;
                }
              }
              &:hover {
                td {
                  background: transparent !important;
                }
              }
              &:last-child {
                td {
                  border: 0;
                  border-bottom: 1px solid;
                  @include border-color(border);
                }
              }
            }
            th {
              padding: 5px 0 !important;
              &:first-child {
                .cell {
                  padding-left: 12px !important;
                }
              }
              &:last-child {
                .cell {
                  padding-right: 12px !important;
                }
              }
              &.is-leaf {
                border: none;
              }
              .filter-side {
                &:hover {
                  .icon-my-filter {
                    @include color(theme, 1);
                  }
                }
              }
            }
            td {
              border-bottom: 0;
              @include border-color(border);
              &:first-child {
                .cell {
                  padding-left: 12px !important;
                }
              }
              &:last-child {
                .cell {
                  padding-right: 12px !important;
                }
              }
            }
            .cell {
              padding-top: 0px !important;
              padding-bottom: 0px !important;
              padding-left: 0px !important;
              padding-right: 0px !important;
            }
          }
        }
      }
      .cell {
        padding-top: 0px !important;
        padding-bottom: 0px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        line-height: 24px;
        height: 24px;
        .fit-info[disabled] {
          opacity: 0.4;
          cursor: not-allowed;
        }
      }
      .caret-wrapper {
        height: 34px;
        width: 20px;
      }
    }
  }
}
.el-message-box {
  &.sd-message-dialog {
    width: 600px;
    .el-message-box__header {
      padding-bottom: 16px;
      border-bottom: 1px solid;
      @include border-color(border);
    }
    .el-message-box__container{
      width:100%;
      display:block;
    }
    .el-message-box__content {
      // padding:0 24px;
      .sd-title-box {
        padding: 16px 0;
        .li-box {
          padding-right:8px;
          &.mode-bx{
            padding-left:8px;
            border-radius:4px;
            @include bg-color(bg-quaternary);
            @include color(tc-secondary);
          }
          &.side-bx{
            padding-left:8px;
            border-radius:4px;
            &.fit-fall{
              background-color:rgba(255, 98, 98, 0.1);
            }
            &.fit-rise{
              background-color:rgba(59, 193, 137, 0.1);
            }
          }
        }
      }
      .sd-cont-box {
        padding: 0;
      }
    }
  }
}
@include mb {
  .el-message-box {
    &.sd-message-dialog {
      .el-message-box__content {
        .sd-title-box {
          padding: 16px 0;
          &.flex-box{
            flex-direction: column;
            align-items:start;
          }
          .mt-bx{
            margin-top:12px;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.lever-block{
  display:inline-block;
  vertical-align:middle;
  padding:2px 8px;
  border-radius:4px;
  white-space:nowrap;
  @include bg-color(bg-tertiary);
  svg{
    display:inline-block;
    vertical-align:middle;
  }
}
.el-button{
  &.position-btn{
    color:#fff !important;
    &.rise-btn{
      @include border-color(rise);
      @include bg-color(rise);
    }
    &.fall-btn{
      @include border-color(fall);
      @include bg-color(fall);
    }
  }
}

.fast-icon-btn, .reserve-icon-btn{
  font-size:40px !important;
  cursor:pointer;
  @include color(tc-primary);
  &:hover{
    @include color(theme);
  }
}
.small-position-input{
  height:28px;
}
.position-header-dashed {
  border-bottom: 1px dashed;
  cursor:pointer;
  @include border-color(tc-tertiary);
}
@include mb{
  .position-m-cont{
    .position-m-wrap{
      padding:0 16px;
      .position-list-cont{
        padding:16px 0;
        border-bottom:1px solid;
        @include border-color(border);
        &:last-child{
          border-bottom:0;
        }
        .symbol-cont{
          padding-bottom:12px;
          .side-tag{
            display:block;
            font-size:12px;
            min-width:16px;
            height:16px;
            line-height:16px;
            text-align:center;
            border-radius:2px;
            margin-right:8px;
            &.fit-fall{
              @include bg-color(fall);
              @include color(tc-button);
            }
            &.fit-rise{
              @include bg-color(rise);
              @include color(tc-button);
            }
          }
        }
        .symbol-info-box{
          padding-bottom:8px;
          p{
            font-size:12px;
          }
          div{
            flex:1;
          }
          span{
            font-size:14px;
            @include color(tc-primary);
            &.fit-rise{
              @include color(rise);
            }
            &.fit-fall{
              @include color(fall);
            }
          }
        }
        .position-btn-box{
          padding-top:8px;
          .pos-btn{
            flex:1;
            padding:8px;
            text-align:center;
            font-size:14px;
            border-radius:8px;
            @include color(tc-primary);
            @include bg-color(bg-secondary);
          }
        }
      }
    }
  }
}
</style>
