<template>
  <div ref="depthRef" class="exchnage-depth-wrap">
    <div class="exchange-depth-top flex-box space-between pd-lr16">
      <div class="flex-box">
        <span :class="{active: type === 'all'}" class="depth-type-taps mg-r12 flex-box space-center" @click="type = 'all'">
          <div class="icon-depths all"></div>
        </span>
        <span :class="{active: type === 'buy'}" class="depth-type-taps mg-r12 flex-box space-center" @click="type = 'buy'">
          <div class="icon-depths buy"></div>
        </span>
        <span :class="{active: type === 'sell'}" class="depth-type-taps mg-r12 flex-box space-center" @click="type = 'sell'">
          <div class="icon-depths sell"></div>
        </span>
      </div>
      <el-dropdown @command="(command) => { decimal = command; setPriceScale(command) }">
        <span class="fit-tc-primary ts-14 el-dropdown-link flex-box">
          <span class="mg-r4" style="display:block;">{{ $t('x位小数', { x: decimal }) }}</span>
          <MonoDownArrowMin size="12"/>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <template v-for="item in priceScale + 1">
              <el-dropdown-item v-if="item > (priceScale - 3 > 0 ? priceScale - 3 : 0)" :key="item - 1" :command="item - 1">{{ $t('x位小数', { x: item - 1 }) }}</el-dropdown-item>
            </template>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="exchange-depth-content">
      <div class="depth-row fit-tc-primary head">
        <span class="depth-row__item">{{ $t('价格') }}({{ pair.split('_')[1] }})</span>
        <span class="depth-row__item">{{ $t('数量') }}({{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }})</span>
        <span class="depth-row__item">{{ $t('合计') }}({{ useCommon.getFuturesSymbol(pair, futuresType === 'ipc' ? isCBCUnitUSD : isCBUUnitUSDT) }})</span>
      </div>
      <div class="depth-wrap-scrollbar">
        <el-scrollbar v-if="type !== 'buy'" wrap-class="buy-area" :wrap-style="`height: ${height}px`" tag="ul">
          <li v-for="(item, index) in asks" :key="index" :class="bgSizeAsk(item)" @click="changePrice(item)">
            <span>
              <span class="fit-fall">{{ format(item.price, decimal) }}</span>
            </span>
            <span @click.stop="changeAmount(item.volume)">
              {{ format((useCommon.cbuConver(item.volume, isCBUUnitUSDT, item.price)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
            </span>
            <span @click.stop="changeAmount(getAskTotal(index).volume)">
              {{ item.price === '--' ? '--' : format((getAskTotal(index).volume), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
            </span>
          </li>
        </el-scrollbar>
        <div class="exchange-depth-ticker" :class="{'sell': type === 'sell'}">
          <div class="flex-box pd-l16">
            <span :class="`ts-18 tw-5 flex-box fit-tc-primary ${className((ticker || {}).change)}`">{{ format((ticker || {}).last, priceScale, true) || '--' }}</span>
            <MonoDownArrowMin size="14" class="mg-l4 change-arrow" :class="className((ticker || {}).change)" />
            <span class="ts-14 mg-l8 fit-tc-secondary">{{ format((futureInfo[pair] || {}).markPrice, priceScale, true) || '--' }}</span>
          </div>
        </div>
        <el-scrollbar v-if="type !== 'sell'" wrap-class="buy-area" :wrap-style="`height: ${height}px`" tag="ul">
          <li v-for="(item, index) in bids" :key="index" :class="bgSizeBid(item)" @click="changePrice(item)">
            <span>
              <span class="fit-rise">{{ format(item.price, decimal) }}</span>
              <!-- <span v-if="handlePricePoints.buy.includes(item.price)" class="postition-absolute"></span> -->
            </span>
            <span @click.stop="changeAmount(item.volume)">
              {{ format((useCommon.cbuConver(item.volume, isCBUUnitUSDT, item.price)), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
            </span>
            <span @click.stop="changeAmount(getBidTotal(index).volume)">
              {{ item.price === '--' ? '--' : format((getBidTotal(index).volume), isCBUUnitUSDT ? priceScale : quantityScale, true) }}
            </span>
          </li>
        </el-scrollbar>
        <div v-if="type === 'all'" class="exchnage-depth-buy-sell-cont">
          <div class="compare-direction">
            <div>B</div>
            <div class="compare-percent-buy">{{ format(((bidsVolumn / totalVolumn) * 100), 2) }}%</div>
          </div>
          <div class="compare-bar">
            <div class="compare-bids-bar" :style="`width: ${format(((bidsVolumn / totalVolumn) * 100), 2)}%;`"></div>
            <div class="compare-asks-bar" :style="`width: ${format(((asksVolumn / totalVolumn) * 100), 2)}%;`"></div>
          </div>
          <div class="compare-direction">
            <div class="compare-percent-sell">{{ format(((asksVolumn / totalVolumn) * 100), 2) }}%</div>
            <div>S</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import BigNumber from 'bignumber.js'
import { format } from '~/utils'
import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElScrollbar } from 'element-plus'
import MonoDownArrowMin from '~/components/common/icon-svg/MonoDownArrowMin.vue'
import { useCommonData } from '~/composables/index'
import { futureStore } from '~/stores/futureStore'
import { commonStore } from '~/stores/commonStore'
const publicStore = commonStore()
const { depthsStore, pairInfo } = storeToRefs(publicStore)
// 新增：与现货保持一致，同步全局价格小数位
const { priceScale: storePriceScale } = storeToRefs(publicStore)
const { setPriceScale } = publicStore

const store = futureStore()
const { subFutureInfoSocket } = store
const { futureInfo } = storeToRefs(store)
const useCommon = useCommonData()
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  ticker: {
    type: Object,
    default(){
      return {}
    }
  },
  isCBCUnitUSD: {
    type: Boolean,
    default: false
  },
  isCBUUnitUSDT: {
    type: Boolean,
    default: false
  },
  futuresType: {
    type: String,
    default: ''
  },
  priceScale: {
    default: '',
    type: [Number, String]
  },
  quantityScale: {
    type: [Number, String],
    default: ''
  }
})
const emit = defineEmits(['change-amount', 'change-price'])
const depths = ref({})
const type = ref('all')
const height = ref(0)
const decimal = ref(0)
const poprerPointClass = ref('')
// SSR 一致性：用 props.priceScale 确定性初始化，避免 hydration mismatch
if (props.priceScale !== undefined && props.priceScale !== null && props.priceScale !== '') {
  decimal.value = Number(props.priceScale)
}

// 同步store中的priceScale，驱动专业版K线Y轴小数联动
watch(() => storePriceScale.value, (val) => {
  if (typeof val === 'number' && val !== decimal.value) {
    decimal.value = val
  }
})

// 初始化时用props.priceScale设置，同时写回store，保持一致
watch(() => props.priceScale, (val) => {
  if (val !== undefined && val !== null && val !== '') {
    const v = Number(val)
    if (!Number.isNaN(v)) {
      decimal.value = v
      setPriceScale(v)
    }
  }
}, { immediate: false })
const changeClass = ref('')
const hoverType = ref('bids')
const depthRef = ref(null)
const requestAnimationFrameInterval = ref(null)
const exchangeDepths = ref({})
const depthNum = computed(() => {
  if (type.value === 'all') {
    return Math.floor((height.value - 152) / 26 / 2)
  } else {
    return Math.floor((height.value - 128) / 26)
  }
})
const className = (c) => {
  if (c * 1 < 0) {
    return 'fit-fall'
  } else {
    return 'fit-rise'
  }
}
const asks = computed(() => {
  if (JSON.stringify(depths.value) === '{}' || depths.value === undefined) {
    return []
  }
  const target = {}
  for (const k in depths.value.asks) {
    const val = depths.value.asks[k]
    const key = new BigNumber(val.price).toFixed(decimal.value, BigNumber.ROUND_UP)
    if (!target[key]) {
      target[key] = JSON.parse(JSON.stringify(val))
      target[key].price = isNaN(key) ? '--' : key
    } else {
      const volume = new BigNumber(target[key].volume).plus(val.volume).valueOf()
      target[key].volume = isNaN(volume) ? '--' : volume
      target[key].price = isNaN(key) ? '--' : key
    }
  }
  let asks = Object.values(target).sort((a, b) => a.price - b.price)
  const diffLength = asks.length - depthNum.value
  if (diffLength > 0) {
    asks = asks.slice(0, depthNum.value)
  } else {
    asks = asks.concat(new Array(-diffLength).fill({
      price: '--',
      volume: '--'
    }))
  }
  return asks.reverse().map((v, index) => {
    v.index = index
    return v
  })
})
const bids = computed(() => {
  if (JSON.stringify(depths.value) === '{}' || depths.value === undefined) {
    return []
  }
  const target = {}
  for (const k in depths.value.bids) {
    const val = depths.value.bids[k]
    const key = new BigNumber(val.price).toFixed(decimal.value, BigNumber.ROUND_DOWN)
    if (!target[key]) {
      target[key] = JSON.parse(JSON.stringify(val))
      target[key].price = isNaN(key) ? '--' : key
    } else {
      const volume = new BigNumber(target[key].volume).plus(val.volume).valueOf()
      target[key].volume = isNaN(volume) ? '--' : volume
      target[key].price = isNaN(key) ? '--' : key
    }
  }
  let bids = Object.values(target).sort((f, b) => b.price - f.price)
  const diffLength = bids.length - depthNum.value
  if (diffLength > 0) {
    bids = bids.slice(0, depthNum.value)
  } else {
    bids = bids.concat(new Array(-diffLength).fill({
      price: '--',
      volume: '--'
    }))
  }
  return bids.map((v, index) => {
    v.index = index
    return v
  })
})
const val = computed(() => {
  return pairInfo.value[props.pair].min_order_size  || 0
})
const transfer = (amount) => {
  const value = props.futuresType === 'ipc' ? val.value : 1
  const decimal = props.futuresType === 'ipc' ? props.quantityScale : (props.isCBUUnitUSDT ? props.priceScale : props.quantityScale)
  return new BigNumber(amount).dividedBy(value).toFixed(decimal, 1)
}
const maxAskVolume = computed (() => {
  let max = 0
  asks.value.forEach(asksP => {
    if (asksP.volume * 1 > max) {
      max = asksP.volume * 1
    }
  })
  return max
})
const maxBidVolume = computed (() => {
  let max = 0
  bids.value.forEach(bidsP => {
    if (bidsP.volume * 1 > max) {
      max = bidsP.volume * 1
    }
  })
  return max
})
const bgSizeAsk = (row) => {
  let size = 0
  if (row.volume !== '--') {
    size = Math.floor((row.volume * 1 / maxAskVolume.value) * 100)
  }
  return 'asks-bg bg-size-' + size
}
const bgSizeBid = (row) => {
  let size = 0
  if (row.volume !== '--') {
    size = Math.floor((row.volume * 1 / maxBidVolume.value) * 100)
  }
  return 'bids-bg bg-size-' + size
}
const getAskTotal = (index) => {
  const asksT = asks.value
  const list = asksT.slice(-asksT.length + index)
  const decimal = props.isCBUUnitUSDT ? props.priceScale : props.quantityScale
  if (!list || list.length === 0) {
    return {
      volume: '--',
      total: '--'
    }
  }
  if (list.length > 0) {
    let volume = list[0].volume
    if (props.isCBUUnitUSDT) {
      volume = new BigNumber(list[0].volume).multipliedBy(list[0].price).toFixed(decimal, BigNumber.ROUND_DOWN) * 1
    }
    list[0] = {
      volume,
      total: getItemTotal(list[0])
    }
  }
  if (list.length === 1) {
    return list[0]
  }
  return list.reduce((f, b) => {
    let total = f.total || getItemTotal(f)
    let addTotal = getItemTotal(b)
    total = total === '--' ? '--' : isNaN(total) ? 0 : total
    addTotal = isNaN(addTotal) ? 0 : addTotal
    let volume = b.volume === '--' ? 0 : b.volume
    if (props.isCBUUnitUSDT && b.volume !== '--') {
      volume = new BigNumber(volume).multipliedBy(b.price).toFixed(decimal, BigNumber.ROUND_DOWN) * 1
    }
    return {
      volume: new BigNumber(f.volume || 0).plus(volume).toNumber(),
      total: total === '--' ? '--' : new BigNumber(total).plus(addTotal).toNumber()
    }
  })
}
const getBidTotal = (index) => {
  const bidsT = bids.value
  const list = bidsT.slice(0, index + 1)
  const decimal = props.isCBUUnitUSDT ? props.priceScale : props.quantityScale
  if (!list || list.length === 0) {
    return {
      volume: '--',
      total: '--'
    }
  }
  if (list.length > 0) {
    let volume = list[0].volume
    if (props.isCBUUnitUSDT) {
      volume = new BigNumber(list[0].volume).multipliedBy(list[0].price).toFixed(decimal, BigNumber.ROUND_DOWN) * 1
    }
    list[0] = {
      volume,
      total: getItemTotal(list[0])
    }
  }
  if (list.length === 1) {
    return list[0]
  }
  return list.reduce((f, b) => {
    let total = f.total || getItemTotal(f, 'price')
    let addTotal = getItemTotal(b, 'price')
    total = total === '--' ? '--' : isNaN(total) ? 0 : total
    addTotal = isNaN(addTotal) ? 0 : addTotal
    let volume = b.volume === '--' ? 0 : b.volume
    if (props.isCBUUnitUSDT && b.volume !== '--') {
      volume = new BigNumber(volume).multipliedBy(b.price).toFixed(decimal, BigNumber.ROUND_DOWN) * 1
    }
    return {
      volume: new BigNumber(f.volume || 0).plus(volume).toNumber(),
      total: total === '--' ? '--' : new BigNumber(total).plus(addTotal).toNumber()
    }
  })
}
const getItemTotal = (item) => {
  if (item.price === '--' || item.volume === '--') {
    return '--'
  }
  return new BigNumber(item.price).multipliedBy(item.volume)
}
const changeAmount = (amount) => {
  if (isNaN(amount)) {
    return false
  }
  emit('change-amount', new BigNumber(amount).toFixed(props.quantityScale, BigNumber.ROUND_DOWN))
}
const changePrice = (row) => {
  if (isNaN(row.price)) {
    return false
  }
  emit('change-price', row.price.toString())
}
const totalVolumn = computed(() => {
  let num = 0
  if (asks.value.length > 0 && bids.value.length > 0) {
    const arr = asks.value.concat(bids.value)
    arr.forEach((item) => {
      num += (item.volume === '--' ? 0 : (Number(item.volume) < 0 ? -Number(item.volume) : Number(item.volume)))
    })
  }
  return num
})
const asksVolumn = computed(() => {
  let num = 0
  if (asks.value.length > 0) {
    asks.value.forEach((item) => {
      num += (item.volume === '--' ? 0 : (Number(item.volume) < 0 ? -Number(item.volume) : Number(item.volume)))
    })
  }
  return num
})
const bidsVolumn = computed(() => {
  let num = 0
  if (bids.value.length > 0) {
    bids.value.forEach((item) => {
      num += (item.volume === '--' ? 0 : (Number(item.volume) < 0 ? -Number(item.volume) : Number(item.volume)))
    })
  }
  return num
})
watch(() => props.pair, (val) => {
  socketDateAnimation()
})
watch(() => props.priceScale, (val) => {
  if (val) {
    decimal.value = val || 4
  }
}, {
  immediate: true
})
const socketDateAnimation = () => {
  depths.value = depthsStore.value[props.pair]
  height.value = depthRef.value && depthRef.value.offsetHeight
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
}
onMounted (() => {
  socketDateAnimation()
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
})
onUnmounted(() => {
  requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
})
</script>
<style lang="scss">
.exchnage-depth-wrap{
  height: calc(100%);
  display: flex;
  flex: 1 1;
  flex-direction: column;
  .change-arrow{
    &.fit-rise{
      transform:rotate(180deg);
    }
  }
  .exchange-depth-top{
    height:42px;
    .depth-type-taps{
      width:28px;
      height:28px;
      border-radius:4px;
      cursor:pointer;
      @include pc-hover {
        &:hover{
          @include bg-color(bg-quaternary);
        }
      }
      &.active{
        @include bg-color(bg-quaternary);
        .icon-depths{
          opacity: 1;
        }
      }
      .icon-depths{
        opacity:0.4;
        width:16px;
        height:16px;
        background-size:48px auto;
        @include get-img('@/assets/images/exchange/icon-depth-light.png', '@/assets/images/exchange/icon-depth-dark.png');
        &.all{
          background-position: 0 0;
        }
        &.buy{
          background-position: -16px 0;
        }
        &.sell{
          background-position: -32px 0;
        }
      }
    }
  }
  .el-dropdown-link{
    padding:0 12px;
    height:34px;
    border-radius:6px;
    border:1px solid;
    cursor:pointer;
    @include border-color(bg-quaternary);
    @include bg-color(bg-quaternary);
    @include pc-hover {
      &:hover{
        @include border-color(theme);
      }
    }
    svg{
      @include color(tc-secondary);
    }
  }
  .exchange-depth-content{
    position: relative;
    height: calc(100% - 42px);
    position: relative;
    .scroll-sum {
      font-size:14px;
      display: none;
      position: absolute;
      left: 0;
      transform: translate(calc(-100% - 8px), calc(-50% + 10px));
      z-index: 12;
      padding: 8px 16px;
      border-radius: 4px;
      box-shadow: 0 12px 48px 0 rgba(0, 0, 0, 0.1),
        0 4px 8px 0 rgba(0, 0, 0, 0.05);
      min-width: 200px;
      box-sizing: border-box;
      background-color: getSingleColor(bg-fourthary, 1, dark);
      &::after {
        content: "";
        position: absolute;
        right: -12px;
        top: 50%;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        transform: translateY(-50%);
        border-left-color: getSingleColor(bg-fourthary, 1, dark);
      }
    }
    .scroll-sum {
      @for $i from 0 to 32 {
        &.index-buy-#{$i} {
          display: block;
          top: calc(44px + #{$i * 26}px);
        }
      }
      @for $i from 0 to 32 {
        &.index-sell-#{$i} {
          display: block;
          bottom: calc(-4px + #{$i * 26}px);
        }
      }
    }
    .depth-row {
      font-size: 12px;
      line-height: 44px;
      height:44px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
      white-space: nowrap;
      position: relative;
      z-index: 2;
      box-sizing: border-box;
      .depth-row__item {
        position: relative;
        z-index: 1;
        &:first-of-type {
          width: 76px;
          margin-right: 4px;
          &.self {
            position: relative;
            &::before {
              content: "";
              position: absolute;
              left: -8px;
              top: calc(50% - 2px);
              width: 4px;
              height: 4px;
              border-radius: 2px;
              @include bg-color(border);
            }
          }
        }
        &:nth-of-type(2) {
          text-align: right;
          width: 90px;
          margin-right: 4px;
        }
        &:nth-of-type(3) {
          text-align: right;
          flex: 1;
        }
      }
    }
    .depth-wrap-scrollbar{
      height: calc(100% - 44px);
      display: flex;
      flex: 1 1;
      flex-direction: column;
    }
    .buy-area {
      li {
        height: 26px;
        line-height: 26px;
        display: flex;
        align-items: center;
        border-top: 1px dashed transparent;
        border-bottom: 1px dashed transparent;
        cursor: pointer;
        @include color(tc-primary);
      }
      li.is-hover {
        @include bg-color(theme, 0.1);
        &.hover-end:not(.sell) {
          border-bottom-color: transparent !important;
          @include border-color(theme, 0.4);
        }
        &.hover-end.sell {
          border-top-color: transparent !important;
          @include border-color(theme, 0.4);
        }
      }
      li > span {
        font-size: 14px;
        line-height: 20px;
        &:nth-of-type(1) {
          width: 76px;
          margin-right: 4px;
          padding-left: 16px;
          box-sizing: border-box;
          position: relative;
          .postition-absolute {
            position: absolute;
            left: 4px;
            top: 7px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            @include bg-color(theme);
          }
        }
        &:nth-of-type(2) {
          width: 90px;
          text-align: right;
          margin-right: 4px;
        }
        &:last-of-type {
          flex: 1;
          text-align: right;
          box-sizing: border-box;
          padding-right: 16px;
        }
      }

      li.is-hover {
        @include bg-color(theme, 0.1);
        &.hover-end:not(.sell) {
          border-bottom-color: transparent !important;
          @include border-color(theme, 0.4);
        }
        &.hover-end.sell {
          border-top-color: transparent !important;
          @include border-color(theme, 0.4);
        }
      }

      li {
        &.asks-bg {
          background-size: 0;
          [class="dark"] & {
            background-image: linear-gradient(
              rgba($color: getSingleColor(fall, 1, dark), $alpha: 0.1),
              rgba($color: getSingleColor(fall, 1, dark), $alpha: 0.1)
            );
          }
          [class="light"] & {
            background-image: linear-gradient(
              rgba($color: getSingleColor(fall, 1, light), $alpha: 0.1),
              rgba($color: getSingleColor(fall, 1, light), $alpha: 0.1)
            );
          }
        }
        &.bids-bg {
          background-size: 0;
          [class="dark"] & {
            background-image: linear-gradient(
              rgba($color: getSingleColor(rise, 1, dark), $alpha: 0.1),
              rgba($color: getSingleColor(rise, 1, dark), $alpha: 0.1)
            );
          }
          [class="light"] & {
            background-image: linear-gradient(
              rgba($color: getSingleColor(rise, 1, light), $alpha: 0.1),
              rgba($color: getSingleColor(rise, 1, light), $alpha: 0.1)
            );
          }
        }
        $b: 1;
        @while $b <= 100 {
          &.bg-size-#{$b * 1} {
            background-position: 100%;
            background-repeat: no-repeat;
            background-size: ($b * 1%) 100%;
          }
          $b: $b + 1;
        }
      }
    }
    .exchange-depth-ticker{
      padding: 12px 0;
      &.sell{
        padding-top:8px;
        padding-bottom:16px;
      }
      .ts-18{
        font-size:20px !important;
      }
      .ts-14{
        font-size:14px !important;
      }
    }
    .exchnage-depth-buy-sell-cont{
      height:44px;
      line-height:44px;
      align-items: center;
      display: flex;
      font-size: 14px;
      justify-content: space-between;
      padding: 0 16px;
      .compare-direction{
        align-items: center;
        display: flex;
        line-height: 44px;
        @include color(tc-primary);
        .compare-percent-buy{
          font-size:12px;
          margin-left:2px;
          @include color(rise);
        }
        .compare-percent-sell{
          font-size:12px;
          margin-right:2px;
          @include color(fall);
        }
      }
      .compare-bar{
        align-items: center;
        display: flex;
        flex: 1 1;
        padding: 0 8px;
        .compare-bids-bar{
          align-items: center;
          height: 6px;
          border-bottom-left-radius:6px;
          border-top-left-radius:6px;
          @include bg-color(rise)
        }
        .compare-asks-bar{
          align-items: center;
          height: 6px;
          margin-left: 2px;
          border-bottom-right-radius:6px;
          border-top-right-radius:6px;
          @include bg-color(fall)
        }
      }
    }
  }
}
</style>
