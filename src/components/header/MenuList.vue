<template>
  <div class="left-menu-list">
    <ul class="flex-box">
      <li :class="{'active': modeType === 'transaction'}">
        <NuxtLink :to="`/${locale}/transaction`">{{ $t('一键买币') }}</NuxtLink>
      </li>
      <li :class="{'active': modeType === 'index'}">
        <NuxtLink :to="`/${locale}`">{{ $t('行情') }}</NuxtLink>
      </li>
      <li :class="{'active': modeType === 'spot'}">
        <NuxtLink :to="`/${locale}/exchange/BTC_USDT`">{{ $t('币币交易') }}</NuxtLink>
      </li>
      <li :class="{'active': modeType === 'future'}">
        <NuxtLink :to="`/${locale}/future/BTC_USDT_SWAP`">{{ $t('合约交易') }}</NuxtLink>
      </li>
      <li :class="{'active': modeType === 'launchpool'}">
        <NuxtLink :to="`/${locale}/launchpool`">LaunchPool</NuxtLink>
      </li>
      <li :class="{'active': modeType === 'coupon'}">
        <NuxtLink :to="`/${locale}/coupon/list`">{{ $t('福利中心') }}</NuxtLink>
      </li>
      <!-- <li>
        <a :href="`${useCommon.zendeskUrl(locale)}/categories/4662266924574`" target="_blank">{{ $t('帮助中心') }}</a>
      </li> -->
    </ul>
  </div>
</template>
<script setup lang="ts">
  import { useCommonData } from '~/composables/index'
  const props = defineProps({
    modeType: {
      type: String,
      default: ''
    }
  })
  const useCommon = useCommonData()
  const { locale } = useI18n()
</script>
<style lang="scss" scoped>
.left-menu-list{
  font-size:16px;
  @include color(tc-primary);
  ul{
    li{
      padding:0 12px;
      white-space:nowrap;
      &.active{
        a{
          @include color(theme);
        }
      }
      a{
        @include color(tc-primary);
        @include pc-hover {
          &:hover{
            @include color(theme);
          }
        }
      }
    }
  }
}
</style>
