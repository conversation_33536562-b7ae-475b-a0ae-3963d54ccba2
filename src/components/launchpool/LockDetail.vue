<template>
  <div>
    <div class="check-title flex-box space-between align-start">
      <div>
        <p class="font-size-14 fit-tc-primary">
          <span class="fit-tc-secondary">{{ $t('已选择') }}：</span>
          {{ checkedList.length }} NFT
        </p>
        <p class="font-size-14 fit-tc-primary">
          <span class="fit-tc-secondary">{{ $t('预计每小时收益') }}：</span>
          {{ profit }} {{ $t('空投币') }}
        </p>
      </div>
      <div class="flex-box check-right-search">
        <!-- <el-input v-model="searchText" :placeholder="$t('请搜索')" class="search-input">
          <template #prepend>
            <MonoSearch size="16" />
          </template>
        </el-input> -->
        <el-button :disabled="nftList.length === 0" type="primary" @click="selectAll">{{ isAllChecked ? $t('取消全选') : $t('全选') }}</el-button>
      </div>
    </div>
    <div v-loading="isLoading">
      <div v-if="nftList.length > 0" class="check-box-container">
        <el-checkbox-group v-model="checkedList" class="flex-box space-start" @change="handleChange">
          <div v-for="(item, index) in nftList" :key="index" class="check-item">
            <div class="check-item-pd">
              <div class="check-item-title text-center">
                <p>{{ item.name }}</p>
                <p>#{{ item.sequence }}#</p>
              </div>
              <div class="check-item-img">
                <img :src="item.icon_url" />
              </div>
              <el-checkbox :value="item.id" :key="item.id" :disabled="item.status * 1 === 2">{{ $t('选择') }}</el-checkbox>
            </div>
          </div>
        </el-checkbox-group>
      </div>
      <div v-else class="check-box-null-container" style="height:300px;">
        <BoxNoData :text="$t('暂无数据')" />
      </div>
    </div>
    <div class="check-bottom-footer">
      <div class="footer-info flex-box">
        <el-button class="mg-r8" @click="rechargeFun">{{ $t('充值') }}</el-button>
        <el-button type="primary" :disabled="isDisabled" :loading="isLockLoading" @click="lockFun()">{{ $t('锁仓') }}</el-button>
      </div>
      <el-checkbox v-model="checkInfo">{{ $t('我已阅读并同意') }}<a :href="`${useCommon.zendeskUrl(locale)}/articles/4662583454750`" class="fit-theme">{{ $t('Launchpool 用户使用协议') }}</a></el-checkbox>
    </div>
  </div>
  <el-dialog v-if="isShowDeposit" v-model="isShowDeposit" width="900" class="deposit-withdawl-dialog" :close-on-click-modal="false" @close="isShowDeposit = false">
    <NFTDeposit />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElDialog, ElInput, ElButton, ElCheckboxGroup, ElCheckbox } from 'element-plus'
  import { getLpNftListAPI, getLpNftLockAPI } from '~/api/tt.ts'
  import { useCommonData } from '~/composables/index'
  import NFTDeposit from '~/components/nft/my/deposit.vue'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  const useCommon = useCommonData()
  const router = useRouter()
  const { locale, t } = useI18n()
  const props = defineProps({
    detail: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['request'])
  const nftList = ref([])
  const checkedList = ref([])
  const isLoading = ref(false)
  const checkInfo = ref(false)
  const isMobile = ref(false)
  const findValueInRange = (num, ranges) => {
    // 遍历数组中的每个子数组
    for (const [range, value] of ranges) {
      // 解析范围字符串为起始和结束数字
      const [start, end] = range.split('_').map(Number)
      // 检查数字是否在范围内
      if (num >= start && num <= end) {
        // 如果在范围内，返回对应的值
        return Number(value)
      }
    }
    // 如果不在任何范围内，返回默认值
    return 1
  }
  const checkedSList = ref([])
  const isDisabled = computed(() => {
    if (checkInfo.value && checkedSList.value.length > 0) {
      return false
    } else {
      return true
    }
  })
  const profit = computed(() => {
    let num = 0
    checkedSList.value.forEach((item) => {
      num += findValueInRange(item, JSON.parse(props.detail.weight))
    })
    console.log(num, Number(props.detail.user_data.nft_lock_weight), Number(props.detail.nft_lock_weight), 'djddejuhedueu')
    return (( num + Number(props.detail.user_data.nft_lock_weight)) / (num + Number(props.detail.nft_lock_weight)) * props.detail.hour_total) || 0
  })
  const getNFtList = async() => {
    isLoading.value = true
    const { data } = await getLpNftListAPI({
      nft_collection_id: JSON.parse(props.detail.nft_collection_id),
      page: 1,
      size: 1000
    })
    if (data) {
      data.rows.forEach((item) => {
        if (item.status * 1 === 2) {
          checkedList.push(item.id)
        }
      })
      nftList.value = data.rows
    }
    isLoading.value = false
  }
  const isLockLoading = ref(false)
  const lockFun = async() => {
    isLockLoading.value = true
    const { data, error } = await getLpNftLockAPI({
      airdrop_id: props.detail.id,
      ids: checkedList.value.join(',')
    })
    if (data) {
      emit('request')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isLockLoading.value = false
  }
  const isAllChecked = ref(false)
  const selectAll = () => {
    isAllChecked.value = !isAllChecked.value
    checkedList.value = isAllChecked.value ? nftList.value.map(item => item.id) : []
    checkedSList.value = isAllChecked.value ? nftList.value.map(item => item.id) : []
  }
  const handleChange = (value) => {
    const sequenceSet = new Set()
    nftList.value.forEach((item) => {
      value.forEach((ite) => {
        if (ite === item.id) {
          sequenceSet.add(item.sequence)
        }
      })
    })
    checkedSList.value = Array.from(sequenceSet)
    isAllChecked.value = checkedList.value.length === nftList.value.length
  }
  const isShowDeposit = ref(false)
  const rechargeFun = () => {
    isMobile.value ? router.push(`/${locale.value}/nft/my/deposit`) : isShowDeposit.value = true
  }
  const screenWidth = ref(0)
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768
  }
  onMounted(() => {
    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
  })
  onBeforeMount(() => {
    getNFtList()
  })
</script>
<style lang="scss">
  .check-title{
    padding-bottom:24px;
    .check-right-search{
      .el-input{
        &.search-input{
          @include bg-color(bg-quaternary);
          border-radius:20px;
          .el-input__wrapper{
            border-radius:20px;
          }
        }
      }
      .el-input-group--prepend > .el-input__wrapper{
        border-bottom-left-radius: 20px !important;
        border-top-left-radius: 20px !important;
      }
      .el-button{
        width:100px;
        margin-left:12px;
      }
    }
  }
  .check-box-container{
    height:580px;
    overflow:auto;
    .el-checkbox-group{
      font-size:14px;
      line-height:18px;
      flex-wrap:wrap;
      .check-item{
        cursor:pointer;
        margin-bottom:8px;
        border-radius:16px;
        border:1px solid;
        margin-right:8px;
        @include border-color(border);
        &:nth-child(4n){
          margin-right:0;
        }
        .check-item-pd{
          padding:20px;
          .check-item-title{
            padding-bottom:16px;
            @include color(tc-primary);
          }
          .check-item-img{
            width:132px;
            height:132px;
            border-radius:8px;
            @include bg-color(bg-quaternary);
            img{
              display:block;
              width:100%;
              border-radius:8px;
            }
          }
        }
      }
    }
  }
  .check-bottom-footer{
    padding-top:32px;
    width:60%;
    margin:0 auto;
    .footer-info, .el-checkbox{
    }
    .footer-info{
      padding-bottom:20px;
    }
    .el-checkbox{
      display:block;
    }
  }
  @include mb {
    .check-title{
      padding:16px;
      flex-direction: column-reverse;
      .check-right-search{
        width:100%;
        margin-bottom:8px;
        .el-input{
          flex:1;
          &.search-input{
            @include bg-color(bg-primary);
            border-radius:6px;
            .el-input__wrapper{
              border-radius:6px;
              .el-input__inner{
                height:36px !important;
              }
            }
          }
        }
        .el-input-group--prepend > .el-input__wrapper{
          border-bottom-left-radius: 6px !important;
          border-top-left-radius: 6px !important;
        }
        .el-button{
          padding:10px 0 10px 12px;
          width:auto;
          margin-left:0;
          background:none !important;
          border:0 !important;
          color:#F0B90B !important;
        }
      }
    }
    .check-box-container{
      height:calc(100vh - 346px);
      overflow:auto;
      padding:0 16px 0px;
      .el-checkbox-group{
        font-size:14px;
        line-height:16px;
        flex-wrap:wrap;
        .check-item{
          margin-bottom:12px;
          border-radius:12px;
          border:1px solid;
          margin-right:8px;
          @include border-color(border);
          &:nth-child(4n){
            margin-right:8px;
          }
          &:nth-child(3n){
            margin-right:0;
          }
          .check-item-pd{
            padding:12px;
            .check-item-title{
              padding-bottom:8px;
              white-space:wrap;
              @include color(tc-primary);
            }
            .check-item-img{
              width:82px;
              height:82px;
              border-radius:8px;
              @include bg-color(bg-quaternary);
              img{
                display:block;
                width:100%;
                border-radius:8px;
              }
            }
          }
        }
      }
    }
    .check-bottom-footer{
      width:100%;
      position:absolute;
      bottom:0;
      left:0;
      right:0;
      padding:12px 16px;
      @include bg-color(bg-primary);
      z-index:99;
      .footer-info, .el-checkbox{
        width:100%;
        margin:0 auto;
      }
      .footer-info{
        padding-bottom:20px;
        .el-button{
          flex:1;
          height:40px;
        }
      }
      .el-checkbox{
        display:block;
      }
    }
  }
</style>