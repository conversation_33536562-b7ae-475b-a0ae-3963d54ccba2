<template>
  <!-- 封装的移动端列表组件 -->
  <div class="mobile-table">
    <van-list 
      :loading="loadingMobile" 
      :finished="finished" 
      :finished-text="emptyText"
      :loading-text="loadingText"
      :immediate-check="false"
      @load="handleLoad"
      @update:loading="handleUpdateLoading">
      <van-cell v-for="(item, index) in dataList" :key="index">
        <div class="mobile-table-item">
          <div 
            v-for="column in columns" 
            :key="column.dataIndex" 
            class="mobile-table-row">
            <div class="mobile-table-label">{{ column.title }}</div>
            <div class="mobile-table-value">
              <slot 
                name="data" 
                :column="column" 
                :record="item" 
                :index="index">
                {{ item[column.dataIndex] }}
              </slot>
            </div>
          </div>
        </div>
      </van-cell>
    </van-list>
  </div>
</template>

<script lang="ts" setup>
const { locale, t } = useI18n()
import 'vant/lib/index.css'
interface Column {
  dataIndex: string;
  title: string;
}

interface Props {
  dataList: Array<any>;
  loadingMobile: boolean;
  finished: boolean;
  initialLoaded?: boolean;
  columns: Column[];
  loadingText?: string;
  emptyText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  initialLoaded: false
});

const emit = defineEmits<{
  load: [];
  'update:loadingMobile': [value: boolean];
}>();

const handleLoad = () => {
  emit('load');
};

const handleUpdateLoading = (value: boolean) => {
  emit('update:loadingMobile', value);
};
</script>

<style lang="scss" scoped>
.mobile-table {
  width: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
  .van-cell{
    .dark & {
      background: #181a1f!important;
    }
  }
}

.mobile-table-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.mobile-table-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.mobile-table-label {
  font-size: 14px;
  color: #9497A0;
  font-weight: 500;
  flex-shrink: 0;
}

.mobile-table-value {
  font-size: 14px;
  font-weight: 500;
  color: #414655;
  text-align: right;
  word-break: break-all;
  .dark & {
      color: #fff;
    }
}
</style>
