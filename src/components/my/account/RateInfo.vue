<template>
  <div v-if="isLogin" class="exchange-info">
    <BoxLoading v-if="isLoading" />
    <div class="common-card">
      <h2 v-if="isRateList" class="info-title flex-box">
        <div class="title-left flex-box">
          <div class="head-icon flex-box space-center mg-r16">
            <MonoUserInfo size="24" />
          </div>
          <div class="flex-box info-box fit-tc-primary">
            {{ isVisibleUserInfo ? userInfo.name_visible : userInfo.name }}
            <template v-if="userInfo.is_bind_totp">
              <MonoEyeOpen size="18" v-if="isVisibleUserInfo" class="cursor-pointer mg-l8" @click="updateUserState(isVisibleUserInfo)" />
              <MonoEyeClose size="18" class="cursor-pointer mg-l8" v-else  @click="updateUserState(isVisibleUserInfo)" />
            </template>
            <AccountLevelType v-if="getUserLevel('contract') >= 50 && getUserLevel('spot') >= 50" :activeVip="activeVip" Ltype="spot" />
          </div>
        </div>
      </h2>
      <h2 v-if="!isRateList" class="info-title flex-box space-between">
        <div class="flex-box font-size-20 fit-tc-primary">
          <div class="title-icon"></div>
          {{ $t('交易费率') }}
          <AccountLevelType v-if="getUserLevel('contract') >= 50 && getUserLevel('spot') >= 50" :activeVip="activeVip" Ltype="spot" />
        </div>
        <div class="more-btn flex-box">
          <NuxtLink :to="`/${locale}/rate-list`">{{ $t('查看详情') }}</NuxtLink>
          <MonoRightArrowShort size="16" />
        </div>
      </h2>
      <div class="rate-table-box">
        <div class="exchange-wrap-info">
          <div class="exchange-item" :class="{'min': !isRateList, 'special': getUserLevel('spot') >= 8}">
            <div class="flex-box flex-column exchange-item-pd">
              <div style="width:100%;">
                <h3 class="flex-box space-between">
                  <span class="font-size-16 mg-r8 fit-tc-primary">{{ $t('现货等级') }}</span>
                  <AccountLevelType class="type-level" :activeVip="activeVip" Ltype="spot" />
                </h3>
                <div class="taker-maker-cont">
                  <dl class="flex-box">
                    <dt class="fit-tc-secondary font-size-14">{{ $t('当前') }}</dt>
                    <dd>
                      <span class="fit-tc-secondary font-size-14">{{ $t('挂单') }}/{{ $t('吃单') }} ：</span>
                      <span class="fit-tc-primary font-size-14">{{ activeVip.spotMaker }}%/{{ activeVip.spotTaker }}%</span>
                    </dd>
                  </dl>
                  <dl v-if="getUserLevel('spot') < 8" class="flex-box mg-t8">
                    <dt class="fit-tc-secondary font-size-14">{{ $t('下一级') }}</dt>
                    <dd>
                      <span class="fit-tc-secondary font-size-14">{{ $t('挂单') }}/{{ $t('吃单') }} ：</span>
                      <span class="fit-tc-primary font-size-14"><span class="fit-tc-secondary no-txt font-size-12">{{ activeVip.spotMaker }}%</span>{{ activeVip.nextSpotMaker }}%/<span class="fit-tc-secondary no-txt font-size-12">{{ activeVip.spotTaker }}%</span>{{ activeVip.nextSpotTaker }}%</span>
                    </dd>
                  </dl>
                </div>
                <div style="width:100%;" class="flex-box align-start">
                  <div v-if="getUserLevel('spot') < 8" class="progress-wrap-text flex-1">
                    <div class="progress-text">
                      <div class="fit-tc-secondary font-size-14 pd-b4">{{ $t('当前资产') }}</div>
                      <span class="fit-tc-primary font-size-16">{{ format(activeVip.dailyAssetUsdt, 4, true, true) }}<span class="font-size-12 mg-l4">USDT</span></span>
                    </div>
                    <div class="progress-box">
                      <div class="progress-cont" :style="`width:${format(DailyAssetPercent, 2, true)}%;`"></div>
                    </div>
                    <div class="progress-text">
                      <span class="fit-tc-primary font-size-14 next-text-box" v-html="$t('需增加 {asset} USDT 升级成为 {nextLevel}', { asset: format(needDailyAsset, 4, true, true), nextLevel: `VIP ${getUserLevel('spot') * 1 + 1}` })"></span>
                    </div>
                  </div>
                  <span v-if="getUserLevel('spot') < 8" class="font-size-12 fit-tc-secondary mg-lr12 mg-t70">{{ $t('或') }}</span>
                  <div v-if="getUserLevel('spot') < 8" class="progress-wrap-text flex-1 flex-box flex-column align-end">
                    <div class="progress-text text-right">
                      <div class="fit-tc-secondary font-size-14 pd-b4">{{ $t('现货30日交易额') }}</div>
                      <span class="fit-tc-primary font-size-16">{{ format(activeVip.spotTradeAmountUsdt, 4, true, true) }}<span class="font-size-12 mg-l4">USDT</span></span>
                    </div>
                    <div class="progress-box flex-box space-end">
                      <div class="progress-cont" :style="`width:${format(SpotTradeAmountPercent, 2, true)}%;`"></div>
                    </div>
                    <div class="progress-text flex-box space-between">
                      <span class="fit-tc-primary font-size-14 next-text-box text-right" v-html="$t('需现货交易 {asset} USDT 升级成为 {nextLevel}', { asset: format(needSpotTradeAmount, 4, true, true), nextLevel: `VIP ${getUserLevel('spot') * 1 + 1}` })"></span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="getUserLevel('spot') < 8" class="exchange-btn-box flex-box space-between">
                <el-button @click="toRecharge()">{{ $t('去充值') }}</el-button>
                <el-button type="primary" @click="toTrade('exchange')">{{ $t('去交易') }}</el-button>
              </div>
            </div>
          </div>
          <div class="exchange-item" :class="{'min': !isRateList}">
            <div class="flex-box flex-column exchange-item-pd">
              <div style="width:100%;">
                <h3 class="flex-box space-between">
                  <span class="font-size-16 mg-r8 fit-tc-primary">{{ $t('合约等级') }}</span>
                  <AccountLevelType class="type-level" :activeVip="activeVip" Ltype="contract" />
                </h3>
                <div class="taker-maker-cont">
                  <dl class="flex-box">
                    <dt class="fit-tc-secondary font-size-14">{{ $t('当前') }}</dt>
                    <dd>
                      <span class="fit-tc-secondary font-size-14">{{ $t('挂单') }}/{{ $t('吃单') }} ：</span>
                      <span class="fit-tc-primary font-size-14">{{ activeVip.contractMaker }}%/{{ activeVip.contractTaker }}%</span>
                    </dd>
                  </dl>
                  <dl v-if="getUserLevel('contract') < 8 && userInfo.is_crm_user * 1 === 0" class="flex-box mg-t8">
                    <dt class="fit-tc-secondary font-size-14">{{ $t('下一级') }}</dt>
                    <dd>
                      <span class="fit-tc-secondary font-size-14">{{ $t('挂单') }}/{{ $t('吃单') }} ：</span>
                      <span class="fit-tc-primary font-size-14"><span class="fit-tc-secondary no-txt font-size-12">{{ activeVip.contractMaker }}%</span>{{ activeVip.nextContractMaker }}%/<span class="fit-tc-secondary no-txt font-size-12">{{ activeVip.contractTaker }}%</span>{{ activeVip.nextContractTaker }}%</span>
                    </dd>
                  </dl>
                </div>
                <div style="width:100%;" class="flex-box align-start">
                  <div v-if="getUserLevel('contract') < 8 && userInfo.is_crm_user * 1 === 0"  class="progress-wrap-text flex-1">
                    <div class="progress-text">
                      <div class="fit-tc-secondary font-size-14 pd-b4">{{ $t('合约30日交易额') }}</div>
                      <span class="fit-tc-primary font-size-16">{{ format(activeVip.contractTradeAmountUsdt, 4, true, true) }}<span class="font-size-12 mg-l4">USDT</span></span>
                    </div>
                    <div class="progress-box">
                      <div class="progress-cont" :style="`width:${format(ContractTradeAmountPercent, 2, true)}%;`"></div>
                    </div>
                    <div class="progress-text flex-box space-between">
                      <span class="fit-tc-primary font-size-14 next-text-box" v-html="$t('需合约交易 {asset} USDT 升级成为 {nextLevel}', { asset: format(needContractTradeAmount, 4, true, true), nextLevel: `VIP ${getUserLevel('contract') * 1 + 1}` })"></span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="getUserLevel('contract') < 50" class="exchange-btn-box flex-box">
                <el-button @click="toTrade('future')">{{ $t('去交易') }}</el-button>
              </div>
            </div>
          </div>
        </div>
        <!--<div class="lasted-exchang-info">
          <div class="flex-box">
            <div class="exchange-num flex-box">
              <span>{{ $t('交易量') }}:</span>
            </div>
            <div class="flex-box box-box-left-right">
              <div class="exchange-type flex-box">
                <span class="fit-tc-secondary mg-r8">{{ $t('币币') }}({{ $t('近30天') }})</span>
                <em class="fit-tc-primary">{{ format(activeVip.spot_trade_amount_usdt, 4, true, true) }} USDT</em>
              </div>
              <div class="exchange-type flex-box">
                <span class="fit-tc-secondary mg-r8">{{ $t('合约') }}({{ $t('近7天') }})</span>
                <em class="fit-tc-primary">{{ format(activeVip.contract_trade_amount_usdt, 4, true, true) }} USDT</em>
              </div>
            </div>
          </div>
          <div class="flex-box mg-t12">
            <div class="exchange-num flex-box">
              <span>{{ $t('资产量') }}:</span>
            </div>
            <div class="flex-box box-box-left-right">
              <div class="exchange-type flex-box">
                <span class="fit-tc-secondary mg-r8">({{ $t('前一天') }})</span>
                <em class="fit-tc-primary">{{ format(activeVip.daily_asset_usdt, 4, true, true) }} USDT</em>
              </div>
            </div>
          </div>
        </div>-->
      </div>
    </div>
  </div>
  <AccountVerifyCodeDialog
    v-if="showVertify"
    :dialogVisible="showVertify"
    @request="verifyUserState"
    @handleClose="showVertify = false"
  />
</template>
<script lang="ts" setup>
  import { ElButton } from 'element-plus'
  import BigNumber from 'bignumber.js'
  import { format, isApp } from '~/utils'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import MonoRightArrowShort from '~/components/common/icon-svg/MonoRightArrowShort.vue'
  import AccountLevelType from '~/components/my/account/LevelType.vue'
  import { useUserStore } from '~/stores/useUserStore'
  import { useCommonData } from '~/composables/index'
  import { getVipDetail } from '~/api/tt.ts'
  import { setUserVisible } from '~/api/user.ts'
  const props = defineProps({
    isRateList: {
      type: Boolean,
      default: false
    }
  })
  const useCommon = useCommonData()
  const store = useUserStore()
  const { getUserInfoAction } = store
  const { userInfo, isLogin, isVisibleUserInfo } = storeToRefs(store)
  const { locale } = useI18n()
  const router = useRouter()
  const activeVip = ref({})
  const isOpen = ref(false)
  const showVertify = ref(false)
  const toTrade = (type) => {
    if (isApp()) {
      type === 'exchange' ? useCommonData().jsAppBridge('exchangeJump') : useCommonData().jsAppBridge('futureJump')
    } else {
      if (type === 'exchange') {
        router.push(`/${locale.value}/exchange/BTC_USDT`)
      } else if (type === 'future') {
        router.push(`/${locale.value}/future/BTC_USDT_SWAP`)
      }
    }
    
  }
  const toRecharge = () => {
    if (isApp()) {
      useCommon.jsAppBridge('rechargeJump', {
        symbol: 'USDT'
      })
    } else {
      router.push(`/${locale.value}/my/assets/deposit?symbol=USDT`)
    }
  }
  const getUserLevel = (type = 'spot') => {
    return type === 'spot' ? userInfo.value.user_level : userInfo.value.contract_level
  }
  const isLoading = ref(true)
  const getVipInfo = async() => {
    isLoading.value = true
    const { data } = await getVipDetail()
    if (data) {
      activeVip.value = data
      activeVip.value.dailyAssetUsdt = activeVip.value.daily_asset_usdt * 1
      activeVip.value.spotTradeAmountUsdt = activeVip.value.spot_trade_amount_usdt * 1
      activeVip.value.contractTradeAmountUsdt = activeVip.value.contract_trade_amount_usdt * 1
      activeVip.value.spotMaker = Math.round((new BigNumber(activeVip.value.spot_maker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.spotTaker = Math.round((new BigNumber(activeVip.value.spot_taker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.contractMaker = Math.round((new BigNumber(activeVip.value.contract_maker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.contractTaker = Math.round((new BigNumber(activeVip.value.contract_taker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.nextSpotMaker = Math.round((new BigNumber(activeVip.value.spot_next_level_obj.spot_maker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.nextSpotTaker = Math.round((new BigNumber(activeVip.value.spot_next_level_obj.spot_taker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.nextContractMaker = Math.round((new BigNumber(activeVip.value.contract_next_level_obj.contract_maker).multipliedBy(100).toNumber()) * 10000) / 10000
      activeVip.value.nextContractTaker = Math.round((new BigNumber(activeVip.value.contract_next_level_obj.contract_taker).multipliedBy(100).toNumber()) * 10000) / 10000
    }
    isLoading.value = false
  }
  const nextDailyAsset = computed(() => {
    return (activeVip.value && activeVip.value.spot_next_level_obj && activeVip.value.spot_next_level_obj.daily_asset * 1) + 1
  })
  const nextSpotTradeAmount = computed(() => {
    return (activeVip.value && activeVip.value.spot_next_level_obj && activeVip.value.spot_next_level_obj.spot_trade_amount * 1) + 1
  })
  const nextContractTradeAmount = computed(() => {
    return (activeVip.value && activeVip.value.contract_next_level_obj && activeVip.value.contract_next_level_obj.contract_trade_amount * 1) + 1
  })
  const DailyAssetPercent = computed(() => {
    const result = new BigNumber(activeVip.value && activeVip.value.dailyAssetUsdt).div(nextDailyAsset.value).multipliedBy(100)
    return result > 100 ? 100 : result
  })
  const SpotTradeAmountPercent = computed(() => {
    const result = new BigNumber(activeVip.value && activeVip.value.spotTradeAmountUsdt).div(nextSpotTradeAmount.value).multipliedBy(100)
    return result > 100 ? 100 : result
  })
  const ContractTradeAmountPercent = computed(() => {
    const result = new BigNumber(activeVip.value && activeVip.value.contractTradeAmountUsdt).div(nextContractTradeAmount.value).multipliedBy(100)
    return result > 100 ? 100 : result
  })
  const needDailyAsset = computed(() => {
    const result = new BigNumber(nextDailyAsset.value).minus(activeVip.value && activeVip.value.dailyAssetUsdt)
    return result < 0 ? 0 : result
  })
  const needSpotTradeAmount = computed(() => {
    const result = new BigNumber(nextSpotTradeAmount.value).minus(activeVip.value && activeVip.value.spotTradeAmountUsdt)
    return result < 0 ? 0 : result
  })
  const needContractTradeAmount = computed(() => {
    const result = new BigNumber(nextContractTradeAmount.value).minus(activeVip.value && activeVip.value.contractTradeAmountUsdt)
    return result < 0 ? 0 : result
  })
  const updateUserState = (val) => {
    isOpen.value = val ? 0 : 1
    if (!val) {
      showVertify.value = true
    } else {
      verifyUserState()
    }
  }
  const verifyUserState = async(code) => {
    const { data, error } = await setUserVisible({
      is_open: isOpen.value,
      totp_code: code ? code : undefined
    })
    if (data) {
      showVertify.value = false
      getUserInfoAction()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onBeforeMount(() => {
    getVipInfo()
  })
</script>
<style lang="scss" scoped>
dl,
dt,
dd,
p,
h1,
h2,
h3,
h4,
ul,
li {
  padding: 0;
  margin: 0;
  font-weight: 500;
}
em {
  font-style: normal;
}
.exchange-info {
  position: relative;
  margin-top:20px;
  margin-bottom:40px;
  .info-title{
    // border: 1px solid;
    // border-top-left-radius:12px;
    // border-top-right-radius:12px;
    padding:16px 0;
    // background: linear-gradient(90deg, rgba(240, 185, 11, 0.08) 0%, rgba(240, 185, 11, 0.02) 100%);
    // border:1px solid rgba(240, 185, 11, 0.2);
    p{
      font-size:14px;
      @include color(tc-secondary);
    }
    .info-box{
    }
    .title-left,.title-center{
      margin-right:60px;
    }
    .head-icon{
      width:46px;
      height:46px;
      border-radius:50%;
      @include color(tc-primary);
      background:rgba(240, 185, 11, 0.6);
      svg{
        @include color(tc-primary);
      }
    }
    .title-icon{
      width:28px;
      height:28px;
      background-size:100% auto;
      margin-right:4px;
      @include get-img('@/assets/images/my/icon-feilv-light.png', '@/assets/images/my/icon-feilv-dark.png');
    }
    .more-btn{
      a{
        font-size:16px;
        @include color(theme);
        margin-right:8px;
      }
      @include color(theme);
    }
  }
  .rate-table-box{
    // border-bottom-left-radius:12px;
    // border-bottom-right-radius:12px;
    // border:1px solid;
    border-top:0;
    @include border-color(border);
    .sx-price-info {
      border-bottom: 1px solid;
      padding: 12px 20px;
      border-top-left-radius: 12px;
      border-top-right-radius:12px;
      @include bg-color(bg-secondary);
      @include border-color(border);
      .discount {
        display: block;
        padding: 4px 8px;
        border-radius: 2px;
        font-size: 12px;
        line-height: 12px;
        background: linear-gradient(90deg, #e8548b 0%, #e63477 100%);
        @include color(tc-button);
        margin-right: 8px;
      }
    }
    .exchange-wrap-info {
      width:100%;
      height: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .exchange-item{
        &.heiAuto{
          height:auto;
        }
        &:first-child{
          margin-right:16px;
        }
        &:nth-child(1){
          flex:1.5;
          &.special{
            flex:1;
          }
        }
        &:nth-child(2){
          flex:1;
        }
        position:relative;
        border-radius: 12px;
        height: auto;
        margin: 0 20px 16px 0;
        max-width: inherit;
        min-width: inherit;
        position: relative;
        width: calc(50% - 12px);
        border: 1px solid;
        @include border-color(border);
        &:last-child{
          margin-right:0;
        }
        .exchange-item-pd{
          padding:20px 16px;
          justify-content:space-between;
          height:100%;
        }
        h3{
          width:100%;
        }
        .taker-maker-cont{
          margin-top:12px;
          border-radius:8px;
          padding:12px;
          @include bg-color(bg-quaternary);
          dl{
            dt{
              width:20%;
            }
            dd{
              .no-txt{
                text-decoration: line-through;
              }
            }
          }
        }
        .progress-wrap-text{
          padding-top:12px;
          .progress-box{
            margin:8px 0;
            width:100%;
            height:4px;
            border-radius:8px;
            overflow:hidden;
            @include bg-color(bg-fourthary);
            .progress-cont{
              border-radius:8px;
              height:4px;
              @include bg-color(theme);
            }
          }
        }
        .mg-t70{
          margin-top:70px;
        }
        .end-box{
          width:100%;
        }
        .exchange-btn-box{
          width:100%;
          margin-top:20px;
          .el-button{
            height:40px;
            width:120px;
          }
        }
      }
    }
    .lasted-exchang-info {
      padding: 22px 0;
      span,
      em {
        display: block;
        line-height: 24px;
      }
      .exchange-num {
        .icon-exchange {
          display: block;
          width: 20px;
          height: 20px;
        }
        span {
          font-weight: 400;
          font-size: 14px;
          @include color(tc-primary);
          padding-right: 0px;
          position: relative;
          &:after {
            // content: "";
            // width: 1px;
            // height: 24px;
            // @include bg-color(border);
            // position: absolute;
            // top: 50%;
            // right: 0;
            // margin-top: -12px;
          }
        }
      }
      .exchange-type {
        padding-left: 16px;
        font-size: 14px;
      }
    }
  }
  .info-banner {
    max-width:1200px;
    margin: 0 auto;
    margin-top: 28px;
    dl {
      width: 100%;
      height: 95px;
      &.sock {
        // @include get-img('~@/assets/images/account/swiper-img1-light.png', '~@/assets/images/account/swiper-img1-dark.png');
      }
      &.invite {
        // @include get-img('~@/assets/images/account/swiper-img2-light.png', '~@/assets/images/account/swiper-img2-dark.png');
        dd {
          .right-text {
            p {
              padding-right: 16px;
              &:after {
                height: 16px;
                margin-top: -8px;
              }
            }
          }
        }
      }
      background-size: 100% auto;
      dt {
        width: 88px;
        height: 68px;
        img {
          width: 88px;
          height: 68px;
        }
      }
      dd {
        margin-left:94px;
        .left-text {
          max-width: 500px;
          margin-left: 8px;
          @include color(tc-primary);
          font-size: 16px;
        }
        .right-text {
          p {
            height: 68px;
            padding-right: 12px;
            position: relative;
            &:after {
              content: "";
              position: absolute;
              top: 50%;
              right: 0;
              margin-top: -12px;
              width: 1px;
              height: 24px;
              background: #EFF0F2;
            }
          }
          .btn-box {
            padding: 0 24px;
            .el-button {
              padding: 8px 24px;
              &.white-btn {
                @include bg-color(bg-tertiary);
                @include border-color(bg-tertiary);
                span {
                  @include color(tc-secondary);
                }
              }
              &.green-btn {
                @include bg-color(theme);
                @include border-color(theme);
              }
            }
          }
        }
      }
    }
    .pagation-nav {
      padding: 8px 0;
      .swiper-pagination-bullet {
        background: #7d92a8;
        opacity: 1;
        &.swiper-pagination-bullet-active {
          width: 16px;
          border-radius: 9px;
          @include bg-color(theme);
        }
      }
    }
  }
}
@include pc{
  .exchange-info{
    .rate-table-box{
      .exchange-wrap-info {
        .exchange-item{
          max-width: calc(50% - 12px);
          min-width: calc(50% - 12px);
        }
      }
    }
  }
}
@media only screen and (min-width: 1060px) and (max-width: 1430px) {
  .exchange-info{
    .rate-table-box{
      .exchange-wrap-info {
        .exchange-item{
          max-width: inherit;
          min-width: inherit;
          &:nth-child(1){
            flex:1.5;
          }
          &:nth-child(2){
            flex:1;
          }
          &.min{
            width: calc(50% - 12px);
            max-width: inherit;
            min-width: inherit;
          }
        }
      }
    }
  }
}
@include mb {
  .exchange-info {
    .info-title{
      padding:0;
      text-align:center;
      background:none;
      border:0;
      .head-icon{
        width:40px;
        height:40px;
      }
      .font-size-24{
        font-size:16px !important;
      }
      &.flex-box{
        justify-content: space-between;
      }
      .info-box{
        margin-top:0;
        height:18px;
        line-height:18px;
        font-size:14px;
        svg{
          margin-left:8px;
          font-size:14px !important;
        }
      }
      .title-left,.title-center{
        margin-right:0;
      }
      .title-icon{
        width:20px;
        height:20px;
      }
      .more-btn{
        a{
          font-size:14px;
          margin-right:0px;
        }
      }
    }
    .rate-table-box{
      border-bottom-left-radius:0px;
      border-bottom-right-radius:0px;
      border:0px solid;
      border-top:0;
      @include border-color(border);
      .sx-price-info {
        border-bottom: 1px solid;
        padding: 12px 20px;
        border-top-left-radius: 12px;
        border-top-right-radius:12px;
        @include bg-color(bg-secondary);
        @include border-color(border);
        .discount {
          display: block;
          padding: 4px 8px;
          border-radius: 2px;
          font-size: 12px;
          line-height: 12px;
          background: linear-gradient(90deg, #e8548b 0%, #e63477 100%);
          @include color(tc-button);
          margin-right: 8px;
        }
      }
      .exchange-wrap-info {
        padding:0;
        &.flex-box{
          flex-direction: column;
        }
        .exchange-item{
          width:100%;
          max-width:100%;
          min-width:100%;
          margin:0;
          &:first-child{
            margin-right:0;
          }
          margin-top:8px;
          height:auto;
          h3{
            width:100%;
          }
          .progress-wrap-text{
            padding-top:12px;
            .progress-text{
              span{
                &.font-size-14{
                  font-size:12px !important;
                }
              }
            }
            .progress-box{
              margin:4px 0;
              width:100%;
              height:4px;
              border-radius:8px;
              background-color:rgba(240, 185, 11, 0.2);
              .progress-cont{
                border-radius:8px;
                height:4px;
                @include bg-color(theme);
              }
            }
          }
          .mg-t70{
            margin-top:62px;
          }
          ul{
            &.font-size-14{
              font-size:12px !important;
            }
          }
          .end-box{
            width:100%;
          }
          .exchange-btn-box{
            position:static;
            width:100%;
            margin-top:12px;
            .el-button{
              height:40px;
              width:100%;
            }
          }
        }
      }
      .lasted-exchang-info {
        padding: 22px 10px;
        .flex-box{
          display:block;
        }
        span,
        em {
          display: block;
          line-height: 24px;
        }
        .box-box-left-right{
          padding-left:0px;
        }
        .exchange-num {
          .icon-exchange {
            display: block;
            width: 20px;
            height: 20px;
          }
          span {
            font-weight: 400;
            font-size: 14px;
            @include color(tc-primary);
            padding-right: 0px;
            position: relative;
            &:after {
              display:none;
            }
          }
        }
        .exchange-type {
          padding-left: 0px;
          font-size: 14px;
          &.flex-box{
            display:flex;
          }
        }
      }
    }
    .info-banner {
      max-width:1200px;
      margin: 0 auto;
      margin-top: 28px;
      dl {
        width: 100%;
        height: 95px;
        &.sock {
          // @include get-img('~@/assets/images/account/swiper-img1-light.png', '~@/assets/images/account/swiper-img1-dark.png');
        }
        &.invite {
          // @include get-img('~@/assets/images/account/swiper-img2-light.png', '~@/assets/images/account/swiper-img2-dark.png');
          dd {
            .right-text {
              p {
                padding-right: 16px;
                &:after {
                  height: 16px;
                  margin-top: -8px;
                }
              }
            }
          }
        }
        background-size: 100% auto;
        dt {
          width: 88px;
          height: 68px;
          img {
            width: 88px;
            height: 68px;
          }
        }
        dd {
          margin-left:94px;
          .left-text {
            max-width: 500px;
            margin-left: 8px;
            @include color(tc-primary);
            font-size: 16px;
          }
          .right-text {
            p {
              height: 68px;
              padding-right: 12px;
              position: relative;
              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 0;
                margin-top: -12px;
                width: 1px;
                height: 24px;
                background: #EFF0F2;
              }
            }
            .btn-box {
              padding: 0 24px;
              .el-button {
                padding: 8px 24px;
                &.white-btn {
                  @include bg-color(bg-tertiary);
                  @include border-color(bg-tertiary);
                  span {
                    @include color(tc-secondary);
                  }
                }
                &.green-btn {
                  @include bg-color(theme);
                  @include border-color(theme);
                }
              }
            }
          }
        }
      }
      .pagation-nav {
        padding: 8px 0;
        .swiper-pagination-bullet {
          background: #7d92a8;
          opacity: 1;
          &.swiper-pagination-bullet-active {
            width: 16px;
            border-radius: 9px;
            @include bg-color(theme);
          }
        }
      }
    }
  }
}
</style>