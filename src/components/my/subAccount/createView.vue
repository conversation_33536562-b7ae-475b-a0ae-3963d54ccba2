<template>
  <div>
    <p v-if="isAuthorize" class="son-tips">{{ $t('*可转发他人使用的授权子账户信息') }}</p>
    <ul v-else class="son-tips-ul">
      <li>{{ $t('根据您的用途、策略和资金规划进一步创建多个快捷子账户自用，每个快捷子账户单独核算资金，便于您能实施不同的交易策略。') }}</li>
      <li>{{ $t('需要授权给他人使用时，前往子账户管理补充该子账户的用户名、登录密码和谷歌验证码即可分享给他人使用。') }}</li>
      <li>{{ $t('当前您可以创建 20 个快捷子账户，您已经创建了 {length} 个。', { length: len }) }}</li>
    </ul>
    <div class="sub-account-create">
      <el-form ref="addFormRef" :model="addForm" :rules="addFormRules">
        <el-form-item v-if="isAuthorize" :label="$t('子账户名称')" prop="name">
          <el-input v-model="addForm.name" type="text" :placeholder="$t('6-20个字符，支持大小写字母、数字，不支持特殊字符')" />
        </el-form-item>
        <el-form-item v-if="isAuthorize" :label="$t('子账户密码')" prop="pwd">
          <el-input v-model="addForm.pwd" :type="isShowEye ? 'text' : 'password'" :placeholder="$t('8-20个字符，需包含大小写字母、数字和特殊字符')">
            <template #suffix>
              <div class="flex-box space-end eye-icon" @click="isShowEye = !isShowEye">
                <MonoEyeClose v-if="!isShowEye" />
                <MonoEyeOpen v-if="isShowEye" />
              </div>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('备注')" prop="desc">
          <el-input v-model="addForm.desc" maxlength="20" type="text" :placeholder="$t('最多20个字符，支持大小写字母、数字，特殊字符')" />
        </el-form-item>
        <el-form-item v-if="isAuthorize" :label="$t('谷歌验证码')" prop="code">
          <GoogleCodeInputMin v-model="addForm.code" :defaultFocus="false" />
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-box mg-t40 btn-box">
      <el-button @click="emit('close')">{{ $t('取消') }}</el-button>
      <el-button :disabled="isDisabled" type="primary" @click="confirm">{{ $t('确认') }}</el-button>
    </div>
  </div>
  <el-dialog class="sub-account-dialog" v-model="isConfirm" :title="$t('授权子账号信息')" width="480px" @close="getItFun">
    <p class="son-tips">{{ $t('*可转发他人使用的授权子账户信息') }}</p>
    <el-form>
      <el-form-item :label="$t('子账户名称')">
        <div class="text-info">{{ confirmItem.name }}</div>
      </el-form-item>
      <el-form-item :label="$t('登录密码')">
        <div class="text-info">{{ confirmItem.pwd }}</div>
        <p class="info-tips"><span class="fit-theme cursor-pointer" @click="useCommon.copy(`${$t('子账户名称')}: ${confirmItem.name}\n${$t('登录密码')}: ${confirmItem.pwd}`, $t('复制成功！'))">{{ $t('复制授权子账户信息') }}</span>{{ $t('转发给您授权的人使用') }}</p>
      </el-form-item>
      <el-form-item :label="$t('备注')">
        <div class="text-info">{{ confirmItem.desc }}</div>
      </el-form-item>
    </el-form>
    <div class="btn-box">
      <el-button type="primary" class="mg-t40" @click="getItFun">{{ $t('明白了') }}</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElForm, ElFormItem, ElInput } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { isValidSubAccountName, isValidSubAccountPwd} from '~/utils/index'
  import { addForSon, getSonGoolgKey, setSonDetailInfo } from '~/api/user'
  import GoogleCodeInputMin from '~/components/common/GoogleCodeInput.vue'
  import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
  import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const props = defineProps({
    isAuthorize: {
      type: Boolean,
      default: false
    },
    len: {
      type: Number,
      default: 0
    },
    currentItem: {
      type: Object,
      default () {
        return {}
      }
    }
  })
  const emit = defineEmits(['close', 'request'])
  const isShowEye = ref(false)
  const isConfirm = ref(false)
  interface AddForm{
    name: String,
    pwd: String,
    desc: String,
    code: String
  }
  const addFormRef = ref<FormInstance>()
  const addForm = reactive<AddForm>({
    name: '',
    pwd: '',
    desc: '',
    code: ''
  })
  const valideDesc = (rule, value, callback) => {
    if (!value) {
      return callback(new Error(t('请输入备注')))
    } else {
      return callback()
    }
  }
  const valideName = (rule, value, callback) => {
    if (!value) {
      return callback(new Error(t('请输入子账户名称')))
    } else if (!isValidSubAccountName(value)) {
      return callback(new Error(t('请输入正确的子账户名称')))
    } else {
      return callback()
    }
  }
  const validePwd = (rule, value, callback) => {
    if (!value) {
      return callback(new Error(t('请输入子账户密码')))
    } else if (!isValidSubAccountPwd(value)) {
      return callback(new Error(t('8-20个字符，需包含大小写字母、数字和特殊字符')))
    } else {
      return callback()
    }
  }
  const addFormRules = reactive<FormRules<AddForm>>({
    name: [
      { required: true, message: t('请输入子账户名称'), trigger: ['blur', 'change'] },
      { validator: valideName, trigger: ['blur', 'change'] }
    ],
    pwd: [
      { required: true, message: t('请输入子账户密码'), trigger: ['blur', 'change'] },
      { validator: validePwd, trigger: ['blur', 'change'] }
    ],
    desc: [
      { required: true, message: t('请输入子账户备注'), trigger: ['blur', 'change'] },
      { validator: valideDesc, trigger: ['blur', 'change'] }
    ]
  })
  const isDisabled = computed(() => {
    if ((!props.isAuthorize && addForm.desc !== '') || (props.isAuthorize && addForm.pwd !== '' && addForm.desc !== '' && addForm.name !== '' && addForm.code !== '' && addForm.code.length === 6 && isValidSubAccountName(addForm.name) && isValidSubAccountPwd(addForm.pwd))) {
      return false
    } else {
      return true
    }
  })
  const confirmItem = ref({})
  const confirm = async() => {
    if (isDisabled.value) {
      return false
    }
    const { data, error } = props.currentItem.user_id ? await setSonDetailInfo({
      son_id: props.currentItem.user_id,
      ...addForm
    }) : await addForSon({
      son_type: props.isAuthorize ? 2 : 1,
      ...addForm
    })
    if (data) {
      useCommon.showMsg('success', t('新增成功！'))
      if (props.isAuthorize) {
        isConfirm.value = true
        confirmItem.value = addForm
        emit('request')
      } else {
        emit('close')
        emit('request')
      }
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const getItFun = () => {
    isConfirm.value = false
    emit('close')
  }
  onMounted(() => {
    addForm.desc = props.currentItem.desc ? props.currentItem.desc : ''
  })
</script>