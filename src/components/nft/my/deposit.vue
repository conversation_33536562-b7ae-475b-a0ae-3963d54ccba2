<template>
  <div class="nft-deposit-withdrawal-cont">
    <div class="nft-deposit-withdrawal-wrap">
      <div class="nft-deposit-withdrawal-title">
        {{ $t('充值NFT') }}
      </div>
      <div class="nft-deposit-withdrawal-box">
        <el-steps direction="vertical" :active="activeStep">
          <el-step :title="$t('充值NFT')">
            <template #description>
              <div class="step-content">
                <CoinListSelect v-model="search.symbol" :isDeposit="true" :isNFT="true" />
              </div>
            </template>
          </el-step>
          <el-step :title="$t('充值地址')">
            <template #description>
              <div v-if="activeStep === 1 && JSON.stringify(nftAddressInfo) !== '{}'" class="step-content">
                <div class="address-cont">
                  <dl v-if="(nftAddressInfo ||{}).address.includes('http')" class="address-wrap flex-box flex-column">
                    <p class="fit-tc-secondary font-size-14 pd-b12">{{ $t('使用 Mixin Messenger 扫码') }}</p>
                    <dt>
                      <BoxQrcode :size="150" :value="nftAddressInfo.address" />
                    </dt>
                  </dl>
                  <dl v-else class="address-wrap flex-box">
                    <dd class="flex-1">
                      <h3>{{ $t('地址') }}</h3>
                      <p class="flex-box">
                        <span class="break-words">{{ nftAddressInfo.address}}</span>
                        <MonoCopy size="16" style="cursor:pointer;" @click="useCommon.copy(nftAddressInfo.address, $t('复制成功！'))" />
                      </p>
                    </dd>
                  </dl>
                  <p class="info-p flex-box space-between">
                    <span>{{ $t('充值到账钱包：') }}</span>
                    <em>{{ $t('我的NFT资产') }}</em>
                  </p>
                  <p class="fit-warn font-size-14">{{ $t('*请确保您绑定的钱包中有足够的资金来支付燃料费。') }}</p>
                </div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
      <div class="nft-deposit-withdrawal-title">
        {{ $t('最近充值记录') }}
        <NuxtLink :to="`/${locale}/nft/my/assets/deposit-list`">{{ $t('查看更多') }}</NuxtLink>
      </div>
      <div class="nft-deposit-withdrawal-list">
        <DepositList />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ElSteps, ElStep, ElButton } from 'element-plus'
  import DepositList from '~/components/nft/my/deposit-list'
  import CoinListSelect from '~/components/common/CoinListSelect.vue'
  import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
  import { getNftAddrApi } from '~/api/tf'
  import { useCommonData } from '~/composables/index'
  const { locale, t } = useI18n()
  const useCommon = useCommonData()
  const search = ref({
    symbol: ''
  })
  const nftAddressInfo = ref({})
  const activeStep = computed(() => {
    let number = 0
    if (search.value.symbol) {
      number = 1
    }
    return number
  })
  watch(() => search.value.symbol, (val) => {
    getNftAddress()
  })
  const getNftAddress = async() => {
    const { data } = await getNftAddrApi({
      nft_id: search.value.symbol
    })
    if (data) {
      nftAddressInfo.value = data
    }
  }
</script>
<style lang="scss" scoped>
  @import '@/assets/style/nft/deposit-withdrawal.scss';
</style>