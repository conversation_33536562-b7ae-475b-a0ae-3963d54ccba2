<template>
  <div class="number-animation" :class="{ 'mobile': isMobile }">
    {{ displayNumber }}
  </div>
</template>

<script lang='ts' setup>
import { ref, watch, onMounted } from 'vue';

interface Props {
  targetNumber: string | number;
  duration?: number;
  isMobile?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 800,
  isMobile: false
});

const displayNumber = ref('0');
let animationId: number | null = null;

const animateNumber = (start: number, end: number, duration: number) => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  const startTime = performance.now();

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = start + (end - start) * easeOutQuart;

    const targetStr = String(end);
    const decimalPlaces = targetStr.includes('.') ? targetStr.split('.')[1].length : 0;
    displayNumber.value = current.toFixed(decimalPlaces);

    if (progress < 1) {
      animationId = requestAnimationFrame(animate);
    } else {
      displayNumber.value = String(end);
      animationId = null;
    }
  };

  animationId = requestAnimationFrame(animate);
};

watch(() => props.targetNumber, (newValue, oldValue) => {
  if (!newValue || newValue === '' || newValue === '0' || isNaN(Number(newValue))) {
    displayNumber.value = '0';
    return;
  }

  const newNum = parseFloat(String(newValue));
  const oldNum = parseFloat(String(oldValue)) || 0;

  if (newNum !== oldNum && !isNaN(newNum)) {
    animateNumber(oldNum, newNum, props.duration);
  }
}, { immediate: true });

onMounted(() => {
  const initialValue = props.targetNumber;
  if (!initialValue || initialValue === '' || initialValue === '0' || isNaN(Number(initialValue))) {
    displayNumber.value = '0';
  } else {
    displayNumber.value = String(parseFloat(String(initialValue)));
  }
});
</script>

<style lang='scss' scoped>
.number-animation {
  font-size: 24px;
  font-weight: 500;
  color: #414655;
  border: none;
  background: transparent;
  padding: 0;
  line-height: 25px;

  .dark & {
    color: #fff;
  }

  &.mobile {
    font-size: 16px;
    font-weight: 500;
    color: #414655;

    .dark & {
      color: #fff;
    }
  }
}
</style>