<template>
  <div
    class="skeleton-item"
    :class="{ 'animated': animated }"
    :style="skeletonStyle"
  >
    <div class="skeleton-shimmer"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: 16
  },
  borderRadius: {
    type: [String, Number],
    default: 4
  },
  animated: {
    type: Boolean,
    default: true
  }
})

const skeletonStyle = computed(() => {
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  const borderRadius = typeof props.borderRadius === 'number' ? `${props.borderRadius}px` : props.borderRadius

  return {
    width,
    height,
    borderRadius
  }
})
</script>

<style lang="scss" scoped>
.skeleton-item {
  position: relative;
  overflow: hidden;
  background-color: #f2f3f4;

  .dark & {
    background-color: #25282F;
  }

  &.animated {
    background: linear-gradient(90deg, #f2f3f4 25%, #e6e7e8 50%, #f2f3f4 75%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;

    .dark & {
      background: linear-gradient(90deg, #25282F 25%, #2d3139 50%, #25282F 75%);
      background-size: 400% 100%;
    }
  }
}

.skeleton-shimmer {
  display: none;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}


</style>