import { storeToRefs } from "pinia"
import { ref, watch, computed } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'
import { marketSocket } from '~/utils'

// 模块级缓存，全局共享
const klineCache = new Map()
const CACHE_EXPIRY_TIME = 5 * 60 * 1000 // 优化：延长为5分钟缓存过期时间
const CACHE_MAX_SIZE = 100 // 最大缓存条目数
let cacheCleanupInterval: NodeJS.Timeout | null = null

// 缓存管理函数
function setCacheData(key: string, data: any) {
  // 限制缓存大小
  if (klineCache.size >= CACHE_MAX_SIZE) {
    // 删除最老的条目
    const firstKey = klineCache.keys().next().value
    klineCache.delete(firstKey)
  }

  klineCache.set(key, {
    data,
    timestamp: Date.now()
  })

  // 启动定期清理机制（如果还未启动）
  if (!cacheCleanupInterval) {
    startCacheCleanup()
  }
}

function getCacheData(key: string) {
  const cached = klineCache.get(key)
  if (!cached) return null

  // 检查缓存是否过期
  if (Date.now() - cached.timestamp > CACHE_EXPIRY_TIME) {
    klineCache.delete(key)
    return null
  }

  return cached.data
}

// 批量清理过期缓存
function startCacheCleanup() {
  if (cacheCleanupInterval) return

  cacheCleanupInterval = setInterval(() => {
    const now = Date.now()
    for (const [key, value] of klineCache.entries()) {
      if (now - value.timestamp > CACHE_EXPIRY_TIME) {
        klineCache.delete(key)
      }
    }

    // 如果缓存为空，停止清理
    if (klineCache.size === 0 && cacheCleanupInterval) {
      clearInterval(cacheCleanupInterval)
      cacheCleanupInterval = null
    }
  }, 60000) // 每分钟清理一次
}

// 清理所有缓存
function clearCache() {
  klineCache.clear()
  if (cacheCleanupInterval) {
    clearInterval(cacheCleanupInterval)
    cacheCleanupInterval = null
  }
}

// 首屏历史就绪标记：key = `${symbol}_#_${resolution}`，用于阻断首屏前的WS实时推送
// 专业版会话直连WS：彻底消除首屏竞态与中间层延迟
const MAX_HISTORY_WAIT_MS = 800 // 首屏历史最大等待阈值（超时后先放行实时）

type TVResolution = string // '1'|'5'|'60'|'1D'|'1W'|'1M' 等
interface SessionSub {
  uid: string
  cb: (bar: any) => void
}
interface KlineSession {
  symbol: string
  resolution: TVResolution
  stream: string
  subs: SessionSub[]
  wsCb?: (res: any) => void
  historyReady: boolean
  timedOut: boolean
  timeoutId?: any
  queuedBars?: any[]
  wsSubscribed: boolean
}
const sessions = new Map<string, KlineSession>()

function getSessionKey(symbol: string, resolution: TVResolution) {
  return `${symbol}_#_${resolution}`
}
function getStream(symbol: string, resolution: TVResolution) {
  const isSwap = symbol.includes('_SWAP')
  const periodMap: Record<string, string> = {
    '1': '1m', '3': '3m', '5': '5m', '15': '15m', '30': '30m',
    '60': '1h', '120': '2h', '240': '4h', '360': '6h', '480': '8h', '720': '12h',
    '1D': '1d', '3D': '3d', '1W': '1w', '1M': '1M'
  }
  const period = periodMap[resolution] || String(resolution)
  return `${isSwap ? 'lpc.' : 'spot.'}${symbol}.candles.${period}`
}
function deliverToSubs(session: KlineSession, bar: any) {
  // 轻量去重：若与最近一次已分发的bar时间戳相同且OHLCV未变化，则跳过
  const lastKey = (session as any)._lastKey
  const currKey = `${bar.time}|${bar.open}|${bar.high}|${bar.low}|${bar.close}|${bar.volume}`
  if (lastKey === currKey) return
  ;(session as any)._lastKey = currKey
  session.subs.forEach(s => {
    try { s.cb(bar) } catch (e) { /* ignore */ }
  })
}

const firstScreenReady = new Map<string, boolean>()


export default function useDatafeedAction(params = {}) {
  const {
    pairDecimals = 8
  } = params
  const store = commonStore()
  const pair = ref('')
  const interval = ref('')
  const preObj = ref({})
  const { getKlineSocket, cancelKline } = store
  const { klineList, klineTicker, ticker, priceScale } = storeToRefs(store)

  // 周期切换状态管理（保留以备将来需要，当前不会阻止更新）
  const resolutionChangingState = ref({
    isChanging: false,
    symbol: null as string | null,
    resolution: null as string | null,
    timestamp: null as number | null
  })
  const resolutionMap: any = {
    1: '1m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  function formatSymbol(symbol: string) {
    // 添加空值检查，防止缓存清除后symbol为undefined
    if (!symbol) {
      return ''
    }
    return symbol.toUpperCase()
  }
  let key = ''
  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    // 参数验证，防止symbolInfo或其属性为空
    if (!symbolInfo || !symbolInfo.fullName) {
      console.warn('[Datafeed] Invalid symbolInfo:', symbolInfo)
      onHistoryCallback([], { noData: true })
      return
    }

    // 立即设置pair和interval，确保WebSocket订阅能正确建立
    // 这必须在任何return之前执行，否则基本版1W/1M无法建立WebSocket连接
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]

    // 首屏就绪 gating：首屏发起时将就绪标记置为 false，阻断WS实时推送先于历史渲染
    const screenKey = `${symbolInfo.fullName}_#_${resolution}`
    if (periodParams.firstDataRequest) {
      firstScreenReady.set(screenKey, false)
    }

    // 专业版会话：创建/获取会话并启动首屏最大等待计时与直连WS订阅
    const sessKey = getSessionKey(symbolInfo.fullName, resolution)
    let session = sessions.get(sessKey)
    if (!session) {
      session = {
        symbol: symbolInfo.fullName,
        resolution,
        stream: getStream(symbolInfo.fullName, resolution),
        subs: [],
        historyReady: false,
        timedOut: false,
        timeoutId: undefined,
        queuedBars: [],
        wsSubscribed: false,
      }
      sessions.set(sessKey, session)
    }
    if (periodParams.firstDataRequest && !session.timeoutId) {
      session.timeoutId = setTimeout(() => {
        session.timedOut = true
        // 周/月（1W/1M）首屏严格历史优先：不放行排队帧，避免“先WS再历史”的闪屏
        const isLongPeriod = (resolution === '1W' || resolution === '1M')
        if (!isLongPeriod && session.subs && session.subs.length) {
          // 非周/月：超时后如已有订阅者，按时间顺序冲洗队列，保障秒见价
          if (session.queuedBars && session.queuedBars.length) {
            session.queuedBars.forEach(b => deliverToSubs(session, b))
            session.queuedBars = []
          }
        }
      }, MAX_HISTORY_WAIT_MS)
    }
    if (!session.wsSubscribed) {
      const stream = session.stream
      marketSocket.send({ method: 'SUBSCRIBE', params: [stream] })
      // 会话守护：若长时间无订阅者，自动退订与清理，避免泄漏
      const guardKey = `${session.symbol}_#_${session.resolution}`
      if (!(marketSocket as any)._sessionGuards) (marketSocket as any)._sessionGuards = new Map()
      if (!(marketSocket as any)._sessionGuards.get(guardKey)) {
        const guardTimer = setInterval(() => {
          const s = sessions.get(getSessionKey(session.symbol, session.resolution))
          if (!s) { clearInterval(guardTimer); (marketSocket as any)._sessionGuards.delete(guardKey); return }
          // 无订阅者且历史未就绪超过一定时间则清理（不改对外接口、仅防泄漏）
          if ((!s.subs || s.subs.length === 0) && s.timeoutId == null && s.historyReady === false) {
            try { marketSocket.off(stream, s.wsCb) } catch (e) {}
            try { marketSocket.send({ method: 'UNSUBSCRIBE', params: [stream] }) } catch (e) {}
            try { clearInterval(guardTimer) } catch (e) {}
            ;(marketSocket as any)._sessionGuards.delete(guardKey)
            sessions.delete(getSessionKey(session.symbol, session.resolution))
          }
        }, 10000) // 每10s检查一次
        ;(marketSocket as any)._sessionGuards.set(guardKey, guardTimer)
      }
      const wsCb = (res: any) => {
        if ((res.t === 1 || res.t === 0) && res.d && res.d.length) {
          const latest = res.d[res.d.length - 1]
          const bar = { time: latest.time, open: latest.open, high: latest.high, low: latest.low, close: latest.close, volume: latest.volume }
          const isLongPeriod = (resolution === '1W' || resolution === '1M')
          // 历史未就绪阶段：
          //  - 周/月：全部排队，等历史到达后/订阅者就位再冲洗
          //  - 其它周期：若未超时则排队；若已超时且已有订阅者则可直接分发
          if (!session!.historyReady) {
            if (!isLongPeriod && session!.timedOut && session!.subs && session!.subs.length > 0) {
              deliverToSubs(session!, bar)
              return
            }
            session!.queuedBars = session!.queuedBars || []
            session!.queuedBars.push(bar)
            return
          }
          // 历史已就绪但订阅者尚未注册：继续排队，待订阅者到位冲洗
          if (!session!.subs || session!.subs.length === 0) {
            session!.queuedBars = session!.queuedBars || []
            session!.queuedBars.push(bar)
            return
          }
          // 正常分发
          deliverToSubs(session!, bar)
        }
      }
      session.wsCb = wsCb
      marketSocket.on(stream, wsCb)
      session.wsSubscribed = true
    }

    const cacheKey = `${formatSymbol(symbolInfo.fullName)}_${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    // 优化：优先使用实时数据，提高响应速度
    if (firstDataRequest) {
      const isLongPeriod = resolution === '1W' || resolution === '1M'

      // 1) 首次加载周/月周期：不要用store实时数据直接返回，确保一定拉取历史数据
      if (!isLongPeriod) {
        if (klineList.value.length > 0 &&
          formatSymbol(klineTicker.value.currentPair) === formatSymbol(symbolInfo.fullName) &&
          String(resolutionReMap[klineTicker.value.currentPeriod]) === String(resolution)) {
          // 检查数据是否新鲜（5秒内）
          const lastCandle = klineList.value[klineList.value.length - 1]
          const isDataFresh = lastCandle && Date.now() - lastCandle.time < 5000
          if (isDataFresh) {
            // 优先使用新鲜的实时数据（仅限非周/月周期）
            preObj.value = klineList.value[0]
            onHistoryCallback(klineList.value, { noData: false })
            return
          }
        }
      }

      // 2) 缓存命中可直接返回（缓存来自历史接口）
      const cachedData = getCacheData(cacheKey)
      if (cachedData && cachedData.length > 0) {
        preObj.value = cachedData[0]
        onHistoryCallback(cachedData, { noData: false })
        return
      }

      // 3) 不再在周/月周期用store数据回填并写缓存，避免只显示推送数据
      if (!isLongPeriod &&
        klineList.value.length &&
        formatSymbol(klineTicker.value.currentPair) === formatSymbol(symbolInfo.fullName) &&
        String(resolutionReMap[klineTicker.value.currentPeriod]) === String(resolution)) {
        preObj.value = klineList.value[0]
        // 将store数据也缓存起来（仅限非周/月周期）
        setCacheData(cacheKey, klineList.value)
        onHistoryCallback(klineList.value, { noData: klineList.value.length === 0 })
        return
      }
    }

    // 如果缓存未命中，则请求API数据
    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback, cacheKey);
  }
  //获取历史数据
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any, cacheKey?: string) {
    try {
      // const endTime  = to * 1000
      const now = new Date().getTime()
      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: firstDataRequest ? now : preObj.value.time,
        limit: countBack > 300 ? 300 : countBack
      })
      if (data) {
        const formattedData = data.e.map(item => ({
          time: Number(item[0]),
          open: Number(item[1]) < 0 ? -Number(item[1]) : Number(item[1]),
          high: Number(item[2]) < 0 ? -Number(item[2]) : Number(item[2]),
          low: Number(item[3]) < 0 ? -Number(item[3]) : Number(item[3]),
          close: Number(item[4]) < 0 ? -Number(item[4]) : Number(item[4]),
          volume: Number(item[5]) < 0 ? -Number(item[5]) : Number(item[5])
        }))
        preObj.value = formattedData[0]

        // 专业版增强：历史返回时直接拼接当前实时数据（仅限首次且1W/1M）
        let resultData = formattedData
        if (firstDataRequest && formattedData.length > 0) {
          try {
            const expectedPeriod = resolutionMap[resolution]
            let currentPeriod: any = klineTicker.value?.currentPeriod
            if (currentPeriod === '1D') currentPeriod = '1d'
            if (currentPeriod === '1W') currentPeriod = '1w'
            const samePair = formatSymbol(klineTicker.value?.currentPair) === formatSymbol(symbol)
            const samePeriod = expectedPeriod === currentPeriod
            if (samePair && samePeriod) {
              const history = formattedData.slice()
              const lastIdx = history.length - 1
              const lastBar = { ...history[lastIdx] }
              const lastTicker = (ticker.value?.[klineTicker.value.currentPair] || {}).last
              if (lastTicker !== undefined) {
                const lastNum = Number(lastTicker)
                lastBar.close = lastNum
                lastBar.high = Math.max(Number(lastBar.high), lastNum)
                lastBar.low = Math.min(Number(lastBar.low), lastNum)
              }
              const latestT = Number(klineTicker.value?.time || klineTicker.value?.timestamp || 0)
              if (latestT && latestT > Number(lastBar.time)) {
                // 先替换历史最后一根，再追加一根当前周期中的“进行中”蜡烛
                history[lastIdx] = lastBar
                const base: any = klineTicker.value || {}
                const c = Number((lastTicker !== undefined ? lastTicker : (base.close ?? lastBar.close)))
                const o = Number(base.open ?? c)
                const h = Math.max(Number(base.high ?? c), c)
                const l = Math.min(Number(base.low ?? c), c)
                const v = Number(base.volume ?? 0)
                history.push({ time: latestT, open: o, high: Math.max(h, c), low: Math.min(l, c), close: c, volume: v })
              } else {
                // 仅用最新价修正最后一根
                history[lastIdx] = lastBar
              }
              resultData = history
            }
          } catch (e) {}
        }

        // 缓存数据（仅在首次请求时）
        if (firstDataRequest && cacheKey && resultData.length > 0) {
          setCacheData(cacheKey, resultData)
        }

        onHistoryCallback(resultData, { noData: resultData.length === 0 })
        // 首屏就绪：切换专业版会话状态，冲洗队列
        try {
          const screenKey = `${symbol}_#_${resolution}`
          firstScreenReady.set(screenKey, true)
          const sessKey = getSessionKey(symbol, resolution)
          const session = sessions.get(sessKey)
          if (session) {
            session.historyReady = true
            if (session.timeoutId) { clearTimeout(session.timeoutId); session.timeoutId = undefined }
            // 历史就绪：如已存在订阅者则立刻冲洗全部排队帧；否则保留至订阅者注册时冲洗
            if (session.queuedBars && session.queuedBars.length) {
              if (session.subs && session.subs.length > 0) {
                session.queuedBars.forEach(b => deliverToSubs(session, b))
                session.queuedBars = []
              }
            }
          }
        } catch (e) {}

      } else {
        // 添加重试延迟，避免频繁请求
        setTimeout(() => {
          fetchHistoricalData(symbol, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback, cacheKey);
        }, 1000)
      }
    } catch (error) {
      // 添加重试延迟，避免频繁请求
      setTimeout(() => {
        fetchHistoricalData(symbol, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback, cacheKey);
      }, 1000)
    }
  }
  // 优化：使用精准监听和同步更新，提升响应速度
  watch([ticker, klineTicker], ([val1, val2]) => {
    // 检查是否有有效数据
    if (!val2 || !val2.currentPair) return

    // 移除过严的时间检查，所有数据都及时推送
    // const hasNewUpdate = val2._lastUpdate && Date.now() - val2._lastUpdate < 1000

    // 遍历所有订阅，找到匹配当前交易对和周期的订阅
    Object.keys(subMap).forEach(key => {
      const subscriptions = subMap[key]

      // 处理数组格式的订阅（支持多个组件订阅同一交易对）
      if (Array.isArray(subscriptions)) {
        subscriptions.forEach(subscription => {
          // 修复：改进匹配逻辑，正确处理1W和1M
          const expectedPeriod = resolutionMap[subscription.resolution] || subscription.resolution
          // 对于1W，确保大小写一致
          const normalizedExpected = expectedPeriod === '1W' ? '1w' : expectedPeriod
          const normalizedCurrent = val2.currentPeriod === '1W' ? '1w' : val2.currentPeriod

          if (subscription &&
            formatSymbol(subscription.symbol) === val2.currentPair &&
            normalizedExpected === normalizedCurrent) {
            const last = (val1[val2.currentPair] || {}).last
            if (last && val2.open !== undefined) {
              // 确保传递完整的OHLCV数据给SMMA等指标
              const resultVal = {
                time: val2.time || val2.timestamp || Date.now(),
                open: Number(val2.open) || Number(last),
                high: val2.high ? Number(val2.high) : Number(last),
                low: val2.low ? Number(val2.low) : Number(last),
                close: Number(last), // 使用最新的ticker价格
                volume: val2.volume ? Number(val2.volume) : 0,
                // 保留原始数据用于调试
                currentPair: val2.currentPair,
                currentPeriod: val2.currentPeriod,
                timestamp: val2.timestamp,
                _lastUpdate: val2._lastUpdate // 传递时间戳
              }
              // 首屏历史未准备好则丢弃实时推送，避免竞态
              const ready = firstScreenReady.get(`${subscription.symbol}_#_${subscription.resolution}`)
              if (ready) {
                subscription.listen(resultVal)
              }
            }
          }
        })
      }
      // 兼容旧的单个订阅格式
      else if (subscriptions &&
        subscriptions.symbol) {
        const expectedPeriod = resolutionMap[subscriptions.resolution] || subscriptions.resolution
        const normalizedExpected = expectedPeriod === '1W' ? '1w' : expectedPeriod
        const normalizedCurrent = val2.currentPeriod === '1W' ? '1w' : val2.currentPeriod

        if (formatSymbol(subscriptions.symbol) === val2.currentPair &&
            normalizedExpected === normalizedCurrent) {
          const last = (val1[val2.currentPair] || {}).last
          if (last && val2.open !== undefined) {
            // 确保传递完整的OHLCV数据给SMMA等指标
            const resultVal = {
              time: val2.time || val2.timestamp || Date.now(),
              open: Number(val2.open) || Number(last),
              high: val2.high ? Number(val2.high) : Number(last),
              low: val2.low ? Number(val2.low) : Number(last),
              close: Number(last), // 使用最新的ticker价格
              volume: val2.volume ? Number(val2.volume) : 0,
              // 保留原始数据用于调试
              currentPair: val2.currentPair,
              currentPeriod: val2.currentPeriod,
              timestamp: val2.timestamp,
              _lastUpdate: val2._lastUpdate
            }
            // 首屏历史未准备好则丢弃实时推送，避免竞态
            const ready = firstScreenReady.get(`${subscriptions.symbol}_#_${subscriptions.resolution}`)
            if (ready) {
              subscriptions.listen(resultVal)
            }
          }
        }
      }
    })
  }, {
    deep: false, // 优化：使用浅监听提升性能
    immediate: true, // 立即执行
    flush: 'sync' // 同步更新而非异步
  })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    // 参数验证
    if (!symbolInfo || !symbolInfo.fullName || !resolution) return

    // 绑定到专业版会话订阅
    const sessKey = getSessionKey(symbolInfo.fullName, resolution)
    let session = sessions.get(sessKey)
    if (!session) {
      session = {
        symbol: symbolInfo.fullName,
        resolution,
        stream: getStream(symbolInfo.fullName, resolution),
        subs: [],
        historyReady: false,
        timedOut: false,
        timeoutId: undefined,
        queuedBars: [],
        wsSubscribed: false,
      }
      sessions.set(sessKey, session)
    }
    const sub: SessionSub = { uid: subscriberUID, cb: onRealtimeCallback }
    session.subs.push(sub)

    // 订阅者就绪：按策略冲洗队列（长周期需历史就绪；其它周期若已超时也可冲洗）
    if (session.queuedBars && session.queuedBars.length) {
      const isLongPeriod = (resolution === '1W' || resolution === '1M')
      if (session.historyReady || (!isLongPeriod && session.timedOut)) {
        session.queuedBars.forEach(b => deliverToSubs(session, b))
        session.queuedBars = []
      }
    }

    // TV的原始订阅缓存仍登记（用于兼容现有取消订阅流程）
    subMap[subscriberUID] = { symbol: symbolInfo.fullName, resolution, subscriberUID, listen: onRealtimeCallback }
  }

  // 状态记录方法（当前仅记录状态，不会阻止任何更新）
  // 保留此方法以便将来需要时可以重新启用控制
  function setResolutionChanging(changing: boolean, symbol: string | null = null, resolution: string | null = null) {
    resolutionChangingState.value = {
      isChanging: changing,
      symbol: symbol,
      resolution: resolution,
      timestamp: changing ? Date.now() : null
    }
  }

  return {
    historyCallback: () => { },
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      // 初始化时，优先采用用户在盘口选择的小数位（store.priceScale），无效时回退到交易对默认精度（pairDecimals）
      let pricescaleValue = (typeof priceScale?.value === 'number' && priceScale.value >= 0) ? priceScale.value : (typeof pairDecimals === 'number' ? pairDecimals : 8)
      pricescaleValue = Math.min(Math.max(pricescaleValue, 0), 16)
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      // 兼容旧的清理：移除 subMap 记录
      const subscription = subMap[subscriberUID]
      if (subscription && subscription.symbol && subscription.resolution) {
        const sessKey = getSessionKey(subscription.symbol, subscription.resolution)
        const session = sessions.get(sessKey)
        if (session) {
          // 移除订阅者
          session.subs = session.subs.filter(s => s.uid !== subscriberUID)
          // 如无订阅者，退订WS并清理会话
          if (session.subs.length === 0) {
            if (session.wsCb) marketSocket.off(session.stream, session.wsCb)
            marketSocket.send({ method: 'UNSUBSCRIBE', params: [session.stream] })
            if (session.timeoutId) clearTimeout(session.timeoutId)
            sessions.delete(sessKey)
          }
        }
      }
      delete subMap[subscriberUID]
    },
    // 清理特定交易对的所有订阅
    clearSymbolSubscriptions(symbol: string) {
      const keysToDelete: string[] = []
      Object.keys(subMap).forEach(key => {
        const subscription = subMap[key]

        // 处理数组格式的订阅
        if (Array.isArray(subscription)) {
          // 过滤掉匹配symbol的订阅
          subMap[key] = subscription.filter(sub => sub.symbol !== symbol)
          if (subMap[key].length === 0) {
            keysToDelete.push(key)
          }
        }
        // 处理单个订阅
        else if (subscription && subscription.symbol === symbol) {
          keysToDelete.push(key)
        }
      })

      // 删除标记的keys
      keysToDelete.forEach(key => delete subMap[key])
    },
    // 清理函数，用于组件销毁时调用
    cleanup() {
      // 清理所有订阅
      Object.keys(subMap).forEach(key => {
        delete subMap[key]
      })
      // 清理缓存
      clearCache()
    },
    // 暴露subMap用于调试
    getSubMap() {
      return subMap
    },
    // 暴露状态控制接口
    setResolutionChanging,
    isResolutionChanging: computed(() => resolutionChangingState.value.isChanging),
    resolutionChangingState
  }
}