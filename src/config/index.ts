const port = 3009
const imgDmain = 'https://res.ktx.com'
const imgKycDmain = 'https://res.ktx.com/'
// websocket 是否启用压缩 默认true
const SOCKET_ISZIP = true
// const BASE_EXCHANGE_URL = 'http://ma-tapi.tonetou.com'
// 接口地址
const BASE_EXCHANGE_URL = 'http://tapi.bost.co'
const PROD_EXCHANGE_URL = 'https://api.ktx.com'
const PROD_ORDER_EXCHANGE_URL = 'https://api-user.tonetou.com'

const BASE_SOCKET_URL = 'wss://stream-market.tonetou.com'
// pm启动key
const PROJECT_KEY = 'madex-pro'

// This option is given directly to the vue-router base
const BASE_URL_DIRECTORY = '/'

const getSiteName = () => {
  let name = BASE_URL_DIRECTORY
  if (name.startsWith('/')) {
    name = name.substring(1)
  }
  if (name.endsWith('/')) {
    name = name.substring(0, name.length - 1)
  }
  return name.replace('/', '-')
}

const SITE_NAME = getSiteName()


const LOCALES = [
  {
    code: 'en',
    label: 'English',
    zendesk: 'en-us'
  },
  {
    code: 'ja',
    label: '日本語',
    zendesk: 'ja'
  },
  {
    code: 'ko',
    label: '한국어',
    zendesk: 'en-us'
  },
  {
    code: 'zh-Hant',
    label: '繁体中文',
    zendesk: 'zh-Hant'
  },
  {
    code: 'zh',
    label: '简体中文',
    zendesk: 'zh-cn'
  }
]
const RATES = [
  {
    rate: 'USD',
    symbol: '$'
  },
  {
    rate: 'JPY',
    symbol: '¥'
  },
  {
    rate: 'KRW',
    symbol: '₩'
  },
  {
    rate: 'CNY',
    symbol: '¥'
  }
]
const ORDER_STATUS_MAP = { // 订单所有状态
  'accepted': '未成交',
  'cancelled': '已撤销',
  'triggered': '已触发',
  'untriggered': '未触发',
  'received': '已接收',
  'cancelling': '取消中',
  'partially-cancelled': '部分成交',
  'partially-filled': '部分成交',
  'rejected': '已废弃',
  'filled': '完全成交'
}
const PROFIT_LOSS_MAP = { // 止盈止损类型
  'stop-limit': '止损',
  'take-profit-limit': '止盈',
  'stop': '止损',
  'take-profit': '止盈'
}
const TRADE_BILLS_MAP = { // 交易账户类型
  'transfer': '资金划转',
  'transfer virtual': '新增资金',
  'trade': '交易',
  'fee': '手续费',
  'rebate': '手续费返还',
  'funding': '资金费率',
  'pnl': '平仓盈亏',
  'adl': 'ADL',
  '--': '--'
}
const ORDER_TYPE_ALL_MAP = { // 现货下单策略
  1: {
    sign: 'market',
    label: '市价委托'
  },
  2: {
    sign: 'limit',
    label: '普通委托'
  }
}
const TIME_ZONE_MAP = {
  'Asia/Singapore': { value: 'Asia/Singapore', label: '(UTC+8) 亚洲/新加坡', num: 8 },
  'Pacific/Honolulu': { value: 'Pacific/Honolulu', label: '(UTC-10) 太平洋/檀香山', num: -10 },
  'America/Vancouver': { value: 'America/Vancouver', label: '(UTC-7) 北美洲/温哥华', num: -7 },
  'America/Los_Angeles': { value: 'America/Los_Angeles', label: '(UTC-7) 北美洲/洛杉矶', num: -7 },
  'America/Phoenix': { value: 'America/Phoenix', label: '(UTC-7) 北美洲/菲尼克斯', num: -7 },
  'America/Chicago': { value: 'America/Chicago', label: '(UTC-5) 北美洲/芝加哥', num: -5 },
  'America/El_Salvador': { value: 'America/El_Salvador', label: '(UTC-6) 北美洲/萨尔瓦多', num: -6 },
  'America/Toronto': { value: 'America/Toronto', label: '(UTC-4) 北美洲/多伦多', num: -4 },
  'America/New_York': { value: 'America/New_York', label: '(UTC-4) 北美洲/纽约', num: -4 },
  'America/Bogota': { value: 'America/Bogota', label: '(UTC-5) 南美洲/波哥大', num: -5 },
  'America/Argentina/Buenos_Aires': { value: 'America/Argentina/Buenos_Aires', label: '(UTC-3) 南美洲/布宜诺斯艾利斯', num: -3 },
  'America/Sao_Paulo': { value: 'America/Sao_Paulo', label: '(UTC-2) 南美洲/圣保罗', num: -2 },
  'Europe/London': { value: 'Europe/London', label: '(UTC+1) 欧洲/伦敦', num: 1 },
  'Europe/Madrid': { value: 'Europe/Madrid', label: '(UTC+2) 欧洲/马德里', num: 2 },
  'Europe/Paris': { value: 'Europe/Paris', label: '(UTC+2) 欧洲/巴黎', num: 2 },
 'Europe/Berlin': { value: 'Europe/Berlin', label: '(UTC+2) 欧洲/柏林', num: 2 },
  'Europe/Warsaw': { value: 'Europe/Warsaw', label: '(UTC+2) 欧洲/华沙', num: 2 },
  'Europe/Athens': { value: 'Europe/Athens', label: '(UTC+3) 欧洲/雅典', num: 3 },
  'Europe/Moscow': { value: 'Europe/Moscow', label: '(UTC+3) 欧洲/莫斯科', num: -10 },
  'Asia/Tehran': { value: 'Asia/Tehran', label: '(UTC+3:30) 亚洲/德黑兰', num: 3.5 },
  'Asia/Dubai': { value: 'Asia/Dubai', label: '(UTC+4) 亚洲/杜拜', num: 4 },
  'Asia/Ashgabat': { value: 'Asia/Ashgabat', label: '(UTC+5) 亚洲/阿什哈巴德', num: 5 },
  'Asia/Kolkata': { value: 'Asia/Kolkata', label: '(UTC+5:30) 亚洲/加尔各答', num: 5.5 },
  'Asia/Almaty': { value: 'Asia/Almaty', label: '(UTC+6) 亚洲/阿拉木图', num: 6 },
  'Asia/Bangkok': { value: 'Asia/Bangkok', label: '(UTC+7) 亚洲/曼谷', num: 7 },
  'Asia/Taipei': { value: 'Asia/Taipei', label: '(UTC+8) 亚洲/台北', num: 8 },
  'Asia/Shanghai': { value: 'Asia/Shanghai', label: '(UTC+8) 亚洲/上海', num: 8 },
  'Asia/Hong_Kong': { value: 'Asia/Hong_Kong', label: '(UTC+8) 亚洲/香港', num: 8 },
  'Asia/Seoul': { value: 'Asia/Seoul', label: '(UTC+9) 亚洲/首尔', num: 9 },
  'Asia/Tokyo': { value: 'Asia/Tokyo', label: '(UTC+9) 亚洲/东京', num: 9 },
  'Australia/Brisbane': { value: 'Australia/Brisbane', label: '(UTC+10) 大洋洲/布里斯班', num: 10 },
  'Australia/Adelaide': { value: 'Australia/Adelaide', label: '(UTC+10:30) 大洋洲/阿德莱德', num: 10.5 },
  'Australia/Sydney': { value: 'Australia/Sydney', label: '(UTC+11) 大洋洲/悉尼', num: 11 },
  'Pacific/Auckland': { value: 'Pacific/Auckland', label: '(UTC+13) 大洋洲/纽西兰', num: 13 },
  'Pacific/Fakaofo': { value: 'Pacific/Fakaofo', label: '(UTC+13) 大洋洲/托克劳', num: 13 },
  'Pacific/Chatham': { value: 'Pacific/Chatham', label: '(UTC+13:45) 大洋洲/查塔姆群岛', num: 13.65 },
  'Europe/Greenwich': { value: 'Europe/Greenwich', label: '(UTC-0) 欧洲/格林威治', num: 0 }
}
const ORDER_TYPE_SIGN_MAP: any = {}
Object.entries(ORDER_TYPE_ALL_MAP).forEach(([type, value]) => {
  ORDER_TYPE_SIGN_MAP[value.sign] = type
})
export {
  imgDmain,
  imgKycDmain,
  LOCALES,
  RATES,
  port,
  SOCKET_ISZIP,
  ORDER_STATUS_MAP,
  PROFIT_LOSS_MAP,
  TIME_ZONE_MAP,
  BASE_EXCHANGE_URL,
  PROD_EXCHANGE_URL,
  PROD_ORDER_EXCHANGE_URL,
  BASE_SOCKET_URL,
  BASE_URL_DIRECTORY,
  SITE_NAME,
  PROJECT_KEY,
  ORDER_TYPE_ALL_MAP,
  TRADE_BILLS_MAP,
  ORDER_TYPE_SIGN_MAP
}
