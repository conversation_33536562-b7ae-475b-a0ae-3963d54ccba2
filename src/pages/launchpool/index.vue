<template>
  <Header modeType="launchpool" />
  <div class="launch-pool-container">
    <div class="launch-pool-title-h5-cont flex-box">
      <span class="active">Launchpool</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4 rotate" />
    </div>
    <div v-loading="JSON.stringify(lpInfo) === '{}' && !isApp()" class="launch-pool-banner">
      <div class="launch-pool-center flex-box flex-column align-start space-between">
        <div class="pool-cont flex-box space-between">
          <div class="left-cont">
            <h1>KTX Launchpool</h1>
            <h2>{{ $t('锁定NFT获得空投') }}</h2>
            <div class="search-cont flex-box">
              <el-input v-model="search" class="search-input" clearable :placeholder="$t('输入NFT名或空投币名检索锁仓项目')" @keyup.enter="getLpList">
                <template #prepend>
                  <MonoSearch class="fit-tc-secondary" :size="20" />
                </template>
              </el-input>
              <el-button v-if="lpInfo.is_sub" :loading="isBtnLoading" type="primary" class="search-btn-box" :disabled="lpInfo.is_sub * 1 === 1" @click="subFun()">{{ lpInfo.is_sub * 1 === 2 ? $t('订阅通知') : $t('已订阅') }}</el-button>
            </div>
          </div>
          <div class="right-cont">
            <img src="~/assets/images/launch-pool/banner-img.png" />
          </div>
        </div>
        <div class="pool-list">
          <ul class="flex-box">
            <li class="flex-1">
              <span class="fit-tc-secondary">{{ $t('总锁仓价值') }}(USDT)</span>
              <p class="fit-tc-primary">{{ format(lpInfo.lock_value, 4, true) }}</p>
            </li>
            <li class="flex-1">
              <span class="fit-tc-secondary">{{ $t('总发放空投价值') }}(USDT)</span>
              <p class="fit-tc-primary">{{ format(lpInfo.airdrop_value, 4, true) }}</p>
            </li>
            <li class="flex-1">
              <span class="fit-tc-secondary">{{ $t('已发行项目') }}</span>
              <p class="fit-tc-primary">{{ format(lpInfo.airdrop_num, 4, true) }}</p>
            </li>
            <li class="flex-1">
              <span class="fit-tc-secondary">{{ $t('参与者') }}</span>
              <p class="fit-tc-primary">{{ format(lpInfo.users, 4, true) }}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div v-loading="isLoading" class="launch-pool-wrapper">
      <div v-if="lpList.length > 0" class="launch-pool-center-wrap">
        <div v-for="(item, index) in lpList" :key="index" class="launch-pool-info-box">
          <NuxtLink :to="`/${locale}/launchpool/detail/${item.id}`">
            <div class="launch-pool-info-pd">
              <div class="info-title flex-box space-between">
                <div class="title-left flex-box">
                  <img :src="getImgUrl(item.icon)" />
                  <span class="mg-l4">{{ getJsonObj(item.name, 'name') }}</span>
                </div>
                <div class="title-right flex-box" :class="{'fall': item.status * 1 === 0, 'rise': item.status * 1 === 1}">
                  <MonoUnstartIcon v-if="item.status * 1 === 0" :size="14" class="fit-fall mg-t4" />
                  <MonoWaitIcon v-if="item.status * 1 === 2" :size="14" class="fit-tc-secondary mg-t4" />
                  <MonoEndIcon v-if="item.status * 1 === 1" :size="14" class="fit-rise mg-t4" />
                  <div class="mg-l4">{{ statusObj[item.status] }}</div>
                </div>
              </div>
              <div class="info-desc">
                {{ getJsonObj(item.desc, 'desc') }}
              </div>
              <div class="symbol-cont-box flex-box flex-column">
                <div class="symbol-info flex-box space-center">
                  <div class="symbol-bd">
                    <img :src="getImgUrl(item.icon)" class="icon-box" />
                  </div>
                  <div v-for="(ite, ind) in JSON.parse(item.lock_icon)" :key="ind" class="symbol-bd mg-ln">
                    <img :src="getImgUrl(ite)" class="icon-box" />
                  </div>
                </div>
                <p>{{ $t('锁定{lockName}即获得{name}空投', { lockName: JSON.parse(item.lock_symbol).join('/'), name: item.symbol }) }}</p>
              </div>
              <ul>
                <li class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('空投总量') }}</span>
                  <span class="fit-tc-primary">{{ item.total }} {{ item.symbol }}</span>
                </li>
                <li class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('锁仓时长') }}</span>
                  <span class="fit-tc-primary">{{ format(item.lock_time / 24, 1, true) }} {{ $t('天') }}</span>
                </li>
                <li class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('参与人数') }}</span>
                  <span class="fit-tc-primary">{{ item.users }}</span>
                </li>
                <li class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('锁仓量') }}</span>
                  <span class="fit-tc-primary">{{ format(item.lock_value) }}</span>
                </li>
                <li v-if="item.status * 1 === 0 || item.status * 1 === 2" class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('开始日期') }}</span>
                  <span class="fit-tc-primary">{{ timeFormat(item.start_time, 'yy-MM-dd hh:mm') }}</span>
                </li>
                <li v-if="item.status * 1 === 2" class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('结束日期') }}</span>
                  <span class="fit-tc-primary">{{ timeFormat(item.end_time, 'yy-MM-dd hh:mm') }}</span>
                </li>
                <li v-if="item.status * 1 === 1" class="flex-box space-between font-size-14">
                  <span class="fit-tc-secondary">{{ $t('截止时间') }}</span>
                  <div v-if="leftTimeMap[item.id]" class="fit-tc-primary flex-box">
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap[item.id].d }}</span>
                    <span class="fit-tc-secondary">{{ $t('天') }}</span>
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap[item.id].h }}</span>
                    <span class="fit-tc-secondary">{{ $t('小时') }}</span>
                    <span class="font-size-16 mg-lr4">{{ leftTimeMap[item.id].m }}</span>
                    <span class="fit-tc-secondary">{{ $t('分') }}</span>
                  </div>
                </li>
              </ul>
            </div>
          </NuxtLink>
        </div>
      </div>
      <div v-else-if="lpList.length === 0" class="null-box">
        <BoxNoData :text="$t('暂无数据')" />
      </div>
    </div>
  </div>
  <Footer />
</template>
<script lang="ts" setup>
  import { ElInput, ElButton } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import MonoUnstartIcon from '~/components/common/icon-svg/MonoUnstartIcon.vue'
  import MonoWaitIcon from '~/components/common/icon-svg/MonoWaitIcon.vue'
  import MonoEndIcon from '~/components/common/icon-svg/MonoEndIcon.vue'
  import { format, timeFormat, getTimeLeft, isApp } from '~/utils'
  import { imgDmain } from '~/config'
  import { getLpSummaryAPI, getLpListAPI, getLpSubInfoAPI } from '~/api/tt.ts'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const { locale, t } = useI18n()
  const lpInfo = ref({})
  const lpList = ref([])
  const statusObj = ref({ // -1 未激活 0 未开始 1 进行中 2结束 
    '-1': t('未激活'),
    0: t('未开始'),
    1: t('进行中'),
    2: t('已结束')
  })
  const leftTimeMap = ref({})
  const interval = ref(null)
  const isLoading = ref(false)
  const isBtnLoading = ref(false)
  const search = ref('')
  watch(() => search.value, (val) => {
    if (val === '') {
      getLpList()
    }
  })
  const countDown = () => {
    interval.value && clearInterval(interval.value)
    interval.value = setInterval(() => {
      lpList.value.forEach((item) => {
        if (item.status * 1 === 1 && new Date(item.end_time).getTime() > new Date().getTime()) {
          const time = new Date().getTime()
          const endTime = new Date(item.end_time).getTime()
          const leftTime = endTime - time
          const leftTimeMapP = getTimeLeft(leftTime)
          if (leftTime <= 0) {
            getLpList()
          }
          leftTimeMap.value[item.id] = leftTimeMapP
        }
      })
    }, 1000)
  }
  const subFun = async() => {
    if ((lpInfo.value || {}).sub * 1 === 1) {
      return false
    }
    isBtnLoading.value = true
    const { data, error } = await getLpSubInfoAPI({
      sub: 1
    })
    if (data) {
      await getLpSummary()
      useCommon.showMsg('success', t('订阅成功！'))
      isBtnLoading.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
      isBtnLoading.value = false
    }
  }
  const getLpSummary = async() => {
    const { data } = await getLpSummaryAPI()
    if (data) {
      lpInfo.value = data
    }
  }
  const getLpList = async() => {
    isLoading.value = true
    const { data } = await getLpListAPI({
      search: search.value,
      page: 1,
      size: 100
    })
    if (data) {
      lpList.value = data.rows
      countDown()
    }
    isLoading.value = false
  }
  const getImgUrl = (url) => {
    return url.includes('http') ? url : `${imgDmain}${url}`
  }
  const getJsonObj = (obj, str) => {
    return (JSON.parse(obj)[locale.value] || JSON.parse(obj)['en'])
  }
  onMounted(() => {
    getLpSummary()
    getLpList()
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/launch-pool.scss');
</style>
<style lang="scss">
  .launch-pool-container {
    .launch-pool-banner {
      .launch-pool-center {
        .pool-cont{
          .left-cont{
            .search-cont{
              .el-input{
                &.search-input{
                  .el-input__wrapper{
                    border-radius:25px !important;
                    @include bg-color(bg-primary);
                  }
                }
              }
            }
          }
        }
      }
    }
    .null-box{
      border-radius:16px;
      height:500px;
      @include bg-color(bg-primary);
    }
  }
</style>