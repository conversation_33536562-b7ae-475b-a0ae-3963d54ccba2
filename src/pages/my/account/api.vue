<template>
  <div class="sub-account-api-container">
    <div class="account-nav flex-box space-between">
      <div>
        <h2 class="font-size-18 fit-tc-primary">{{ $t('API管理') }}</h2>
        <p style="margin-top:-10px;">{{ $t('* 你总共可以创建{len}个API，当前已创建{curLen}个。', { len: 5, curLen: apisList.length }) }}
          <a :href="`https://ktx-private.github.io/api-${locale === 'zh' ? 'zh' : 'en'}/`" target="_blank" class="fit-theme">{{ $t('了解更多>>') }}</a>
        </p>
      </div>
      <el-button type="primary" @click="createApiFun()">{{ $t('创建API') }}</el-button>
    </div>
    <div v-loading="isLoading" class="sub-account-api-wrapper noTop">
      <div v-for="(item, index) in apisList" :key="index" class="api-item-box">
        <div class="api-item-pd">
          <div class="api-title">{{ item.name }}</div>
          <dl class="flex-box align-start">
            <dt>
              <BoxQrcode :size="110" :value="item.api_key" />
            </dt>
            <dd>
              <div class="li-item">
                <div class="item-left">Access Key</div>
                <div class="item-right">{{ item.api_key }}</div>
              </div>
              <div class="li-item">
                <div class="item-left">Option</div>
                <div class="item-right">
                  <el-checkbox v-model="item.readInfoChecked" :disabled="editId !== item.id" @change="checkBoxReadInfoStatus($event, item)">{{ $t('读取信息') }}</el-checkbox>
                  <el-checkbox v-model="item.rechargeChecked" :disabled="editId !== item.id"@change="checkBoxRechargeStatus($event, item)">{{ $t('开放交易') }}</el-checkbox>
                </div>
              </div>
              <div class="li-item">
                <div class="item-left">{{ $t('授权信任的IP地址') }}</div>
                <div class="item-right">
                  <template v-if="!item.ipStr && item.id !== editId">
                    <div class="flex-box fit-warn font-size-14">
                       {{ $t('建议您添加可信的 IP 地址访问 API Key') }}
                      <span class="fit-theme mg-l16 font-size-14 cursor-pointer" @click="editId = item.id">{{
                        $t('添加')
                      }}</span>
                    </div>
                  </template>
                  <div v-if="item.ipStr && item.id !== editId" class="ip-item">{{ item.ipStr }}</div>
                  <template v-if="item.id === editId">
                    <el-input type="text" v-model="item.ipStr" :placeholder="$t('请输入IP地址')" />
                    <p>{{ $t('* 如果使用多个IP, 请使用逗号分隔, 最多添加20个') }}</p>
                  </template>
                </div>
              </div>
            </dd>
          </dl>
          <div class="btn-box flex-box">
            <template v-if="item.id !== editId">
              <el-button @click="deletePre(item)">{{ $t('删除') }}</el-button>
              <el-button type="primary" @click="editId = item.id">{{ $t('编辑') }}</el-button>
            </template>
            <template v-else>
              <el-button @click="cancelItemFun(item)">{{ $t('取消') }}</el-button>
              <el-button type="primary" @click="savePre(item)">{{ $t('保存') }}</el-button>
            </template>
          </div>
        </div>
      </div>
      <div v-if="apisList.length === 0 && !isLoading" style="height:500px;">
        <BoxNoData :text="$t('*API 密钥可以让您借助第三方网站或移动应用使用 KTX 的各类交易服务。')">
          <h3 class="fit-tc-primary font-size-16" style="padding-top:12px;margin-bottom:-10px;">{{ $t('您还没有创建任何API秘钥，可以点击右上角的创建按钮，创建您的API') }}</h3>
        </BoxNoData>
      </div>
    </div>
  </div>
  <CreateApiDialog v-if="isShowCreate" :isMain="true" :dialogVisible="isShowCreate" @close="isShowCreate = false" @confirm="createConfirm" />
  <DeleteApiDialog v-if="isShowDelete" :data="currentData" :dialogVisible="isShowDelete" @close="isShowDelete = false" @confirm="deleteConfirm"/>
  <AccountVerifyCodeDialog
    v-if="showEditVerify"
    :dialogVisible="showEditVerify"
    @request="saveItemFun"
    @handleClose="showEditVerify = false"
  />
  <CommonVerifyIsHasGoogleDialog v-if="isShowVerify" :isShow="isShowVerify" @close="isShowVerify = false" />
  <CreateApiInfoDialog v-if="isShowAdd" :createdData="createdData" :dialogVisible="isShowAdd" @close="isShowAdd = false" />
</template>
<script lang="ts" setup>
  import { ElCheckbox, ElInput, ElMessageBox } from 'element-plus'
  import BoxNoData from '~/components/common/BoxNoData.vue'
  import CreateApiDialog from '~/components/my/subAccount/CreateApiDialog.vue'
  import DeleteApiDialog from '~/components/my/subAccount/DeleteApiDialog.vue'
  import { useCommonData } from '~/composables/index'
  import { queryApiKey, addApiKey, deleteApiKey, updateApiKey } from '~/api/user'
  import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
  import CreateApiInfoDialog from '~/components/my/subAccount/CreateApiInfoDialog.vue'
  import { useUserStore } from '~/stores/useUserStore'
  const useUser = useUserStore()
  const { userInfo } = storeToRefs(useUser)
  const useCommon = useCommonData()
  const router = useRouter()
  const { locale, t } = useI18n()
  const isShowCreate = ref(false)
  const isShowDelete = ref(false)
  const showEditVerify = ref(false)
  const apisList = ref([])
  const cacheData = ref({})
  const isShowVerify = ref(false)
  watch(() => userInfo.value, (val) => {
    if (JSON.stringify(val) !== '{}' && val.is_paper_trader * 1 === 1) {
      router.push(`/${locale.value}/my/dashboard`)
    }
  }, {
    immediate: true
  })
  const createApiFun = () => {
    if (!userInfo.value.is_bind_totp) {
      isShowVerify.value = true
      return false
    }
    isShowCreate.value = true
  }
  const isLoading = ref(true)
  const getApiList = async() => {
    const { data, error } = await queryApiKey()
    if (data) {
      cacheData.value = JSON.parse(JSON.stringify(data))
      apisList.value = data.map((v) => {
        v.readInfo = v.api_flag & 1 ? 1 : 0
        v.readInfoChecked = !!(v.api_flag & 1)
        v.recharge = v.api_flag & 2 ? 1 : 0
        v.rechargeChecked = !!(v.api_flag & 2)
        v.withdrawal = v.api_flag & 4 ? 1 : 0
        v.ipStr = v.allow_ips.join(',') === '0.0.0.0' ? '' : v.allow_ips.join(',')
        return v
      })
      console.info(apisList.value, 'dhdhdhduhudhuueuue')
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
    isLoading.value = false
  }
  const isShowAdd = ref(false)
  const createdData = ref({})
  const createConfirm = async(params) => {
    const { data, error } = await addApiKey({
      name: params.name,
      allow_ips_array: params.ids,
      allow_ips: params.ids !== '' ? params.ids.split(',') : [],
      totp_code: params.code
    })
    if (data) {
      isShowCreate.value = false
      isShowAdd.value = true
      createdData.value = data
      useCommon.showMsg('success', t('新增成功！'))
      getApiList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const editId = ref('')
  const deleteId = ref('')
  const currentData = ref({})
  const checkBoxReadInfoStatus = (value, item) => {
    item.readInfo = value ? 1 : 0
    return value
  }
  const checkBoxRechargeStatus = (value, item) => {
    item.recharge = value ? 1 : 0
    return value
  }
  const deletePre = (item) => {
    ElMessageBox.confirm(t('您确定要删除这条API吗？'), t('删除API'), {
      distinguishCancelAndClose: true,
      confirmButtonText: t('确认'),
      cancelButtonText: t('取消'),
    }).then(() => {
      currentData.value = item
      deleteId.value = item.id
      isShowDelete.value = true
    })
  }
  const cancelItemFun = (item) => {
    editId.value = ''
    apisList.value = cacheData.value.map((v) => {
      v.readInfo = v.api_flag & 1 ? 1 : 0
      v.readInfoChecked = !!(v.api_flag & 1)
      v.recharge = v.api_flag & 2 ? 1 : 0
      v.rechargeChecked = !!(v.api_flag & 2)
      v.withdrawal = v.api_flag & 4 ? 1 : 0
      v.ipStr = v.allow_ips.join(',') === '0.0.0.0' ? '' : v.allow_ips.join(',')
      return v
    })
    getApiList()
  }
  const validatorIds = (value) => {
    // IPv4正则（允许0-255）
    const regIPv4 = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6正则（支持完整和缩写格式）
    const regIPv6 = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))$/;
    // 处理输入：替换中文逗号，分割并去除空格
    const ips = value.replace(/，/g, ',').split(',').map(ip => ip.trim());
    // 检查每个IP是否匹配IPv4或IPv6
    const allValid = ips.every(ip => regIPv4.test(ip) || regIPv6.test(ip));
    if (!allValid) {
      return true
    } else {
      return false
    }
  }
  const savePre = (item) => {
    editId.value = item.id
    if (validatorIds(item.ipStr)) {
      useCommon.showMsg('error', t('IP地址输入有误'))
      return false
    }
    currentData.value = item
    showEditVerify.value = true
  }
  const saveItemFun = async(code) => {
    const allowIps = []
    currentData.value.ipStr.replace('，', ',').split(',').forEach(v => {
      if (!allowIps.includes(v)) {
        allowIps.push(v)
      }
    })
    const params = {
      name: currentData.value.name,
      totp_code: code,
      id: currentData.value.id,
      query: currentData.value.readInfo,
      trade: currentData.value.recharge,
      withdraw: currentData.value.withdrawal,
      allow_ips_g: currentData.value.ipStr,
      allow_ips: allowIps.filter(v => v) || []
    }
    console.info(params, 'paramsparamsparams')
    const { data, error } = await updateApiKey(params)
    if (data) {
      editId.value = ''
      useCommon.showMsg('success', t('更新成功！'))
      getApiList()
      showEditVerify.value = false
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  const deleteConfirm = async(code) => {
    const {data, error} = await deleteApiKey({
      totp_code: code,
      id: deleteId.value
    })
    if (data) {
      useCommon.showMsg('success', t('删除API成功'))
      isShowDelete.value = false
      getApiList()
    } else {
      useCommon.showMsg('error', useCommon.err(error.code, error))
    }
  }
  onBeforeMount(async() => {
    isLoading.value = true
    await getApiList()
  })
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/my/api.scss');
</style>