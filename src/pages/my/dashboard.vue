<template>
  <div class="wallet-container">
    <BoxLoading v-if="isLoading" />
    <div class="wallet-wrapper">
      <div class="user-wrap-cont flex-box">
        <div class="user-info-left flex-box">
          <div v-if="JSON.stringify(user) !== '{}' && user.name !== ''" class="account-head">{{ user.name.slice(0, 1).toUpperCase() }}</div>
          <div class="account-email">
            {{ isVisibleUserInfo ? user.name_visible : user.name }}
            <template v-if="user.is_bind_totp && user.son_type * 1 !== 2">
              <MonoEyeOpen v-if="isVisibleUserInfo" @click="updateUserState(isVisibleUserInfo)" />
              <MonoEyeClose v-else  @click="updateUserState(isVisibleUserInfo)" />
            </template>
          </div>
        </div>
        <div class="user-info-right flex-box">
          <div class="item-cont">
            <div class="item-label flex-box">
              UID
              <MonoCopy @click="useCommon.copy(user.user_id, $t('复制成功！'))" />
            </div>
            <div class="item-value fit-tc-primary">{{ user.user_id }}</div>
          </div>
          <div v-if="user.user_type * 1 !== 100" class="item-cont cursor-pointer" @click="router.push(`/${locale}/my/kyc/result`)">
            <div class="item-label flex-box">
              {{ $t('身份认证') }}
              <MonoEdit />
            </div>
            <div class="item-value">
              <span v-if="user.is_real_name * 1 === 0" class="fit-warn">{{ $t('未认证') }}</span>
              <span v-if="user.is_real_name * 1 === 1" class="fit-tc-secondary">{{ $t('审核中') }}</span>
              <span v-if="user.is_real_name * 1 === 2" class="fit-warn">{{ $t('认证失败') }}</span>
              <span v-if="user.is_real_name * 1 === 3" class="fit-rise">{{ $t('已认证') }}</span>
            </div>
          </div>
          <div v-if="JSON.stringify(user) !== '{}' && user.name !== ''" class="item-cont" @click="isShowChangeTimeZone = true">
            <div class="item-label flex-box">
              {{ $t('账户时区') }}
               <MonoEdit />
            </div>
            <div class="item-value fit-tc-primary">{{ timeZoneObj[user.utc_area].label }}</div>
          </div>
        </div>
        <NuxtLink v-if="JSON.stringify(user) !== '{}' && user.other_device_login * 1 === 1" :to="`/${locale}/my/account/device`" class="user-device-info flex-box fit-tc-primary font-size-14">
          {{ $t('您的账号已在其它设备登录') }}
          <MonoRightArrowShort size="18" class="fit-tc-secondary mg-l8" />
        </NuxtLink>
      </div>
      <template v-if="user.user_type * 1 !== 100">
        <WarnTips :isShow="!user.is_bind_totp && isShowGoogle" type="warn" :tipsText="$t('为了您的资金安全，请尽快绑定“谷歌验证码”')" @close="isShowGoogle = false" />
        <WarnTips :isShow="user.is_real_name * 1 !== 3 && isShowRealName" type="warn" :tipsText="$t('您尚未完成“实名认证”，完成实名认证，可提升提币额度。')" @close="isShowRealName = false" />
        <template v-if="notifyList.length > 0">
          <WarnTips v-for="(item, index) in notifyList" :key="index" :isShow="isShowRecharge[item.id]" type="info" :tipsText="$t('钱包账户有一笔 {amount} {coin_symbol}通过区块链充值到账', { amount: item.amount, coin_symbol: item.coin_symbol })" @close="closeRecharge(item)" />
        </template>
      </template>
      <div v-if="JSON.stringify(allAsset) !== '{}'" class="wallet-assets-box">
        <div class="wallet-assets wallet-asset-all">
          <div class="left">
            <div class="title-cont flex-box">
              <div class="icon"></div>
              {{ $t('我的资产估值') }}
              <MonoEyeOpen v-if="!isHideAssets" @click="setHideAssets" />
              <MonoEyeClose v-if="isHideAssets" @click="setHideAssets" />
            </div>
            <div class="info-text">
              <span style="font-weight:600;">{{ useCommon.hideAssets(formatNumberWithCommas(AllUSDTAssets)) }}</span>
              <em>USDT</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(useCommon.convert(AllUSDTAssets, 'USDT', true)) }}</div>
          </div>
        </div>
        <div class="wallet-assets wallet-asset-index">
          <div class="left flex-1" @click="router.push(`/${locale}/my/assets/wallet`)">
            <div class="title-cont">
              {{ $t('钱包账户') }}
            </div>
            <div class="info-text">
              <span style="font-weight:600;">{{ useCommon.hideAssets(formatNumberWithCommas(allAsset.main.equsdt)) }}</span>
              <em>USDT</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(useCommon.convert(allAsset.main.equsdt, 'USDT', true)) }}</div>
            <div class="info-min-text">{{ AllUSDTAssets * 1 === 0 ? 0 : format(((allAsset.main.equsdt / AllUSDTAssets) * 100), 2) }}%</div>
          </div>
          <div class="right">
            <el-button type="primary" @click="router.push(`/${locale}/my/assets/deposit`)">{{ $t('充值') }}</el-button>
            <template v-if="user.user_type * 1 !== 100">
              <el-button @click="goWithdrawalFun()">{{ $t('提币') }}</el-button>
            </template>
            <el-button @click="transferFun('main')">{{ $t('划转') }}</el-button>
          </div>
        </div>
        <div class="wallet-assets wallet-asset-index">
          <div class="left flex-1" @click="router.push(`/${locale}/my/assets/trade`)">
            <div class="title-cont">
              {{ $t('交易账户') }}
            </div>
            <div class="info-text">
              <span style="font-weight:600;">{{ useCommon.hideAssets(formatNumberWithCommas(tradeUSDTAssets)) }}</span>
              <em>USDT</em>
            </div>
            <div class="info-small-text"> ≈{{ useCommon.hideAssets(useCommon.convert(tradeUSDTAssets, 'USDT', true)) }}</div>
            <div class="info-min-text">{{ AllUSDTAssets * 1 === 0 ? 0 : format(((tradeUSDTAssets / AllUSDTAssets) * 100), 2) }}%</div>
          </div>
          <div class="right">
            <el-button @click="transferFun('trade')">{{ $t('划转') }}</el-button>
          </div>
        </div>
      </div>
      <AccountRateInfo />
    </div>
  </div>
  <CheckedUserTimezone v-if="isShowChangeTimeZone" :defaultTimeZone="user.utc_area" :useCommon="useCommon" :isShow="isShowChangeTimeZone" @close="isShowChangeTimeZone = false" @request="useUser.getUserInfoAction()" />
  <AccountVerifyCodeDialog
    v-if="showVertify"
    :dialogVisible="showVertify"
    @request="verifyUserState"
    @handleClose="showVertify = false"
  />
  <CouponTipsDialog v-if="isShowCouponTips" :visible="isShowCouponTips" @close="isShowCouponTips = false" @request="goWithdrawalRouter()" />
  <TransferDialog v-if="isShowTransfer" :defaultAccount="defaultAccount" :visibleDialog="isShowTransfer" @close="isShowTransfer = false" />
</template>
<script lang="ts" setup>
import BigNumber from 'bignumber.js'
import { ElButton } from 'element-plus'
import { format, formatNumberWithCommas } from '~/utils'
import { TIME_ZONE_MAP } from '~/config'
import MonoEyeOpen from '~/components/common/icon-svg/MonoEyeOpen.vue'
import MonoEyeClose from '~/components/common/icon-svg/MonoEyeClose.vue'
import MonoCopy from '~/components/common/icon-svg/MonoCopy.vue'
import MonoEdit from '~/components/common/icon-svg/MonoEdit.vue'
import WarnTips from '~/components/common/WarnTips.vue'
import TransferDialog from '~/components/common/TransferDialog.vue'
import AccountRateInfo from '~/components/my/account/RateInfo.vue'
import AccountVerifyCodeDialog from '~/components/common/AccountVerifyCodeDialog.vue'
import CouponTipsDialog from '~/components/common/CouponTipsDialog.vue'
import { commonStore } from '~/stores/commonStore'
import { setUserVisible } from '~/api/user.ts'
import { lastDepositNotify } from '~/api/tf.ts'
const store = commonStore()
const { setHideAssets, getAssetsByCoin } = store
const { mainAssetObj, tradeAssetObj, allAsset, posMapObj, isHideAssets } = storeToRefs(store)
const router = useRouter()
const { locale } = useI18n()
const props = defineProps({
  user: {
    type: Object,
    default () {
      return {}
    }
  },
  useCommon: {
    type: Object,
    default () {
      return {}
    }
  },
  isVisibleUserInfo: {
    type: Boolean,
    default: false
  },
  useUser: {
    type: Object,
    default () {
      return {}
    }
  }
})
const isShowTransfer = ref(false)
const defaultAccount = ref('')
const isOpen = ref<number>(0)
const showVertify = ref(false)
const isShowGoogle = ref(false)
const isShowRealName = ref(false)
const isShowRecharge = ref({})
const isLoading = ref(true)
const isShowChangeTimeZone = ref(false)
const transferFun = (type) => {
  isShowTransfer.value = true
  defaultAccount.value = type
}
const unprofitUSDTAssets = computed(() => {
  return posMapObj.value && posMapObj.value['USDT'] && posMapObj.value['USDT'].unprofit
})
const AllUSDTAssets = computed(() => {
  return new BigNumber(allAsset.value && allAsset.value.all.equsdt).plus(unprofitUSDTAssets.value)
})
const tradeUSDTAssets = computed(() => {
  return new BigNumber(allAsset.value && allAsset.value.trade.equsdt).plus(unprofitUSDTAssets.value)
})
const timeZoneObj = computed(() => {
  return TIME_ZONE_MAP
})
const verifyUserState = async(code) => {
  const { data, error } = await setUserVisible({
    is_open: isOpen.value,
    totp_code: code ? code : undefined
  })
  if (data) {
    showVertify.value = false
    props.useUser.getUserInfoAction()
  } else {
    props.useCommon.showMsg('error', props.useCommon.err(error.code, error))
  }
}
watch(() => props.user.user_id, (val) => {
  getAssetsByCoin()
  props.useUser.getUserInfoAction()
})
const updateUserState = (val) => {
  isOpen.value = val ? 0 : 1
  if (!val) {
    showVertify.value = true
  } else {
    verifyUserState()
  }
}
const notifyList = ref([])

const getNotify = async() => {
  isLoading.value = true
  const { data } = await lastDepositNotify()
  console.info(data, 'hdheudhudue')
  if (data) {
    notifyList.value = data
    data.forEach((item) => {
      isShowRecharge.value[item.id] = true
    })
    isLoading.value = false
  } else {
    isLoading.value = false
  }
  isShowGoogle.value = true
  isShowRealName.value = true
}
const closeRecharge = (item) => {
  isShowRecharge.value[item.id] = false
}
const isHasAccountCoupon = computed(() => {
  const targets = ['KtxQ1', 'KtxQ2']
  return Object.values(tradeAssetObj.value).some(item => targets.includes(item.asset) && item.total * 1 > 0)
})
const isShowCouponTips = ref(false)
const goWithdrawalFun = () => {
  if (isHasAccountCoupon.value) {
    isShowCouponTips.value = true
    return false
  }
  router.push(`/${locale.value}/my/assets/withdrawal`)
}
const goWithdrawalRouter = () => {
  router.push(`/${locale.value}/my/assets/withdrawal`)
}
onMounted(() => {
  getNotify()
  getAssetsByCoin()
  props.useUser.getUserInfoAction()
})
</script>
<style lang="scss" scoped>
  @import url('@/assets/style/orders/wallet.scss');
</style>