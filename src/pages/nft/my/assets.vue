<template>
  <Header />
  <div class="nft-assets-container">
    <div class="nft-h5-cont flex-box">
      <span class="active">{{ $t('我的NFT') }}</span>
      <MonoRightArrowShort :size="16" class="fit-tc-secondary mg-lr4 rotate" />
    </div>
    <div class="nft-assets-wrapper">
      <div class="nft-assets-title flex-box space-between">
        <h1>{{ $t('我的NFT资产') }}</h1>
        <div class="nft-title-btn">
          <el-button @click="isShowDeposit = true">{{ $t('充值NFT') }}</el-button>
          <el-button type="primary" :disabled="checkedList.length === 0" @click="showWithdrawal">{{ $t('提现NFT') }}</el-button>
        </div>
      </div>
      <div class="nft-nav-list">
        <ul class="flex-box">
          <li :class="{'active': curPage === '/nft/my/assets/nft-list'}" @click="router.push(`/${locale}/nft/my/assets/nft-list`)">{{ $t('我的NFT') }}</li>
          <li :class="{'active': curPage === '/nft/my/assets/deposit-list'}" @click="router.push(`/${locale}/nft/my/assets/deposit-list`)">{{ $t('充值记录') }}</li>
          <li :class="{'active': curPage === '/nft/my/assets/withdrawal-list'}" @click="router.push(`/${locale}/nft/my/assets/withdrawal-list`)">{{ $t('提现记录') }}</li>
        </ul>
      </div>
      <div class="nft-assets-content">
        <NuxtPage @getNftLen="getNftLen" />
      </div>
    </div>
    <div class="nft-h5-btn">
      <div class="flex-box pd-tb12" style="padding-left:20px;padding-right:20px;">
        <el-button class="flex-1" @click="router.push(`/${locale}/nft/my/deposit`)">{{ $t('充值NFT') }}</el-button>
        <el-button class="flex-1" :disabled="checkedList.length === 0" type="primary" @click="showWithdrawal">{{ $t('提现NFT') }}</el-button>
      </div>
    </div>
  </div>
  <el-dialog v-if="isShowDeposit" v-model="isShowDeposit" width="900" class="deposit-withdawl-dialog" :close-on-click-modal="false" @close="isShowDeposit = false">
    <NFTDeposit />
  </el-dialog>
  <el-dialog v-if="isShowWithdrawal" v-model="isShowWithdrawal" width="900" class="deposit-withdawl-dialog" :close-on-click-modal="false" @close="isShowWithdrawal = false">
    <NFTWithdrawal />
  </el-dialog>
</template>
<script lang="ts" setup>
  import { ElButton, ElDialog } from 'element-plus'
  import NFTDeposit from '~/components/nft/my/deposit.vue'
  import NFTWithdrawal from '~/components/nft/my/withdrawal.vue'
  import { useUserStore } from '~/stores/useUserStore'
  import { useCommonData } from '~/composables/index'
  const useCommon = useCommonData()
  const store = useUserStore()
  const { getUserInfoAction } = store
  const { isLogin, userInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  watch(() => userInfo.value, (val) => {
    if (JSON.stringify(val) !== '{}' && val.is_paper_trader * 1 === 1) {
      router.push(`/${locale.value}/my/dashboard`)
    }
  }, {
    immediate: true
  })
  const curPage = computed(() => {
    const pathAry = router.currentRoute.value.path.split('/')
    pathAry.splice(1, 1)
    if (pathAry.join('/').endsWith('/')) {
      return pathAry.join('/').replace(/\/$/g, '')
    }
    return pathAry.join('/')
  })
  const isShowDeposit = ref(false)
  const isShowWithdrawal = ref(false)
  const checkedList = ref([])
  const getNftLen = (arr) => {
    checkedList.value = arr
  }
  const showWithdrawal = () => {
    if (checkedList.value.length === 0) {
      return false
    }
    router.push(`/${locale.value}/nft/my/withdrawal`)
    isShowWithdrawal = true
  }
  onMounted(async() => {
    if (!isLogin.value) {
      useCommon.openLogin()
      return false
    }
    if (JSON.stringify(userInfo.value) === '{}') {
      await getUserInfoAction()
    }
  })
</script>
<style lang="scss" scoped>
  @import '@/assets/style/nft/assets.scss';
</style>