<template>
  <div>
    <div class="transaction-content-right">
      <!-- 在中等屏幕断点下显示的文案 -->
      <div class="trading-title-container">
        <div class="trading-main-title">{{ $t('使用第三方支付买卖加密货币现在就开始吧') }}</div>
        <div class="trading-sub-title">{{ $t('现在开始KTX为您提供多币种多渠道的买卖方式') }}</div>
        <div class="trading-sub-title">{{ $t('您可以使用我们支持的第三方支付在KTX上方便的买卖USDT或其他加密货币') }}</div>
      </div>
      <div :class="state.navbar === 'buy' ? 'transaction-content-left-bg' : 'transaction-content-right-bg'">
        <div class="navbar-box">
          <div :class="state.navbar === 'buy' ? 'trade' : 'isTrade'" @click="handleNavbarClick('buy')">{{ $t('买入') }}
          </div>
          <!-- @click="handleNavbarClick('sell')" 卖出暂时关闭-->
          <div class="coming-soon-box" :class="state.navbar === 'sell' ? 'trade' : 'isTrade'">{{ $t('卖出') }}
            <!-- 敬请期待 -->
              <div class="coming-soon-icon" :class="{ loaded: state.isComponentLoaded }">{{ $t('敬请期待') }}</div>
          </div>
        </div>
        <!-- 买入 -->
        <div v-show="state.navbar === 'buy'">
          <div class="support-right-box">
            <div class="transaction-right-payment-text">{{ $t('您支付') }}</div>
            <div class="payment-box">
              <div>
                <input type="text" class="input-style" placeholder="10-10,000" v-model="state.fiatPayValue"
                  @input="handleNumberInput" />
                <div class="no-payment-v1" v-if="state.showMinAmountError">{{ $t('最小金额 10') }} {{ selectedYouPay.code }}
                </div>
                <div class="no-payment-v1" v-if="state.showMaxAmountError">{{ $t('最大金额 10,000') }} {{
                  selectedYouPay.code }}</div>
              </div>
              <div class="el-dropdown-link" @click="state.isShowModal = true">
                <img v-if="selectedYouPay.icon" :src="selectedYouPay.icon" class="item-support-right-icon">
                <span class="active-text">{{ selectedYouPay.code }}</span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.isShowModal">
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('您支付') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(1)" />
                    </el-icon>
                  </div>
                  <div>
                    <el-input v-model="state.modalInput" class="modal-box-content-input" clearable>
                      <template #prepend>
                        <MonoSearch size="16" />
                      </template>
                    </el-input>
                  </div>
                  <div class="modal-box-content-list">
                    <div v-for="(item, index) of filteredSupportedFiatList" :key="index" class="modal-box-content-item"
                      @click="handleYouPay(index, item, 'buy')">
                      <img :src="item.icon" class="item-support-right-icon">
                      <div class="modal-box-content-item-text">
                        <div class="active-text">{{ item.code }}</div>
                        <div class="item-currencies-value">{{ item.name }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1">
            <div class="transaction-right-payment-text">{{ $t('您将收到') }}</div>
            <div class="payment-box">
              <Skeleton :width="80" :height="20" v-if="state.buyQuotesLoading || !selectedCurrency.displayName" />
              <div v-else>
                <NumberAnimation :targetNumber="currentReceiveAmount"></NumberAnimation>
              </div>
              <div class="el-dropdown-link" @click="state.isShowModal_v1 = true">
                <img v-if="selectedYouGet.icon" :src="selectedYouGet.icon" class="item-support-right-icon">
                <span class="active-text">{{ selectedYouGet.code }}</span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.isShowModal_v1">
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('您将收到') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(2)" />
                    </el-icon>
                  </div>
                  <div>
                    <div>
                      <el-input v-model="state.modalInput_v1" class="modal-box-content-input" clearable>
                        <template #prepend>
                          <MonoSearch size="16" />
                        </template>
                      </el-input>
                    </div>
                    <div class="modal-box-content-list">
                      <div v-for="(item, index) of filteredSupportedCurrencyList" :key="index"
                        class="modal-box-content-item" @click="handleYouGet(index, item, 'buy')">
                        <img :src="item.icon" class="item-support-right-icon">
                        <div class="item-support-flex">
                          <div class="modal-box-content-item-text">
                            <div class="active-text">{{ item.code }}</div>
                            <div class="item-currencies-value">{{ item.name }}</div>
                          </div>
                          <div class="item-currencies-value">{{ item.networkDisplayName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1"
            @click="(selectedCurrency.displayName && !state.buyQuotesLoading) ? state.isShowModal_v2 = true : null">
            <div class="transaction-right-payment-text">{{ $t('选择购买渠道') }}</div>
            <div class="payment-box">
              <div style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
                <Skeleton :width="120" :height="20" v-if="state.buyQuotesLoading || !selectedCurrency.displayName" />
                <div class="channel-box" v-else>
                  <img :src="selectedCurrency.icon" class="channel-icon">
                  <div class="flex-box">
                    <span class="item-label-title">{{ selectedCurrency.displayName }}</span>
                    <span class="quotation"
                      v-if="selectedCurrency.recommendations && selectedCurrency.recommendations.includes('BestPrice')"></span>
                  </div>
                </div>
                <Skeleton :width="80" :height="20" v-if="state.buyQuotesLoading || !selectedCurrency.payout" />
                <div class="el-dropdown-link" v-else>
                  <span class="quotation-text">{{ selectedCurrency.payout }}</span>
                  <el-icon class="active-icon-v1">
                    <CaretBottom />
                  </el-icon>
                </div>
              </div>
              <div>
                <div class="modal-box" v-if="state.isShowModal_v2" @click.stop>
                  <div class="modal-box-content">
                    <div class="modal-box-content-header">
                      <span class="modal-box-content-header-title">{{ $t('购买渠道') }}</span>
                      <el-icon class="close-icon">
                        <CloseBold @click="closeModal(3)" />
                      </el-icon>
                    </div>
                    <div>
                      <div>
                        <el-input v-model="state.modalInput_v2" class="modal-box-content-input" clearable>
                          <template #prepend>
                            <MonoSearch size="16" />
                          </template>
                        </el-input>
                      </div>
                      <div class="modal-box-content-list">
                        <div v-for="(item, index) of filteredQuotesList" :key="index" class="item-channel-list"
                          @click="handleChannelCommand(index, item)">
                          <div class="item-channel-title">
                            <img v-if="item.icon" :src="item.icon" class="item-support-right-icon_v1">
                            <div class="modal-box-content-item-text">
                              <div class="active-text">{{ item?.displayName || item?.ramp || '' }}</div>
                              <div v-for="(i, v) of item.recommendations" :key="v">
                                <div class="quotation" v-if="i === 'BestPrice'">{{ $t('最优报价') }}</div>
                              </div>
                            </div>
                          </div>
                          <div class="item-channel-title_v1">
                            <div class="receive-text">{{ $t('您收到的') }}</div>
                            <div class="price-text">{{ item.payout }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1" @click="(selectedPayment.name && !state.buyQuotesLoading) ? state.isShowModal_v3 = true : null">
            <div class="transaction-right-payment-text">{{ $t('选择支付方式') }}</div>
            <div class="payment-box">
              <Skeleton :width="100" :height="20" v-if="state.buyQuotesLoading || !selectedPayment.name" />
              <div class="channel-box" v-else>
                <img :src="selectedPayment.icon" class="channel-icon">
                <span class="item-label-title">{{ selectedPayment.name }}</span>
              </div>
              <Skeleton :width="30" :height="20" v-if="state.buyQuotesLoading || !selectedPayment.name" />
              <div class="el-dropdown-link" v-else>
                <span class="position"></span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.isShowModal_v3" @click.stop>
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('支付方式') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(4)" />
                    </el-icon>
                  </div>
                  <div>
                    <div>
                      <el-input v-model="state.modalInput_v3" class="modal-box-content-input" clearable>
                        <template #prepend>
                          <MonoSearch size="16" />
                        </template>
                      </el-input>
                    </div>
                    <div class="modal-box-content-list">
                      <div v-for="(item, index) of hasSelectedPaymentList" :key="index" class="modal-box-content-item"
                        @click="handlePaymentMethod(index, item)">
                        <div class="modal-box-content-item-text-v1">
                          <img :src="item.icon" class="item-support-right-icon">
                          <div class="active-text">{{ item.name }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="no-payment" v-if="shouldShowNoPaymentMessage">{{ $t('这些详细信息中没有可用的提款方式。请选择另一种支付方式，法定货币或加密货币') }}
          </div>
          <div>
            <div class="estimated-price" v-if="Number(currentReceiveAmount) !== 0 && state.quotesListData.rate">{{
              $t('预计价格') }}
              <span>1 {{ selectedYouGet.code }} ≈ {{
                state.quotesListData.rate.toFixed(2) }} {{ selectedYouPay.code }}</span>
            </div>
            <el-button v-if="!isLogin" @click="goLogin" class="submit-btn" type="primary">{{ $t('立即登录') }}</el-button>
            <el-button v-if="isLogin" :loading="state.transactionLoading"
              :class="Number(currentReceiveAmount) !== 0 ? 'submit-btn' : 'submit-btn-disabled'" type="primary"
              @click="Number(currentReceiveAmount) !== 0 ? trade('buy') : ''">{{ $t('购买') }}
              {{ selectedYouGet.code }}
            </el-button>
            <div class="technical-support">{{ $t('该服务由 Onramper 提供技术支持') }} <a href="https://www.onramper.com/widget"
                target="_blank">{{ $t('Onramper官方服务') }}</a></div>
          </div>
        </div>

        <!-- 卖出-->
        <div v-show="state.navbar === 'sell'">
          <div class="support-right-box">
            <div class="transaction-right-payment-text">{{ $t('您出售') }}</div>
            <div class="payment-box">
              <div>
                <input type="text" class="input-style" :placeholder="state.recommendedData.amount"
                  :value="sellInputValue" @input="handleSellNumberInput" />
              </div>
              <div class="el-dropdown-link" @click="state.sellData.isShowModal = true">
                <img :src="selectedSellYouSell.icon" class="item-support-right-icon">
                <span class="active-text">{{ selectedSellYouSell.code }}</span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.sellData.isShowModal">
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('您出售') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(5)" />
                    </el-icon>
                  </div>
                  <div>
                    <div>
                      <el-input v-model="state.sellData.modalInput" class="modal-box-content-input" clearable>
                        <template #prepend>
                          <MonoSearch size="16" />
                        </template>
                      </el-input>
                    </div>
                    <div class="modal-box-content-list">
                      <div v-for="(item, index) of filteredSellSupportedCurrencyList" :key="index"
                        class="modal-box-content-item" @click="handleSellYouSell(index, item, 'sell')">
                        <img :src="item.icon" class="item-support-right-icon">
                        <div class="item-support-flex">
                          <div class="modal-box-content-item-text">
                            <div class="active-text">{{ item.code }}</div>
                            <div class="item-currencies-value">{{ item.name }}</div>
                          </div>
                          <div class="item-currencies-value">{{ item.networkDisplayName }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1">
            <div class="transaction-right-payment-text">{{ $t('您将收到') }}</div>
            <div class="payment-box">
              <Skeleton :width="80" :height="20" v-if="state.sellQuotesLoading || !selectedCurrency.displayName" />
              <div v-else>
                <NumberAnimation :targetNumber="currentSellReceiveAmount"></NumberAnimation>
              </div>
              <div class="el-dropdown-link" @click="state.sellData.isShowModal_v1 = true">
                <img :src="selectedSellYouReceive.icon" class="item-support-right-icon">
                <span class="active-text">{{ selectedSellYouReceive.code }}</span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.sellData.isShowModal_v1">
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('您将收到') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(6)" />
                    </el-icon>
                  </div>
                  <div>
                    <div>
                      <el-input v-model="state.sellData.modalInput_v1" class="modal-box-content-input" clearable>
                        <template #prepend>
                          <MonoSearch size="16" />
                        </template>
                      </el-input>
                    </div>
                    <div class="modal-box-content-list">
                      <div v-for="(item, index) of filteredSellSupportedFiatList" :key="index"
                        class="modal-box-content-item" @click="handleSellYouReceive(index, item)">
                        <img :src="item.icon" class="item-support-right-icon">
                        <div class="modal-box-content-item-text">
                          <div class="active-text">{{ item.code }}</div>
                          <div class="item-currencies-value">{{ item.name }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1"
            @click="(selectedCurrency.displayName && !state.sellQuotesLoading) ? state.sellData.isShowModal_v2 = true : null">
            <div class="transaction-right-payment-text">{{ $t('选择出售渠道') }}</div>
            <div class="payment-box">
              <div style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
                <Skeleton :width="120" :height="20" v-if="state.sellQuotesLoading || !selectedCurrency.displayName" />
                <div class="channel-box" v-else>
                  <img :src="selectedCurrency.icon" class="channel-icon">
                  <div class="flex-box">
                    <span class="item-label-title">{{ selectedCurrency.displayName }}</span>
                    <span class="quotation"
                      v-if="selectedCurrency.recommendations && selectedCurrency.recommendations.includes('BestPrice')"></span>
                  </div>
                </div>
                <Skeleton :width="80" :height="20" v-if="state.sellQuotesLoading || !selectedCurrency.payout" />
                <div class="el-dropdown-link" v-else>
                  <span class="quotation-text">{{ selectedCurrency.payout }}</span>
                  <el-icon class="active-icon-v1">
                    <CaretBottom />
                  </el-icon>
                </div>
              </div>
              <div>
                <div class="modal-box" v-if="state.sellData.isShowModal_v2" @click.stop>
                  <div class="modal-box-content">
                    <div class="modal-box-content-header">
                      <span class="modal-box-content-header-title">{{ $t('出售渠道') }}</span>
                      <el-icon class="close-icon">
                        <CloseBold @click="closeModal(7)" />
                      </el-icon>
                    </div>
                    <div>
                      <div>
                        <el-input v-model="state.sellData.modalInput_v2" class="modal-box-content-input" clearable>
                          <template #prepend>
                            <MonoSearch size="16" />
                          </template>
                        </el-input>
                      </div>
                      <div class="modal-box-content-list">
                        <div v-for="(item, index) of filteredSellQuotesList" :key="index" class="item-channel-list"
                          @click="handleSellChannelCommand(index, item)">
                          <div class="item-channel-title">
                            <img v-if="item.icon" :src="item.icon" class="item-support-right-icon_v1">
                            <div class="modal-box-content-item-text">
                              <div class="active-text">{{ item?.displayName || item?.ramp || '' }}</div>
                              <div v-for="(i, v) of item.recommendations" :key="v">
                                <div class="quotation" v-if="i === 'BestPrice'">{{ $t('最优报价') }}</div>
                              </div>
                            </div>
                          </div>
                          <div class="item-channel-title_v1">
                            <div class="receive-text">{{ $t('您收到的') }}</div>
                            <div class="price-text">{{ item.payout }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="support-right-box_v1"
            @click="(selectedPayment.name && !state.sellQuotesLoading) ? state.sellData.isShowModal_v3 = true : null">
            <div class="transaction-right-payment-text">{{ $t('选择收款方式') }}</div>
            <div class="payment-box">
              <Skeleton :width="120" :height="20" v-if="state.sellQuotesLoading || !selectedPayment.name" />
              <div class="channel-box" v-else>
                <img :src="selectedPayment.icon" class="channel-icon">
                <span class="item-label-title">{{ selectedPayment.name }}</span>
              </div>
              <Skeleton :width="30" :height="20" v-if="state.sellQuotesLoading || !selectedPayment.name" />
              <div class="el-dropdown-link" v-else>
                <span class="position"></span>
                <el-icon class="active-icon">
                  <CaretBottom />
                </el-icon>
              </div>
              <div class="modal-box" v-if="state.sellData.isShowModal_v3" @click.stop>
                <div class="modal-box-content">
                  <div class="modal-box-content-header">
                    <span class="modal-box-content-header-title">{{ $t('收款方式') }}</span>
                    <el-icon class="close-icon">
                      <CloseBold @click="closeModal(8)" />
                    </el-icon>
                  </div>
                  <div>
                    <div>
                      <el-input v-model="state.sellData.modalInput_v3" class="modal-box-content-input" clearable>
                        <template #prepend>
                          <MonoSearch size="16" />
                        </template>
                      </el-input>
                    </div>
                    <div class="modal-box-content-list">
                      <div v-for="(item, index) of hasSelectedPaymentList" :key="index" class="modal-box-content-item"
                        @click="handlePaymentMethod(index, item)">
                        <img :src="item.icon" class="item-support-right-icon">
                        <div class="modal-box-content-item-text-v1">
                          <div class="active-text">{{ item.name }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="no-payment" v-if="shouldShowNoPaymentMessage">{{ $t('这些详细信息中没有可用的提款方式。请选择另一种支付方式，法定货币或加密货币') }}
          </div>
          <div class="submit-btn-box">
            <div class="estimated-price" v-if="Number(currentSellReceiveAmount) !== 0 && state.sellQuotesListData.rate">
              {{
                $t('预计价格') }} <span>1 {{
                selectedSellYouSell.code }} ≈ {{ state.sellQuotesListData.rate.toFixed(2) }} {{
                  selectedSellYouReceive.code
                }}</span></div>
            <el-button v-if="!isLogin" @click="goLogin" class="submit-btn" type="primary">{{ $t('立即登录') }}</el-button>
            <el-button v-if="isLogin" :loading="state.transactionLoading"
              :class="Number(currentSellReceiveAmount) !== 0 ? 'submit-btn' : 'submit-btn-disabled'" type="primary"
              @click="Number(currentSellReceiveAmount) !== 0 ? trade('sell') : ''">{{ $t('出售') }} {{
                selectedSellYouSell.code
              }}</el-button>
            <div class="technical-support">{{ $t('该服务由 Onramper 提供技术支持') }} <a href="https://www.onramper.com/widget"
                target="_blank">{{ $t('Onramper官方服务') }}</a></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 跳往第三方支付 -->
    <div class="dialog-box">
      <el-dialog v-model="state.dialogVisible_v1" :title="$t('继续完成交易')" width="500">
        <div class="dialog-content">
          <div class="dialog-content-title">
            <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/secure-icon.png"
              class="secure-icon">
            <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/secure-icon-dark.png"
              class="secure-icon">
            <div class="secure-text">{{ $t('您的链接是安全的') }}</div>
          </div>
          <div class="dialog-box">
            <div class="dialog-box-title">{{ $t('在打开的新标签页中使用') }} <span>{{ selectedCurrency.displayName }}</span> {{
              $t('完成购买') }}</div>
            <div class="dialog-box-text">{{ $t('* 您可以在KTX买卖币交易历史订单中跟踪交易状态') }}</div>
            <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/transaction-bg.png"
              class="transaction-bg">
            <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/transaction-bg-dark.png"
              class="transaction-bg">
            <div class="dialog-box-text">{{ $t('如果没有打开新标签，请在浏览器中检查与标签相关的设置，然后单击下面的按钮重试') }}</div>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="tradeBtn">
              {{ $t('在新标签页中打开') }}{{ selectedCurrency.displayName }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getSupportDefaultsAll, getCoinsSupported, getBuyQuotes, getSellQuotes, rampsAllList, paymentsBySourceList, sendCreateTrade } from '~/api/public.ts'
import { CaretBottom, CloseBold } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import NumberAnimation from '~/components/oneClickPurchase/numberAnimation.vue'
import { imgDmain } from '~/config'
import Skeleton from '~/components/oneClickPurchase/skeleton.vue'
import { useUserStore } from '~/stores/useUserStore'
import { useCommonData } from '~/composables/index'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const route = useRoute();
const router = useRouter();
const userStore = useUserStore()
const { isLogin, userInfo } = storeToRefs(userStore)
const useCommon = useCommonData()
const state = reactive({
  isComponentLoaded:false, // 敬请期待
  transactionLoading: false,
  buyQuotesLoading: false,
  sellQuotesLoading: false,
  youPayIndex: 0,
  youGetIndex: 0,
  selectedIndex: 0,
  selectedChannelIndex: 0,
  selectedPaymentIndex: 0,
  hasSelectedPayment: false,
  hasUserSelectedFiat: false,
  hasUserSelectedCrypto: false,
  isHandleNavbarCalling: false,
  isInitializing: true,
  navbar: 'buy',
  dialogVisible_v1: false,
  fiatPayValue: '',
  fiatSellPayValue: '',
  showMinAmountError: false,
  showMaxAmountError: false,
  modalInput: '',
  modalInput_v1: '',
  modalInput_v2: '',
  modalInput_v3: '',
  isShowModal: false,
  isShowModal_v1: false,
  isShowModal_v2: false,
  isShowModal_v3: false,
  defaultsAll: {},
  recommendedData: {},
  supportedFiatList: [],
  supportedCurrencyList: [],
  inputTimer: null,
  buyQuotesList: [],
  sellQuotesList: [],
  quotesListData: {},
  sellQuotesListData: {},
  rampsAllDataList: [],
  quotesList: [],
  sellQuotesList_merged: [],
  paymentsList: [],
  createTradeData: {},
  sellData: {
    fiatSellPayValue: '',
    modalInput: '',
    modalInput_v1: '',
    modalInput_v2: '',
    modalInput_v3: '',
    isShowModal: false,
    isShowModal_v1: false,
    isShowModal_v2: false,
    isShowModal_v3: false,
  },
})
const goLogin = () => {
  useCommon.openLogin()
}
// 处理选项卡点击事件，只有在切换到不同选项卡时才调用handleNavbar
const handleNavbarClick = (type: string) => {
  if (state.navbar === type) {
    return
  }
  handleNavbar(type)
}
// 切换买 - 卖
const handleNavbar = (type: string) => {
  state.isHandleNavbarCalling = true
  state.navbar = type
  state.hasUserSelectedFiat = false
  state.hasUserSelectedCrypto = false
  state.youPayIndex = 0
  state.youGetIndex = 0
  state.selectedChannelIndex = 0
  state.selectedPaymentIndex = 0
  state.hasSelectedPayment = false
  state.paymentsList = []
  state.fiatPayValue = ''
  state.sellData.fiatSellPayValue = ''
  state.sellData.modalInput = ''
  state.sellData.modalInput_v1 = ''
  // 重置错误状态
  state.showMinAmountError = false
  state.showMaxAmountError = false
  // 重置买入和卖出加载状态
  state.buyQuotesLoading = false
  state.sellQuotesLoading = false

  getSupportDefaultsAllList().then(() => {
    nextTick(async () => {
      if (type === 'sell') {
        if (state.recommendedData && typeof state.recommendedData === 'object') {
          if (state.recommendedData.source && state.supportedCurrencyList.length > 0) {
            const matchedCryptoIndex = state.supportedCurrencyList.findIndex(item => item.code === state.recommendedData.source)
            if (matchedCryptoIndex !== -1) {
              state.youGetIndex = matchedCryptoIndex
            }
          }

          if (state.recommendedData.target && state.supportedFiatList.length > 0) {
            const matchedFiatIndex = state.supportedFiatList.findIndex(item => item.code === state.recommendedData.target)
            if (matchedFiatIndex !== -1) {
              state.youPayIndex = matchedFiatIndex
            }
          }
        }
        setDefaultAmountForSell()
        await getPaymentType()
        await getRampsAllList()
        getSellQuotesList()
      } else {
        if (state.recommendedData && typeof state.recommendedData === 'object') {
          if (state.recommendedData.source && state.supportedFiatList.length > 0) {
            const matchedFiatIndex = state.supportedFiatList.findIndex(item => item.code === state.recommendedData.source)
            if (matchedFiatIndex !== -1) {
              state.youPayIndex = matchedFiatIndex
            }
          }

          if (state.recommendedData.target && state.supportedCurrencyList.length > 0) {
            const matchedCryptoIndex = state.supportedCurrencyList.findIndex(item => item.code === state.recommendedData.target)
            if (matchedCryptoIndex !== -1) {
              state.youGetIndex = matchedCryptoIndex
            }
          }
        }
        setDefaultAmount()
        await getPaymentType()
        await getRampsAllList()
        getBuyQuotesList()
      }
      state.isHandleNavbarCalling = false
    })
  })
}
// 买卖选项卡
const trade = (type: string) => {
  if (type === 'buy') {
    getCreateTrade('buy')
  }
  else {
    getCreateTrade('sell')
  }
}

const tradeBtn = () => {
  window.open(state.createTradeData.transactionInformation.url, '_blank')
}
const handleNumberInput = (event) => {
  const value = event.target.value
  let filteredValue = value.replace(/[^\d.]/g, '')

  const parts = filteredValue.split('.')
  if (parts.length > 2) {
    filteredValue = parts[0] + '.' + parts.slice(1).join('')
  }

  // 验证金额范围
  const numericValue = parseFloat(filteredValue)
  state.showMinAmountError = false
  state.showMaxAmountError = false

  if (filteredValue && !isNaN(numericValue)) {
    if (numericValue <= 10) {
      state.showMinAmountError = true
    } else if (numericValue > 10000) {
      state.showMaxAmountError = true
    }
  }

  state.fiatPayValue = filteredValue
  event.target.value = filteredValue

  state.quotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }
  clearTimeout(state.inputTimer)
  state.inputTimer = setTimeout(() => {
    const currentFiat = state.supportedFiatList[state.youPayIndex]
    const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
    if (filteredValue && parseFloat(filteredValue) > 0 && currentFiat?.id && currentCrypto?.id) {
      getBuyQuotesList()
    }
  }, 100)
}
// 卖出输入框的方法
const handleSellNumberInput = (event) => {
  const value = event.target.value
  let filteredValue = value.replace(/[^\d.]/g, '')
  const parts = filteredValue.split('.')
  if (parts.length > 2) {
    filteredValue = parts[0] + '.' + parts.slice(1).join('')
  }
  state.sellData.fiatSellPayValue = filteredValue
  event.target.value = filteredValue

  state.sellQuotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }

  clearTimeout(state.inputTimer)
  state.inputTimer = setTimeout(() => {
    const currentFiat = state.supportedFiatList[state.youPayIndex]
    const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
    if (filteredValue && parseFloat(filteredValue) > 0 && currentFiat?.id && currentCrypto?.id) {
      getSellQuotesList()
    }
  }, 100)
}
const closeModal = (type) => {
  switch (type) {
    case 1:
      state.isShowModal = false
      state.modalInput = ''
      break
    case 2:
      state.isShowModal_v1 = false
      state.modalInput_v1 = ''
      break
    case 3:
      state.isShowModal_v2 = false
      state.modalInput_v2 = ''
      break
    case 4:
      state.isShowModal_v3 = false
      state.modalInput_v3 = ''
      break
    case 5:
      state.sellData.isShowModal = false
      state.sellData.modalInput = ''
      break
    case 6:
      state.sellData.isShowModal_v1 = false
      state.sellData.modalInput_v1 = ''
      break
    case 7:
      state.sellData.isShowModal_v2 = false
      state.sellData.modalInput_v2 = ''
      break
    case 8:
      state.sellData.isShowModal_v3 = false
      state.sellData.modalInput_v3 = ''
      break
  }
}
const selectedCurrency = computed(() => {
  if (state.navbar === 'sell') {
    let list = state.sellQuotesList_merged || []
    const result = list[state.selectedChannelIndex] || list[0] || { ramp: '', payout: '', displayName: '', icon: '', quoteId: '' }
    return result
  } else {
    let list = state.quotesList || []
    const result = list[state.selectedChannelIndex] || list[0] || { ramp: '', payout: '', displayName: '', icon: '', quoteId: '' }
    return result
  }
})
// 您支付
const selectedYouPay = computed(() => {
  let list = state.supportedFiatList || []
  const selected = list[state.youPayIndex] || list[0] || { icon: '', code: '', name: '' }
  if (selected.code) {
    if (state.hasUserSelectedFiat && state.defaultsAll && typeof state.defaultsAll === 'object') {
      const matchedData = Object.values(state.defaultsAll).find((item: any) =>
        item && item.source === selected.code
      )
      if (matchedData && matchedData.amount !== undefined) {
        return {
          ...selected,
          amount: matchedData.amount
        }
      }
    } else if (!state.hasUserSelectedFiat && state.recommendedData && typeof state.recommendedData === 'object') {
      if (state.recommendedData.source === selected.code && state.recommendedData.amount !== undefined) {
        return {
          ...selected,
          amount: state.recommendedData.amount
        }
      }
    }
  }
  return selected
})
// 您将收到
const selectedYouGet = computed(() => {
  let list = state.supportedCurrencyList || []
  const selected = list[state.youGetIndex] || list[0] || { icon: '', code: '', name: '' }
  if (selected.code) {
    if (state.hasUserSelectedCrypto && state.defaultsAll && typeof state.defaultsAll === 'object') {
      const matchedData = Object.values(state.defaultsAll).find((item: any) =>
        item && item.target === selected.code
      )
      if (matchedData && matchedData.target !== undefined) {
        return {
          ...selected,
          target: matchedData.target
        }
      }
    } else if (!state.hasUserSelectedCrypto && state.recommendedData && typeof state.recommendedData === 'object') {
      if (state.recommendedData.target === selected.code) {
        return {
          ...selected,
          target: state.recommendedData.target
        }
      }
    }
  }

  return selected
})
// 计算当前选中渠道对应的收到金额
const currentReceiveAmount = computed(() => {
  const current = selectedCurrency.value
  if (current && current.payout) {
    const payoutValue = parseFloat(current.payout)
    return isNaN(payoutValue) ? 0 : parseFloat(payoutValue.toFixed(8))
  }
  return 0
})
// 计算卖出时收到的法币金额
const currentSellReceiveAmount = computed(() => {
  const current = selectedCurrency.value
  if (current && current.payout) {
    // 直接使用API返回的payout值，这是卖出时应该收到的法币金额
    const payoutValue = parseFloat(current.payout)
    return isNaN(payoutValue) ? 0 : parseFloat(payoutValue.toFixed(2))
  }
  return 0
})
// 卖出-您出售 加密货币选择和默认值
const selectedSellYouSell = computed(() => {
  let list = state.supportedCurrencyList || []
  const selected = list[state.youGetIndex] || list[0] || { icon: '', code: '', name: '' }

  if (selected.code) {
    if (state.hasUserSelectedCrypto && state.defaultsAll && typeof state.defaultsAll === 'object') {
      const matchedData = Object.values(state.defaultsAll).find((item: any) =>
        item && item.source === selected.code
      )
      if (matchedData && matchedData.amount !== undefined) {
        return {
          ...selected,
          amount: matchedData.amount
        }
      }
    } else if (!state.hasUserSelectedCrypto && state.recommendedData && typeof state.recommendedData === 'object') {
      if (state.recommendedData.source === selected.code && state.recommendedData.amount !== undefined) {
        return {
          ...selected,
          amount: state.recommendedData.amount
        }
      }
    }
  }
  return selected
})
// 判断是否应该显示无可用支付方式的提示
const shouldShowNoPaymentMessage = computed(() => {
  // 必须初始化完成
  if (state.isInitializing) return false
  
  // 必须不在加载中
  if (state.navbar === 'buy' && state.buyQuotesLoading) return false
  if (state.navbar === 'sell' && state.sellQuotesLoading) return false
  
  // 检查是否已经调用过接口但没有有效的报价（所有报价都没有rate）
  if (state.navbar === 'buy') {
    // buyQuotesList 有数据但过滤后 quotesList 为空，说明没有包含rate的报价
    return state.buyQuotesList.length > 0 && state.quotesList.length === 0
  } else {
    // 卖出模式同理
    return state.sellQuotesList.length > 0 && state.sellQuotesList_merged.length === 0
  }
})

// 卖出-您将收到 法币选择和默认值
const selectedSellYouReceive = computed(() => {
  let list = state.supportedFiatList || []
  const selected = list[state.youPayIndex] || list[0] || { icon: '', code: '', name: '' }
  if (selected.code) {
    if (state.hasUserSelectedFiat && state.defaultsAll && typeof state.defaultsAll === 'object') {
      const matchedData = Object.values(state.defaultsAll).find((item: any) =>
        item && item.target === selected.code
      )
      if (matchedData && matchedData.amount !== undefined) {
        return {
          ...selected,
          amount: matchedData.amount
        }
      }
    } else if (!state.hasUserSelectedFiat && state.recommendedData && typeof state.recommendedData === 'object') {
      if (state.recommendedData.target === selected.code && state.recommendedData.amount !== undefined) {
        return {
          ...selected,
          amount: state.recommendedData.amount
        }
      }
    }
  }
  return selected
})
// 卖出输入框的值
const sellInputValue = computed(() => {
  if (state.sellData.fiatSellPayValue !== null && state.sellData.fiatSellPayValue !== undefined) {
    return state.sellData.fiatSellPayValue
  }

  // 如果是卖出模式且没有用户输入，使用 recommendedData 的默认值
  if (state.navbar === 'sell' &&
    !state.hasUserSelectedCrypto &&
    state.recommendedData &&
    typeof state.recommendedData === 'object' &&
    state.recommendedData.amount !== undefined) {
    const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
    if (currentCrypto && state.recommendedData.source === currentCrypto.code) {
      return state.recommendedData.amount.toString()
    }
  }

  if (selectedSellYouSell.value && selectedSellYouSell.value.amount !== undefined) {
    return selectedSellYouSell.value.amount.toString()
  }
  if (state.navbar === 'sell' &&
    state.recommendedData &&
    typeof state.recommendedData === 'object' &&
    state.recommendedData.amount !== undefined) {
    return state.recommendedData.amount.toString()
  }

  return ''
})
// 支持的支付方式
const selectedPayment = computed(() => {
  let list = state.paymentsList || []
  if (list.length === 0) {
    return { name: '', paymentTypeId: '', icon: '' }
  }
  const index = Math.min(state.selectedPaymentIndex, list.length - 1)
  return list[index] || list[0] || { name: '', paymentTypeId: '', icon: '' }
})
// 您支付
const handleYouPay = (index: number, item: any, type: string) => {
  state.hasUserSelectedFiat = true
  // 清除错误状态
  state.showMinAmountError = false
  state.showMaxAmountError = false

  let selectedItem = filteredSupportedFiatList.value[index]
  if (selectedItem) {
    let originalIndex = state.supportedFiatList.findIndex(item =>
      item.code === selectedItem.code && item.name === selectedItem.name
    )
    if (originalIndex !== -1) {
      state.youPayIndex = originalIndex
    }
    if (selectedItem.amount !== undefined) {
      state.fiatPayValue = selectedItem.amount.toString()
    }
  }

  state.quotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }

  closeModal(1)
  nextTick(async () => {
    await getPaymentType()
    if (type === 'buy') {
      getBuyQuotesList()
    }
    else {
      getSellQuotesList()
    }
  })
}
// 您收到
const handleYouGet = (index: number, item: any, type: string) => {
  state.hasUserSelectedCrypto = true
  let selectedItem = filteredSupportedCurrencyList.value[index]
  if (selectedItem) {
    let originalIndex = state.supportedCurrencyList.findIndex(item =>
      item.code === selectedItem.code && item.name === selectedItem.name
    )
    if (originalIndex !== -1) {
      state.youGetIndex = originalIndex
    }
  }

  state.quotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }

  closeModal(2)
  nextTick(async () => {
    await getPaymentType()
    if (type === 'buy') {
      getBuyQuotesList()
    }
    else {
      getSellQuotesList()
    }
  })
}
// 购买渠道
const handleChannelCommand = (index: number, item: any) => {
  const originalIndex = state.quotesList.findIndex(quote =>
    quote.id === item.id && quote.ramp === item.ramp
  )
  const previousIndex = state.selectedChannelIndex
  const newIndex = originalIndex >= 0 ? originalIndex : 0
  state.selectedChannelIndex = newIndex
  closeModal(3)
  updateQuotesListData()
  if (previousIndex !== newIndex) {
    nextTick(() => {
      refreshQuotes()
    })
  }
}
// 支付方式
const handlePaymentMethod = async (index: number, item: any) => {
  state.hasSelectedPayment = true
  let selectedItem = hasSelectedPaymentList.value[index]
  if (selectedItem) {
    let originalIndex = state.paymentsList.findIndex(item =>
      item.name === selectedItem.name
    )
    if (originalIndex !== -1) {
      state.selectedPaymentIndex = originalIndex
    }
  }

  if (state.navbar === 'sell') {
    closeModal(8)
  } else {
    closeModal(4)
  }

  nextTick(() => {
    if (state.navbar === 'sell') {
      getSellQuotesList()
    } else {
      getBuyQuotesList()
    }
  })
}
// 卖出-您出售 选择加密货币
const handleSellYouSell = (index: number, item: any, type: string) => {
  state.hasUserSelectedCrypto = true
  let selectedItem = filteredSellSupportedCurrencyList.value[index]
  if (selectedItem) {
    let originalIndex = state.supportedCurrencyList.findIndex(item =>
      item.code === selectedItem.code && item.name === selectedItem.name
    )
    if (originalIndex !== -1) {
      state.youGetIndex = originalIndex
    }
    if (selectedItem.amount !== undefined) {
      state.sellData.fiatSellPayValue = selectedItem.amount.toString()
    }
  }

  state.sellQuotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }

  closeModal(5)
  nextTick(async () => {
    await getPaymentType()
    if (type === 'sell') {
      getSellQuotesList()
    } else {
      getBuyQuotesList()
    }
  })
}
// 卖出-您将收到 选择法币
const handleSellYouReceive = (index: number, item: any) => {
  state.hasUserSelectedFiat = true
  let selectedItem = filteredSellSupportedFiatList.value[index]
  if (selectedItem) {
    let originalIndex = state.supportedFiatList.findIndex(item =>
      item.code === selectedItem.code && item.name === selectedItem.name
    )
    if (originalIndex !== -1) {
      state.youPayIndex = originalIndex
    }
  }

  state.sellQuotesListData = {
    payout: null,
    rate: null,
    selectedFee: null
  }

  closeModal(6)
  nextTick(async () => {
    await getPaymentType()
    getSellQuotesList()
  })
}
// 卖出渠道
const handleSellChannelCommand = (index: number, item: any) => {
  const originalIndex = state.sellQuotesList_merged.findIndex(quote =>
    quote.id === item.id && quote.ramp === item.ramp
  )
  const previousIndex = state.selectedChannelIndex
  const newIndex = originalIndex >= 0 ? originalIndex : 0
  state.selectedChannelIndex = newIndex
  closeModal(7)
  updateSellQuotesListData()
  if (previousIndex !== newIndex) {
    nextTick(() => {
      refreshQuotes()
    })
  }
}
// 更新 quotesListData 为当前选中的渠道数据 买入
const updateQuotesListData = () => {
  if (state.quotesList && state.quotesList.length > 0) {
    // 确保 selectedChannelIndex 在有效范围内
    if (state.selectedChannelIndex >= state.quotesList.length) {
      state.selectedChannelIndex = 0
    }

    const currentSelected = state.quotesList[state.selectedChannelIndex] || state.quotesList[0]
    state.quotesListData = {
      payout: currentSelected.payout,
      rate: currentSelected.rate,
      selectedFee: currentSelected.selectedFee
    }
  }
}
// 更新 sellQuotesListData 为当前选中的渠道数据 卖出
const updateSellQuotesListData = () => {
  if (state.sellQuotesList_merged && state.sellQuotesList_merged.length > 0) {
    // 确保 selectedChannelIndex 在有效范围内
    if (state.selectedChannelIndex >= state.sellQuotesList_merged.length) {
      state.selectedChannelIndex = 0
    }

    const currentSelected = state.sellQuotesList_merged[state.selectedChannelIndex] || state.sellQuotesList_merged[0]
    state.sellQuotesListData = {
      payout: currentSelected.payout,
      rate: currentSelected.rate,
      selectedFee: currentSelected.selectedFee
    }
  }
}
// 根据当买卖报价调接口
const refreshQuotes = () => {
  if (state.navbar === 'buy') {
    getBuyQuotesList()
  }
  else {
    getSellQuotesList()
  }
}
// 支付搜索
const filteredSupportedFiatList = computed(() => {
  let filteredByDefaults = state.supportedFiatList
  if (state.defaultsAll && typeof state.defaultsAll === 'object') {
    const sourceToAmountMap = new Map()
    Object.values(state.defaultsAll).forEach((item: any) => {
      if (item && item.source && item.amount !== undefined) {
        sourceToAmountMap.set(item.source, item.amount)
      }
    })
    if (sourceToAmountMap.size > 0) {
      filteredByDefaults = state.supportedFiatList
        .filter(item => sourceToAmountMap.has(item.code))
        .map(item => ({
          ...item,
          amount: sourceToAmountMap.get(item.code)
        }))
    }
  }
  if (!state.modalInput) {
    return filteredByDefaults
  }

  let searchText = state.modalInput.toLowerCase()
  return filteredByDefaults.filter(item =>
    item.code?.toLowerCase().includes(searchText) ||
    item.name?.toLowerCase().includes(searchText)
  )
})
// 您收到
const filteredSupportedCurrencyList = computed(() => {
  if (!state.modalInput_v1) {
    return state.supportedCurrencyList
  }

  let searchText = state.modalInput_v1.toLowerCase()
  return state.supportedCurrencyList.filter(item =>
    item.code?.toLowerCase().includes(searchText) ||
    item.name?.toLowerCase().includes(searchText)
  )
})
// 购买渠道搜索过滤
const filteredQuotesList = computed(() => {
  if (!state.modalInput_v2) {
    return state.quotesList
  }

  let searchText = state.modalInput_v2.toLowerCase()
  return state.quotesList.filter(item =>
    item.displayName?.toLowerCase().includes(searchText) ||
    item.ramp?.toLowerCase().includes(searchText) ||
    item.id?.toLowerCase().includes(searchText)
  )
})
// 支付方式过滤
const hasSelectedPaymentList = computed(() => {
  let nameValue = selectedCurrency.value.displayName?.toLowerCase()

  // 根据当前模式选择对应的搜索输入
  const searchInput = state.navbar === 'sell' ? state.sellData.modalInput_v3 : state.modalInput_v3

  // 从 buyQuotesList 中找到与 nameValue 匹配的 ramp 项目，并提取其可用支付方式名称
  const availablePaymentNames = new Set()

  if (state.buyQuotesList && state.buyQuotesList.length > 0 && nameValue) {
    state.buyQuotesList.forEach(quote => {
      // 检查 ramp 字段是否与 nameValue 匹配，并且有 rate
      if (quote.rate && quote.ramp?.toLowerCase() === nameValue) {
        if (quote.availablePaymentMethods && Array.isArray(quote.availablePaymentMethods)) {
          quote.availablePaymentMethods.forEach(paymentMethod => {
            if (paymentMethod && paymentMethod.name) {
              availablePaymentNames.add(paymentMethod.name)
            }
          })
        }
      }
    })
  }

  // 先用可用支付方式名称过滤 paymentsList，只保留匹配的支付方式
  let filteredPayments = state.paymentsList.filter(item =>
    item && item.name && availablePaymentNames.has(item.name)
  )

  // 如果没有搜索输入，直接返回过滤后的结果
  if (!searchInput) {
    return filteredPayments
  }

  // 应用搜索过滤
  let searchText = searchInput.toLowerCase()
  return filteredPayments.filter(item =>
    item.name?.toLowerCase().includes(searchText)
  )
})
const filteredSellSupportedCurrencyList = computed(() => {
  let enrichedCurrencyList = state.supportedCurrencyList.map(item => {
    let enrichedItem = { ...item }

    if (state.defaultsAll && typeof state.defaultsAll === 'object') {
      const matchedData = Object.values(state.defaultsAll).find((defaultItem: any) =>
        defaultItem && defaultItem.source === item.code
      )
      if (matchedData && matchedData.amount !== undefined) {
        enrichedItem.amount = matchedData.amount
      }
    }

    if (!enrichedItem.amount && state.recommendedData && typeof state.recommendedData === 'object') {
      if (state.recommendedData.source === item.code && state.recommendedData.amount !== undefined) {
        enrichedItem.amount = state.recommendedData.amount
      }
    }

    return enrichedItem
  })

  if (!state.sellData.modalInput) {
    return enrichedCurrencyList
  }

  let searchText = state.sellData.modalInput.toLowerCase()
  return enrichedCurrencyList.filter(item =>
    item.code?.toLowerCase().includes(searchText) ||
    item.name?.toLowerCase().includes(searchText)
  )
})
// 卖出-您将收到的法币搜索过滤
const filteredSellSupportedFiatList = computed(() => {
  let filteredByDefaults = state.supportedFiatList
  if (state.defaultsAll && typeof state.defaultsAll === 'object') {
    const targetToAmountMap = new Map()
    Object.values(state.defaultsAll).forEach((item: any) => {
      if (item && item.target && item.amount !== undefined) {
        targetToAmountMap.set(item.target, item.amount)
      }
    })
    if (targetToAmountMap.size > 0) {
      filteredByDefaults = state.supportedFiatList
        .filter(item => targetToAmountMap.has(item.code))
        .map(item => ({
          ...item,
          amount: targetToAmountMap.get(item.code)
        }))
    }
  }
  if (!state.sellData.modalInput_v1) {
    return filteredByDefaults
  }

  let searchText = state.sellData.modalInput_v1.toLowerCase()
  return filteredByDefaults.filter(item =>
    item.code?.toLowerCase().includes(searchText) ||
    item.name?.toLowerCase().includes(searchText)
  )
})
// 卖出渠道搜索过滤
const filteredSellQuotesList = computed(() => {
  if (!state.sellData.modalInput_v2) {
    return state.sellQuotesList_merged
  }

  let searchText = state.sellData.modalInput_v2.toLowerCase()
  return state.sellQuotesList_merged.filter(item =>
    item.displayName?.toLowerCase().includes(searchText) ||
    item.ramp?.toLowerCase().includes(searchText) ||
    item.id?.toLowerCase().includes(searchText)
  )
})
// 查询 全部 法币-币种
const getSupportDefaultsAllList = async () => {
  try {
    let params = {
      type: state.navbar,
      country: '',
      country_sub: '',
    }
    const { data } = await getSupportDefaultsAll(params)
    if (data) {
      state.defaultsAll = data.defaults
      state.recommendedData = data.recommended
    }
  } catch (error) { }
}
// 设置默认金额和选中项 买入
const setDefaultAmount = () => {
  if (state.supportedFiatList.length > 0) {
    const currentFiat = state.supportedFiatList[state.youPayIndex]
    if (currentFiat) {
      if (state.hasUserSelectedFiat && state.defaultsAll && typeof state.defaultsAll === 'object') {
        const matchedDefault = Object.values(state.defaultsAll).find((item: any) =>
          item && item.source === currentFiat.code
        )

        if (matchedDefault && matchedDefault.amount !== undefined) {
          state.fiatPayValue = matchedDefault.amount.toString()
        }
      } else if (!state.hasUserSelectedFiat && state.recommendedData && typeof state.recommendedData === 'object') {
        if (state.recommendedData.source === currentFiat.code && state.recommendedData.amount !== undefined) {
          state.fiatPayValue = state.recommendedData.amount.toString()
        }
      }
    }
  }
}
// 设置默认金额和选中项 卖出
const setDefaultAmountForSell = () => {
  if (state.supportedCurrencyList.length > 0) {
    const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
    if (currentCrypto) {
      if (state.hasUserSelectedCrypto && state.defaultsAll && typeof state.defaultsAll === 'object') {
        const matchedDefault = Object.values(state.defaultsAll).find((item: any) =>
          item && item.source === currentCrypto.code
        )

        if (matchedDefault && matchedDefault.amount !== undefined) {
          state.sellData.fiatSellPayValue = matchedDefault.amount.toString()
        }
      } else if (!state.hasUserSelectedCrypto && state.recommendedData && typeof state.recommendedData === 'object') {
        if (state.recommendedData.source === currentCrypto.code && state.recommendedData.amount !== undefined) {
          state.sellData.fiatSellPayValue = state.recommendedData.amount.toString()
        }
      }
    }
  }
}
// 查询支持的法币与币种
const getCoinsSupportedList = async () => {
  let params = {
    type: state.navbar,
    country: '',
    country_sub: ''
  }
  const { data } = await getCoinsSupported(params)
  if (data) {
    state.supportedFiatList = data.fiat
    state.supportedCurrencyList = data.crypto
  }
}
// 匹配和合并 buyQuotesList 和 rampsAllDataList 数据
const mergeQuotesWithRamps = () => {
  if (!state.buyQuotesList.length || !state.rampsAllDataList.length) {
    state.quotesList = []
    // 清空报价数据，避免显示过期的 rate 值
    state.quotesListData = {
      payout: null,
      rate: null,
      selectedFee: null
    }
    return
  }
  const quotesWithRate = state.buyQuotesList.filter(quote => quote.rate)
  const mergedData = quotesWithRate.map(quote => {
    const matchedRamp = state.rampsAllDataList.find(ramp => ramp.id === quote.ramp)
    if (matchedRamp) {
      return {
        displayName: matchedRamp.displayName,
        icon: matchedRamp.icon,
        id: matchedRamp.id,
        recommendations: quote.recommendations,
        rate: quote.rate,
        payout: quote.payout,
        selectedFee: quote.selectedFee,
        ramp: quote.ramp,
        quoteId: quote.quoteId,
        selectedFee: quote.selectedFee,
      }
    }
    return null
  }).filter(Boolean)
  const currentSelectedItem = state.quotesList[state.selectedChannelIndex]
  const sortedData = mergedData.sort((a, b) => {
    const aHasBestPrice = a.recommendations && a.recommendations.includes('BestPrice')
    const bHasBestPrice = b.recommendations && b.recommendations.includes('BestPrice')

    if (aHasBestPrice && !bHasBestPrice) return -1
    if (!aHasBestPrice && bHasBestPrice) return 1
    return 0
  })

  state.quotesList = sortedData

  if (currentSelectedItem && currentSelectedItem.ramp) {
    const newIndex = sortedData.findIndex(item =>
      item.ramp === currentSelectedItem.ramp &&
      item.id === currentSelectedItem.id
    )
    if (newIndex !== -1) {
      state.selectedChannelIndex = newIndex
    } else {
      state.selectedChannelIndex = 0
    }
  } else {
    state.selectedChannelIndex = 0
  }
  updateQuotesListData()
}
// 匹配和合并 sellQuotesList 和 rampsAllDataList 数据
const mergeSellQuotesWithRamps = () => {

  if (!state.sellQuotesList.length || !state.rampsAllDataList.length) {
    state.sellQuotesList_merged = []
    state.sellQuotesListData = {
      payout: null,
      rate: null,
      selectedFee: null
    }
    return
  }
  const quotesWithRate = state.sellQuotesList.filter(quote => quote.rate)

  const mergedData = quotesWithRate.map(quote => {
    const matchedRamp = state.rampsAllDataList.find(ramp => ramp.id === quote.ramp)

    if (matchedRamp) {
      const merged = {
        displayName: matchedRamp.displayName,
        icon: matchedRamp.icon,
        id: matchedRamp.id,
        recommendations: quote.recommendations,
        rate: quote.rate,
        payout: quote.payout,
        selectedFee: quote.selectedFee,
        ramp: quote.ramp,
        quoteId: quote.quoteId,
      }
      return merged
    } else {
      const fallback = {
        displayName: quote.ramp,
        icon: '',
        id: quote.ramp,
        recommendations: quote.recommendations,
        rate: quote.rate,
        payout: quote.payout,
        selectedFee: quote.selectedFee,
        ramp: quote.ramp,
        quoteId: quote.quoteId,
      }
      return fallback
    }
  }).filter(Boolean)
  const currentSelectedItem = state.sellQuotesList_merged[state.selectedChannelIndex]
  const sortedData = mergedData.sort((a, b) => {
    const aHasBestPrice = a.recommendations && a.recommendations.includes('BestPrice')
    const bHasBestPrice = b.recommendations && b.recommendations.includes('BestPrice')

    if (aHasBestPrice && !bHasBestPrice) return -1
    if (!aHasBestPrice && bHasBestPrice) return 1
    return 0
  })

  state.sellQuotesList_merged = sortedData

  if (currentSelectedItem && currentSelectedItem.ramp) {
    const newIndex = sortedData.findIndex(item =>
      item.ramp === currentSelectedItem.ramp &&
      item.id === currentSelectedItem.id
    )
    if (newIndex !== -1) {
      state.selectedChannelIndex = newIndex
    } else {
      state.selectedChannelIndex = 0
    }
  } else {
    state.selectedChannelIndex = 0
  }
  updateSellQuotesListData()
}
// 查询买币报价
const getBuyQuotesList = async () => {
  state.buyQuotesLoading = true
  // 获取当前选中的法币和加密货币的 id
  const currentFiat = state.supportedFiatList[state.youPayIndex]
  const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
  const currentAmount = state.fiatPayValue || state.recommendedData?.amount
  const currentPayment = selectedPayment.value

  let params = {
    fiat: currentFiat?.id,
    crypto: currentCrypto?.id,
    amount: currentAmount,
    payment_method: currentPayment?.paymentTypeId || 'debitcard',
    client_name: '',
    wallet_address: '',
    country: '',
  }

  try {
    if (params.fiat && params.crypto && currentAmount) {
      const { data } = await getBuyQuotes(params)
      if (data) {
        state.buyQuotesList = data
        mergeQuotesWithRamps()
      } else {
        state.buyQuotesList = []
        mergeQuotesWithRamps()
      }
    } else {
      state.buyQuotesList = []
      mergeQuotesWithRamps()
    }
  } finally {
    state.buyQuotesLoading = false
  }
}
// 查询卖币报价
const getSellQuotesList = async () => {
  state.sellQuotesLoading = true

  const currentFiat = state.supportedFiatList[state.youPayIndex]
  const currentCrypto = state.supportedCurrencyList[state.youGetIndex]

  let currentAmount = state.sellData.fiatSellPayValue || sellInputValue.value || state.recommendedData?.amount

  if (!currentAmount || parseFloat(currentAmount) <= 0) {
    currentAmount = '1'
  }

  const currentPayment = selectedPayment.value

  let params = {
    fiat: currentFiat?.id,
    crypto: currentCrypto?.id,
    amount: currentAmount,
    payment_method: currentPayment?.paymentTypeId || 'debitcard',
    client_name: '',
    wallet_address: '',
    country: '',
  }

  try {
    if (params.fiat && params.crypto && currentAmount) {
      const { data } = await getSellQuotes(params)
      if (data) {
        state.sellQuotesList = data
        mergeSellQuotesWithRamps()
      } else {
        state.sellQuotesList = []
        mergeSellQuotesWithRamps()
      }
    } else {
      state.sellQuotesList = []
      mergeSellQuotesWithRamps()
    }
  } finally {
    // 无论成功还是失败都关闭卖出加载状态
    state.sellQuotesLoading = false
  }
}
// 查询所有服务商
const getRampsAllList = async () => {
  let params = {
    type: state.navbar,
  }
  let data = await rampsAllList(params)
  if (data) {
    state.rampsAllDataList = data.data
    if (state.navbar === 'buy') {
      mergeQuotesWithRamps()
    } else {
      mergeSellQuotesWithRamps()
    }
  }
}
// 查询支付方式
const getPaymentType = async () => {
  const currentFiat = state.supportedFiatList[state.youPayIndex]
  const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
  let params = {
    type: state.navbar,
    source: state.navbar === 'buy' ? currentFiat?.id : currentCrypto?.id,
    destination: state.navbar === 'buy' ? currentCrypto?.id : currentFiat?.id,
  }
  let data = await paymentsBySourceList(params)
  if (data) {
    const oldPaymentsList = state.paymentsList
    state.paymentsList = data.data

    if (state.hasSelectedPayment && oldPaymentsList.length > 0) {
      const currentSelectedPayment = oldPaymentsList[state.selectedPaymentIndex]
      if (currentSelectedPayment) {
        const newIndex = state.paymentsList.findIndex(payment =>
          payment.paymentTypeId === currentSelectedPayment.paymentTypeId &&
          payment.name === currentSelectedPayment.name
        )
        if (newIndex !== -1) {
          state.selectedPaymentIndex = newIndex
        } else {
          state.selectedPaymentIndex = 0
          state.hasSelectedPayment = false
        }
      }
    }
  }
}
// 发起交易
const getCreateTrade = async (type: string) => {
  state.transactionLoading = true
  const currentFiat = state.supportedFiatList[state.youPayIndex]
  const currentCrypto = state.supportedCurrencyList[state.youGetIndex]
  const currentAmount = state.fiatPayValue || state.recommendedData?.amount
  const currentPayment = selectedPayment.value

  let params = {
    rate: selectedCurrency.value.rate,
    fee: selectedCurrency.value.selectedFee,
    on_ramp: selectedCurrency.value.ramp,
    source: currentFiat?.id,
    destination: currentCrypto?.id,
    amount: state.navbar === 'buy' ? Number(state.fiatPayValue) : sellInputValue.value,
    type: state.navbar,
    payment_method: currentPayment?.paymentTypeId,
    network: "bitcoin",
    originating_host: "onramper.com",
    wallet: null,
    supported_params: {
      theme: {
        isDark: false,
        themeName: "light-theme",
        primaryColor: "#241D1C",
        secondaryColor: "#FFFFFF",
        primaryTextColor: "#141519",
        secondaryTextColor: "#6B6F80",
        cardColor: "#F6F7F9",
        borderRadius: null
      },
      language: "en",
      partnerData: {
        offrampCashoutRedirectUrl: null,
        redirectUrl: {
          success: null,
          failure: null
        },
      },
      isRecurringPayment: false,
    },
    meta_data: {
      quoteId: selectedCurrency.value.quoteId,
      recurringPayment: {},
      intermediateCurrencyId: "sol",
      walletAddresses: "",
      networkWalletAddresses: ""
    }
  }
  let data = await sendCreateTrade(params)
  if (data.error && data.error.code === '2020') {
    ElMessageBox.confirm(t(''), t('没有实名认证，请实名认证'), {
      distinguishCancelAndClose: true,
      showCancelButton: false,
      showClose: false,
      confirmButtonText: t('去认证'),
      cancelButtonText: t('取消'),
      customStyle: 'text-align: center;'
    }).then(() => {
      router.push(`/${locale.value}/my/kyc/result`)
    })
    return
  }
  if (data) {
    state.createTradeData = data.data
    state.transactionLoading = false
    if (data.data.transactionInformation) {
      state.dialogVisible_v1 = true
    }
    else {
      ElMessageBox.confirm(t(''), t('当前服务商不支持，请重新选择支付渠道！'), {
        distinguishCancelAndClose: true,
        showCancelButton: false,
        showClose: false,
        confirmButtonText: t('确认'),
        cancelButtonText: t('取消'),
        customStyle: 'text-align: center;'
      }).then(() => { })
    }
  }
}
onMounted(async () => {
  try {
    await getSupportDefaultsAllList()
    await getCoinsSupportedList()
    await getRampsAllList()
    nextTick(() => {
      state.isComponentLoaded = true //敬请期待
      if (state.navbar === 'buy') {
        if (state.recommendedData && typeof state.recommendedData === 'object') {
          if (state.recommendedData.source && state.supportedFiatList.length > 0) {
            const matchedFiatIndex = state.supportedFiatList.findIndex(item => item.code === state.recommendedData.source)
            if (matchedFiatIndex !== -1) {
              state.youPayIndex = matchedFiatIndex
            }
          }

          if (state.recommendedData.target && state.supportedCurrencyList.length > 0) {
            const matchedCryptoIndex = state.supportedCurrencyList.findIndex(item => item.code === state.recommendedData.target)
            if (matchedCryptoIndex !== -1) {
              state.youGetIndex = matchedCryptoIndex
            }
          }
        }
        setDefaultAmount()
      } else {
        if (state.recommendedData && typeof state.recommendedData === 'object') {
          if (state.recommendedData.source && state.supportedCurrencyList.length > 0) {
            const matchedCryptoIndex = state.supportedCurrencyList.findIndex(item => item.code === state.recommendedData.source)
            if (matchedCryptoIndex !== -1) {
              state.youGetIndex = matchedCryptoIndex
            }
          }

          if (state.recommendedData.target && state.supportedFiatList.length > 0) {
            const matchedFiatIndex = state.supportedFiatList.findIndex(item => item.code === state.recommendedData.target)
            if (matchedFiatIndex !== -1) {
              state.youPayIndex = matchedFiatIndex
            }
          }
        }
        setDefaultAmountForSell()
      }
      state.isInitializing = false

      nextTick(async () => {
        await getPaymentType()
        if (state.navbar === 'buy') {
          getBuyQuotesList()
        } else {
          getSellQuotesList()
        }
      })
    })
  } catch (error) {
    state.isInitializing = false
  }
})
watch(
  () => [state.recommendedData, state.supportedFiatList, state.supportedCurrencyList, state.navbar],
  ([newRecommendedData, newSupportedFiatList, newSupportedCurrencyList, newNavbar], oldValues) => {
    let shouldCallQuotes = false
    const isNavbarChanged = oldValues && oldValues[3] && newNavbar !== oldValues[3]

    if (state.isInitializing) {
      return
    }

    if (newNavbar === 'buy') {
      if (!state.isHandleNavbarCalling && !state.hasUserSelectedFiat &&
        newRecommendedData &&
        typeof newRecommendedData === 'object' &&
        newRecommendedData.source &&
        newRecommendedData.amount !== undefined &&
        newSupportedFiatList &&
        newSupportedFiatList.length > 0) {

        const matchedFiatIndex = newSupportedFiatList.findIndex(item => item.code === newRecommendedData.source)
        if (matchedFiatIndex !== -1) {
          state.youPayIndex = matchedFiatIndex
          state.fiatPayValue = newRecommendedData.amount.toString()
          shouldCallQuotes = true
        }
      }

      if (!state.isHandleNavbarCalling && !state.hasUserSelectedCrypto &&
        newRecommendedData &&
        typeof newRecommendedData === 'object' &&
        newRecommendedData.target &&
        newSupportedCurrencyList &&
        newSupportedCurrencyList.length > 0) {

        const matchedCryptoIndex = newSupportedCurrencyList.findIndex(item => item.code === newRecommendedData.target)
        if (matchedCryptoIndex !== -1) {
          state.youGetIndex = matchedCryptoIndex
          shouldCallQuotes = true
        }
      }
    } else {
      if (!state.isHandleNavbarCalling && !state.hasUserSelectedCrypto &&
        newRecommendedData &&
        typeof newRecommendedData === 'object' &&
        newRecommendedData.source &&
        newRecommendedData.amount !== undefined &&
        newSupportedCurrencyList &&
        newSupportedCurrencyList.length > 0) {

        const matchedCryptoIndex = newSupportedCurrencyList.findIndex(item => item.code === newRecommendedData.source)
        if (matchedCryptoIndex !== -1) {
          state.youGetIndex = matchedCryptoIndex
          state.sellData.fiatSellPayValue = newRecommendedData.amount.toString()
          shouldCallQuotes = true
        }
      }

      if (!state.isHandleNavbarCalling && !state.hasUserSelectedFiat &&
        newRecommendedData &&
        typeof newRecommendedData === 'object' &&
        newRecommendedData.target &&
        newSupportedFiatList &&
        newSupportedFiatList.length > 0) {

        const matchedFiatIndex = newSupportedFiatList.findIndex(item => item.code === newRecommendedData.target)
        if (matchedFiatIndex !== -1) {
          state.youPayIndex = matchedFiatIndex
          shouldCallQuotes = true
        }
      }
    }

    if (shouldCallQuotes) {
      nextTick(async () => {
        await getPaymentType()
        if (newNavbar === 'buy') {
          getBuyQuotesList()
        } else {
          getSellQuotesList()
        }
      })
    }
  },
  { deep: true, immediate: true }
)

watch(() => state.selectedChannelIndex, (newIndex, oldIndex) => {
  if (newIndex !== oldIndex && state.quotesList && state.quotesList.length > 0) {
    updateQuotesListData()
  }
})

</script>
<style lang='scss' scoped>
@import url('@/assets/style/transaction.scss');
</style>