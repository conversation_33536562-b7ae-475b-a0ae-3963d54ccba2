<template>
  <Header modeType="transaction" />
  <div v-if="state.isMobile">
    <IndexMobile :hot-coins-list="state.hotCoinsList" :supported-currencies-list="supportedCurrenciesList"/>
  </div>
  <div v-else>
    <IndexPc :hot-coins-list="state.hotCoinsList" :supported-currencies-list="supportedCurrenciesList"/>
  </div>
  <Footer />
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import IndexPc from './indexPc.vue'
import IndexMobile from './indexMobile.vue'
import { getHotCoins } from '~/api/public.ts'
const route = useRoute();
const router = useRouter();
const colorMode = useColorMode()
const { locale, t } = useI18n()
const state = reactive({
  screenWidth:0,
  isMobile:false,
})
const supportedCurrenciesList = ref([
  {
    img: 'https://res.ktx.com/appimg/1754640610033.png',
    label: 'EUR',
    value: 'Euro',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640642053.png',
    label: 'USD',
    value: 'US Dollar',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640664091.png ',
    label: 'CNY',
    value: 'Chinese Yuan',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640683965.png',
    label: 'JPY',
    value: 'Japanese Yen',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640700917.png ',
    label: 'AUD',
    value: 'Australian Dollar',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640716886.png',
    label: 'CHF',
    value: 'Swiss Franc',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640735581.png',
    label: 'SGD',
    value: 'Singapore Dollar',
  },
  {
    img: 'https://res.ktx.com/appimg/1754640754300.png',
    label: 'CAD',
    value: 'Canadian Dollar',
  },
])
const updateScreenSize = () => {
  if (process.client) {
    state.screenWidth = window.innerWidth
    state.isMobile = state.screenWidth <= 768
  }
}
// 热门加密货币
const getHotCoinsList = async () => {
  let params = {
    type: state.navbar,
  }
  const { data } = await getHotCoins(params)
  if (data) {
    state.hotCoinsList = data
  }
}
onMounted(async () => {
  getHotCoinsList()
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
})
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', updateScreenSize)
  }
})

</script>
<style lang='scss' scoped>
@import url('@/assets/style/transaction.scss');
</style>

<style lang="scss">
.dialog-box {
  .el-dialog .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog .el-dialog__body {
    padding: 0 15px 0 3px;
  }

  .el-dialog {
    border-radius: 20px;
  }

  .el-dialog__header {
    margin-top: 5px !important;
  }
}
</style>
