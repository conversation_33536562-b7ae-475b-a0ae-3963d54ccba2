<template>
  <div class="transaction-container-mobile">
    <div class="transaction-title-box">
      <div class="order-box">
        <img class="title-icon" v-if="colorMode.preference === 'light'"
          src="~/assets/images/transaction/order-icon.png">
        <img class="title-icon" v-if="colorMode.preference === 'dark'"
          src="~/assets/images/transaction/order-icon-dark.png">
        <NuxtLink :to="`/${locale}/transaction/orderList`">
          <div class="title">
            <div>{{ $t('订单') }}</div>
            <!-- 提示消息 -->
            <!-- <div class="tips-box" v-if="true"></div> -->
          </div>
        </NuxtLink>
      </div>
    </div>
    <div class="transaction-content-mobile">
      <div class="transaction-content-left">
        <div class="support-box-mobile">
          <div class="support-title">{{ $t('热门法币') }}</div>
          <div class="supported-currencies-box-mobile">
            <div class="item-supported-currencies-mobile" v-for="(item, index) of supportedCurrenciesList" :key="index">
              <img :src="item.img" class="item-currencies-icon">
              <div class="item-currencies-label-mobile">{{ item.label }}</div>
              <div class="item-currencies-value-mobile">{{ item.value }}</div>
            </div>
          </div>
        </div>
        <div class="support-box-mobile">
          <div class="support-title">{{ $t('热门加密货币') }}</div>
          <div class="supported-currencies-box-mobile">
            <div class="item-supported-currencies-mobile" v-for="(item, index) of hotCoinsList" :key="index">
              <img :src="`${imgDmain}${item.icon_url}`" class="item-currencies-icon">
              <div class="item-currencies-label">{{ item.symbol }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="transaction-content-right">
        <div class="left-title-mobile-box">
          <div class="left-title-mobile">{{ $t('使用第三方支付买卖加密货币现在就开始吧') }}</div>
          <div class="left-info-mobile">{{ $t('现在开始KTX为您提供多币种多渠道的买卖方式') }}</div>
          <div class="left-info-mobile">{{ $t('您可以使用我们支持的第三方支付在KTX上方便的买卖USDT或其他加密货币') }}</div>
        </div>
        <TradingComponentMobile />
      </div>
    </div>
    <div class="footer-content-mobile">
      <div class="footer-content-title-mobile">{{ $t('使用第三方支付买卖加密货币须知') }}</div>
      <div class="footer-content-box-mobile">
        <div>
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v1.png"
            class="footer-content-icon-mobile">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v1.png"
            class="footer-content-icon-mobile">
          <div class="footer-content-box-title-mobile">{{ $t('完成 KTX 实名认证') }}</div>
        </div>
        <div>
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v2.png"
            class="footer-content-icon-mobile">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v2.png"
            class="footer-content-icon-mobile">
          <div class="footer-content-box-title-mobile">{{ $t('创建订单') }}</div>
          <div class="footer-content-box-info-mobile">{{ $t('* 输入将要买卖的金额，选择支付方式，并创建订单。') }}</div>
        </div>
        <div>
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v3.png"
            class="footer-content-icon-mobile">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v3.png"
            class="footer-content-icon-mobile">
          <div class="footer-content-box-title-mobile">{{ $t('完成买入或卖出加密货币') }}</div>
          <div class="footer-content-box-info-mobile">{{ $t('* 加密货币将存入您的KTX钱包账户，法币将汇入您使用的收款账户。') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { imgDmain } from '~/config'
import 'vant/lib/index.css'
import TradingComponentMobile from './components/tradingComponentMobile.vue'
const route = useRoute();
const router = useRouter();
const colorMode = useColorMode()
const { locale, t } = useI18n()
const state = reactive({})
const props = defineProps({
  supportedCurrenciesList: {
    type: Array,
    default: () => []
  },
  hotCoinsList: {
    type: Array,
    default: () => []
  },
})
</script>
<style lang='scss' scoped>
@import url('@/assets/style/transaction.scss');
</style>
<style lang="scss">
.van-action-sheet-box {
  .van-action-sheet {
    .dark & {
      background-color: #181A1F !important;
    }
  }

  .van-action-sheet__header {
    .dark & {
      color: #fff;
    }
  }
}

.dialog-box {
  .van-action-sheet {
    .dark & {
      background-color: #181A1F !important;
    }
  }

  .van-action-sheet__header {
    .dark & {
      color: #fff;
    }
  }
}

.van-action-sheet-box {
  .van-search {
    background: transparent !important;
  }

  .van-search__content {
    .dark & {
      background: #25282F !important;
      border-radius: 4px !important;
    }
  }

  .van-field__control {
    .dark & {
      color: #fff !important;
    }
  }
}
</style>