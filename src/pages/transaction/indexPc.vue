<template>
  <div class="transaction-container">
    <div class="transaction-title-box">
      <div class="order-box">
        <img class="title-icon" v-if="colorMode.preference === 'light'"
          src="~/assets/images/transaction/order-icon.png">
        <img class="title-icon" v-if="colorMode.preference === 'dark'"
          src="~/assets/images/transaction/order-icon-dark.png">
        <NuxtLink :to="`/${locale}/transaction/orderList`">
          <div class="title">
            <div>{{ $t('订单') }}</div>
            <!-- 提示消息 -->
            <!-- <div class="tips-box" v-if="true"></div> -->
          </div>
        </NuxtLink>
      </div>
    </div>
    <div class="transaction-content">
      <div class="transaction-content-left">
        <div class="left-title-box">
          <div class="left-title">{{ $t('使用第三方支付买卖加密货币现在就开始吧') }}</div>
          <div class="left-info">{{ $t('现在开始KTX为您提供多币种多渠道的买卖方式') }}</div>
          <div class="left-info">{{ $t('您可以使用我们支持的第三方支付在KTX上方便的买卖USDT或其他加密货币') }}</div>
        </div>
        <div class="support-box">
          <div class="support-title">{{ $t('热门法币') }}</div>
          <div class="supported-currencies-box">
            <div class="item-supported-currencies" v-for="(item, index) of supportedCurrenciesList" :key="index">
              <img :src="item.img" class="item-currencies-icon">
              <div class="item-currencies-label">{{ item.label }}</div>
              <div class="item-currencies-value">{{ item.value }}</div>
            </div>
          </div>
        </div>
        <div class="support-box">
          <div class="support-title">{{ $t('热门加密货币') }}</div>
          <div class="supported-currencies-box">
            <div class="item-supported-currencies" v-for="(item, index) of hotCoinsList" :key="index">
              <img :src="`${imgDmain}${item.icon_url}`" class="item-currencies-icon">
              <div class="item-currencies-label">{{ item.symbol }}</div>
            </div>
          </div>
        </div>
      </div>
      <TradingComponent />
    </div>
    <div class="footer-content">
      <div class="footer-content-title">{{ $t('使用第三方支付买卖加密货币须知') }}</div>
      <div class="footer-content-box">
        <div>
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v1.png"
            class="footer-content-icon">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v1.png"
            class="footer-content-icon">
          <div class="footer-content-box-title">{{ $t('完成 KTX 实名认证') }}</div>
        </div>
        <div class="footer-content-box-item">
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v2.png"
            class="footer-content-icon">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v2.png"
            class="footer-content-icon">
          <div class="footer-content-box-title">{{ $t('创建订单') }}</div>
          <div class="footer-content-box-info">{{ $t('* 输入将要买卖的金额，选择支付方式，并创建订单。') }}</div>
        </div>
        <div class="footer-content-box-item">
          <img v-if="colorMode.preference === 'dark'" src="~/assets/images/transaction/footer-icon-pc-v3.png"
            class="footer-content-icon">
          <img v-if="colorMode.preference === 'light'" src="~/assets/images/transaction/footer-icon-light-v3.png"
            class="footer-content-icon">
          <div class="footer-content-box-title">{{ $t('完成买入或卖出加密货币') }}</div>
          <div class="footer-content-box-info">{{ $t('* 加密货币将存入您的KTX钱包账户，法币将汇入您使用的收款账户。') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { CaretBottom, CloseBold } from '@element-plus/icons-vue'
import { imgDmain } from '~/config' 
import TradingComponent from './components/tradingComponentPc.vue'
const route = useRoute();
const router = useRouter();
const colorMode = useColorMode()
const { locale, t } = useI18n()
const state = reactive({})
const props = defineProps({
  supportedCurrenciesList: {
    type: Array,
    default: () => []
  },
  hotCoinsList: {
    type: Array,
    default: () => []
  },
})
</script>
<style lang='scss' scoped>
@import url('@/assets/style/transaction.scss');
</style>

<style lang="scss">
.dialog-box {
  .el-dialog .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog .el-dialog__body {
    padding: 0 15px 0 3px;
  }

  .el-dialog {
    border-radius: 20px;
  }

  .el-dialog__header {
    margin-top: 5px !important;
  }
}

.modal-box-content-input {
  .el-input {
    border-radius: 10px !important;
  }
}

.van-skeleton-paragraph-box {
  .van-skeleton-paragraph {
    width: 93% !important;
    margin: 0 auto;
    border-radius: 20px;
    height: 92px;
  }
}

.el-skeleton-paragraph-box {
  .el-skeleton__item {
    display: flex;
    width: 428px !important;
    margin: 16px auto 0 auto;
    border-radius: 20px;
    height: 92px;
    background-color: #25282F;
  }
}

.skeleton-box {
  .el-skeleton__item {
    display: flex;
    margin: 16px auto 0 auto;
    border-radius: 20px;
    width: 428px;
    height: 92px;
  }
}
.submit-btn-disabled.el-button.el-button--primary,
.submit-btn-disabled .el-button.el-button--primary {
  background: #f5f5f5 !important;
  .dark & {
    background: #25282F !important;
  }
}
</style>
