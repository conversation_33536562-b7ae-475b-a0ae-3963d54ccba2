<template>
  <Header modeType="orderList" />
  <div class="order-list-container">
    <div class="navigation-box">
      <div class="navigation-title" @click="goPage">{{ $t('三方支付买币') }}</div>
      <el-icon class="navigation-icon">
        <ArrowRightBold />
      </el-icon>
      <div class="navigation-record">{{ $t('历史记录') }}</div>
    </div>
    <div class="order-list-content" v-if="!isMobile">
      <BoxLoading v-show="state.loading" />
      <el-table :data="state.dataList" style="width: 100%">
        <el-table-column prop="transaction_id" :label="$t('交易编号')" align="center" width="260px"/>
        <el-table-column prop="type" :label="$t('交易类型')" align="center"/>
        <el-table-column prop="destination" :label="$t('交易币种')" align="center">
          <template #default="scope">
            <div>{{ scope.row.destination?.toUpperCase() }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="rate" :label="$t('法币价格')" align="center"/>
        <el-table-column prop="amount" :label="$t('交易数量')" align="center"/>
        <el-table-column prop="trade_sum" :label="$t('交易金额')" align="center">
          <template #default="scope">
            <div>{{ scope.row.trade_sum }} {{ scope.row.source?.toUpperCase() }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="ramp" :label="$t('服务商')" align="center"/>
        <el-table-column prop="payment_method" :label="$t('支付方式')" align="center"/>
        <el-table-column prop="status" :label="$t('操作')" align="center">
          <template #default="scope">
            <div class="operate-text" v-if="scope.row.status === 'in_progress'">{{ $t('处理中') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'completed'">{{ $t('已完成') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'paid'">{{ $t('已付款') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'pending'">{{ $t('进行中') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'new'">{{ $t('未付款') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'failed'">{{ $t('失败') }}</div>
            <div class="operate-text" v-if="scope.row.status === 'canceled'">{{ $t('已取消') }}</div>
          </template>
        </el-table-column>
      </el-table>
      <ElPagination v-if="state.dataList.length > 0" class="flex-box space-center  mg-t24" background layout="prev, pager, next" center
        :current-page="state.page" :page-size="state.size" :total="state.tableTotal"
        @current-change="handleCurrentChange">
      </ElPagination>
    </div>
    <div class="order-list-content-mobile" v-if="isMobile">
      <BoxLoading v-show="state.loadingMobile && !state.initialLoaded" />
      <MobileTable 
        :data-list="state.dataListMobile"
        :loading-mobile="state.loadingMobile"
        :finished="state.finished"
        :initial-loaded="state.initialLoaded"
        :columns="columns"
        :loading-text="$t('加载中...')"
        :empty-text="$t('- 没有更多了哦 -')"
        @load="onLoad"
        @update:loading-mobile="(value) => state.loadingMobile = value">
        <template #data="{ column, record }">
          <template v-if="column.dataIndex === 'operate'">
            <div class="operate-text" v-if="record.status === 'in_progress'">{{ $t('处理中') }}</div>
            <div class="operate-text" v-if="record.status === 'completed'">{{ $t('已完成') }}</div>
            <div class="operate-text" v-if="record.status === 'paid'">{{ $t('已付款') }}</div>
            <div class="operate-text" v-if="record.status === 'pending'">{{ $t('进行中') }}</div>
            <div class="operate-text" v-if="record.status === 'new'">{{ $t('未付款') }}</div>
            <div class="operate-text" v-if="record.status === 'failed'">{{ $t('失败') }}</div>
            <div class="operate-text" v-if="record.status === 'canceled'">{{ $t('已取消') }}</div>
          </template>
          <template v-if="column.dataIndex === 'trade_sum'">
            <div>{{ record.trade_sum }} {{ record.source?.toUpperCase() }}</div>
          </template>
        </template>
      </MobileTable>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowRightBold } from '@element-plus/icons-vue'
import { ElPagination } from 'element-plus'
import MobileTable from '@/components/mobileList/index.vue'
import { orderHisList } from '~/api/public.ts'
const { locale, t } = useI18n()
const route = useRoute();
const router = useRouter();
const state = reactive({
  page: 1,
  size: 20,
  tableTotal: 0,
  loading: false,
  loadingMobile: false,
  finished: false,
  initialLoaded: false,
  dataList: [],
  dataListMobile: [],
  pageMobile: 1,
  sizeMobile: 20,
})
const columns = computed(() => {
  return [
    { dataIndex: "transaction_id", title: t("交易编号")},
    { dataIndex: "type", title: t("交易类型") },
    { dataIndex: "destination", title: t("交易币种") },
    { dataIndex: "rate", title: t("法币价格") },
    { dataIndex: "amount", title: t("交易数量") },
    { dataIndex: "trade_sum", title: t("交易金额") },
    { dataIndex: "ramp", title: t("服务商") },
    { dataIndex: "payment_method", title: t("支付方式") },
    { dataIndex: "operate", title: t("操作") },
  ];
});
const goPage = () => {
  router.go(-1)
}
const onLoad = async () => {
  if (state.finished) return;

  state.loadingMobile = true;
  await getDataListMobile();
};
const handleCurrentChange = async (val: number) => {
  state.page = val
  await getDataList()
}

const getDataListMobile = async () => {
  let params = {
    page: state.pageMobile,
    size: state.sizeMobile,
  }
  try {
    let data = await orderHisList(params)
    let newData = []
    let total = 0

    if(data && data.result) {
      newData = data.result.rows || []
      total = data.result.count || 0
    } else if(data && data.data) {
      newData = data.data.rows || []
      total = data.data.total || data.data.count || 0
    }

    // 移动端使用累加模式
    if (state.pageMobile === 1) {
      state.dataListMobile = newData
    } else {
      state.dataListMobile = [...state.dataListMobile, ...newData]
    }

    // 判断是否还有更多数据
    const totalLoaded = state.dataListMobile.length
    state.finished = totalLoaded >= total || newData.length < state.sizeMobile

    // 如果还有数据，准备下一页
    if (!state.finished) {
      state.pageMobile++
    }

  } catch (error) {
    state.finished = true
  } finally {
    state.loadingMobile = false
  }
}
const getDataList = async () => {
  state.loading = true
  let params = {
    page: state.page,
    size: state.size,
  }
  try {
    let data = await orderHisList(params)
    if(data && data.result) {
      state.dataList = data.result.rows || []
      state.tableTotal = data.result.count || 0
    } else if(data && data.data) {
      state.dataList = data.data.rows || []
      state.tableTotal = data.data.total || data.data.count || 0
    } else {
      state.dataList = []
      state.tableTotal = 0
    }
  } catch (error) {
    state.dataList = []
    state.tableTotal = 0
  } finally {
    state.loading = false
  }
}
const screenWidth = ref(0)
const isMobile = ref(false)
const updateScreenSize = async () => {
  if (process.client) {
    const oldIsMobile = isMobile.value
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value <= 768

    // 检测设备类型是否发生变化
    if (oldIsMobile !== isMobile.value && state.initialLoaded) {
      if (isMobile.value) {
        // 切换到移动端
        if (state.dataListMobile.length === 0) {
          state.pageMobile = 1
          state.finished = false
          await getDataListMobile()
        }
      } else {
        // 切换到PC端
        if (state.dataList.length === 0) {
          await getDataList()
        }
      }
    }
  }
}
onMounted(async () => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)

  // 根据设备类型加载对应数据
  if (isMobile.value) {
    state.pageMobile = 1
    state.dataListMobile = []
    state.finished = false
    await getDataListMobile()
  } else {
    await getDataList()
  }

  state.initialLoaded = true;
})
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', updateScreenSize)
  }
})

</script>
<style lang='scss' scoped>
.order-list-container {
  width: 100%;
  min-height: calc(100vh - 74px);
  margin-top: 12px;
}

.navigation-box {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.navigation-icon {
  color: #9497A0;
  margin-left: 5px;
  margin-right: 5px;
}

.navigation-title {
  font-size: 16px;
  color: #9497A0;
  font-weight: 600;
  cursor: pointer;
}

.navigation-record {
  font-size: 16px;
  color: #414655;
  font-weight: 600;
  .dark & {
      color: #fff;
    }
}

.order-list-content {
  margin-top: 32px;
}

.order-list-content-mobile {
  margin-top: 14px;
}

.operate-text {
  color: #F0B90B;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

:deep(.el-table) {
  border: none !important;
}

:deep(.el-table td),
:deep(.el-table th) {
  border: none !important;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table--border::after) {
  display: none;
}


</style>