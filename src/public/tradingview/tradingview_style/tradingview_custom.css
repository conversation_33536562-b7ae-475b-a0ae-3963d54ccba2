:root:not(.theme-dark) {
    --tv-color-platform-background: #ffffff;
    --tv-color-pane-background: #ffffff;
    --tv-color-toolbar-button-background-hover: #f8f9fa;
    --tv-color-toolbar-button-background-expanded: #f8f9fa;
    --tv-color-toolbar-button-background-active: #f8f9fa;
    --tv-color-toolbar-button-background-active-hover: #f8f9fa;
    --tv-color-toolbar-button-text: #9497a0;
    --tv-color-toolbar-button-text-hover: #f0b90b;
    --tv-color-toolbar-button-text-active: #f0b90b;
    --tv-color-toolbar-button-text-active-hover: #f0b90b;
    --tv-color-item-active-text: #f0b90b;
    --tv-color-toolbar-toggle-button-background-active: #f0b90b;
    --tv-color-toolbar-toggle-button-background-active-hover: #f0b90b;
    --tv-color-toolbar-divider-background: rgb(251, 223, 244);
    --tv-color-toolbar-save-layout-loader: rgb(106, 109, 120);

    --tv-color-popup-background: rgb(241, 188, 225);
    --tv-color-popup-element-text: rgb(136, 24, 79);
    --tv-color-popup-element-text-hover: rgb(74, 20, 140);
    --tv-color-popup-element-background-hover: rgb(244, 143, 177);
    --tv-color-popup-element-divider-background: rgb(251, 223, 244);
    --tv-color-popup-element-secondary-text: rgb(74, 20, 140);
    --tv-color-popup-element-hint-text: rgb(74, 20, 140);
    --tv-color-popup-element-text-active:#f0b90b;
    --tv-color-popup-element-background-active: #f0b90b;
    --tv-color-popup-element-toolbox-text: rgb(136, 24, 79);
    --tv-color-popup-element-toolbox-text-hover: #f0b90b;
    --tv-color-popup-element-toolbox-text-active-hover: #f0b90b;
    --tv-color-popup-element-toolbox-background-hover: rgb(222, 89, 132);
    --tv-color-popup-element-toolbox-background-active-hover: magenta;
}

.theme-dark:root {
    --tv-color-platform-background: #181a1f;
    --tv-color-pane-background: #181a1f;
    --tv-color-toolbar-button-background-hover: #303134;
    --tv-color-toolbar-button-background-expanded:#303134;
    --tv-color-toolbar-button-background-active: #303134;
    --tv-color-toolbar-button-background-active-hover: #303134;
    --tv-color-toolbar-button-text: #9497a0;
    --tv-color-toolbar-button-text-hover: #f0b90b;
    --tv-color-toolbar-button-text-active: #f0b90b;
    --tv-color-toolbar-button-text-active-hover: #f0b90b;
    --tv-color-item-active-text: #f0b90b;
    --tv-color-toolbar-toggle-button-background-active: #f0b90b;
    --tv-color-toolbar-toggle-button-background-active-hover: #f0b90b;
    --tv-color-toolbar-divider-background: rgb(251, 223, 244);
    --tv-color-toolbar-save-layout-loader: rgb(134, 137, 147);

    --tv-color-popup-background: rgb(241, 188, 225);
    --tv-color-popup-element-text: rgb(136, 24, 79);
    --tv-color-popup-element-text-hover: rgb(74, 20, 140);
    --tv-color-popup-element-background-hover: rgb(244, 143, 177);
    --tv-color-popup-element-divider-background: rgb(251, 223, 244);
    --tv-color-popup-element-secondary-text: rgb(74, 20, 140);
    --tv-color-popup-element-hint-text: rgb(74, 20, 140);
    --tv-color-popup-element-text-active: rgb(6, 6, 255);
    --tv-color-popup-element-background-active: red;
    --tv-color-popup-element-toolbox-text: rgb(136, 24, 79);
    --tv-color-popup-element-toolbox-text-hover: rgb(74, 20, 140);
    --tv-color-popup-element-toolbox-text-active-hover: rgb(74, 20, 140);
    --tv-color-popup-element-toolbox-background-hover: rgb(222, 89, 132);
    --tv-color-popup-element-toolbox-background-active-hover: magenta;
}
.drawingToolbar-2_so5thS{
  background-color: transparent !important
}
.chart-page .layout__area--top [class*="isOpened-"],
.chart-page .layout__area--left [class*="isActive-"] [class*="bg-"] {
  color: #f0b90b !important;
  border-radius: 4px;
}
html.theme-dark .button-263WXsg--.isActive-2mI1-NUL- .icon-1Y-3MM9F- {
  color: #f0b90b !important;
}
html.theme-dark .button-263WXsg-- .icon-1Y-3MM9F-.hovered--MYZioUu-,
html.feature-no-touch.theme-dark .button-263WXsg-- .icon-1Y-3MM9F-:hover,
html.feature-no-touch.theme-dark .button-263WXsg-- .icon-1Y-3MM9F-:active,
html.feature-touch.theme-dark .button-263WXsg-- .icon-1Y-3MM9F-:active {
  color: #f0b90b !important;
}
.chart-page .layout__area--top:hover[class*="isOpened-"],
.chart-page .layout__area--left [class*="isActive-"]:hover[class*="bg-"] {
  color: #f0b90b !important;
}
.chart-page .button-5-QHyx-s:hover{
  color: #f0b90b !important;
}
.toggleButton-13QgefpG- {
  bottom: 50%;
}
.chart-page {
  background-color: transparent !important;
}
.chart-page .chart-container {
  border-color: transparent;
  background-color: transparent;
}
.chart-page .layout__area--top [class^="group-"],
.chart-page .layout__area--top [class^="fill-"] {
  background: transparent;
}
.chart-page .apply-common-tooltip {
  color: #656c77;
  cursor: pointer;
}
.chart-page .apply-common-tooltip:hover {
  color: #f0b90b !important;
}
.chart-page .apply-common-tooltip [class^="icon-"] {
  color: inherit;
}
.chart-page .apply-common-tooltip [class^="icon-"]:hover {
  color: #f0b90b;
}
.layout__area--center {
  background-color: transparent;
}
html {
  width: 100%;
  height: 100%;
}
html body .common-tooltip-wrapper .common-tooltip-body {
  border-color: rgba(0, 0, 0, 0.03);
}
html body .common-tooltip-wrapper.below:before {
  border-bottom-color: rgba(0, 0, 0, 0.03);
}
html body .common-tooltip-wrapper.above:before {
  border-top-color: rgba(0, 0, 0, 0.03);
}
html body .common-tooltip-wrapper.otr:before {
  border-right-color: rgba(0, 0, 0, 0.03);
}
html body .common-tooltip-wrapper.otl:before {
  border-left-color: rgba(0, 0, 0, 0.03);
}
html.feature-no-touch .chart-controls-bar-buttons a:not(.disabled):hover {
  background-color: rgba(137, 148, 163, 0.16);
}
.chart-container .chart-controls-bar {
  background: transparent;
}
.layout__area--left .drawingToolbar-U3_QXRof- {
  background: transparent;
}
.main-properties:not(.main-properties-aftertabs) {
  height: 200px !important;
  overflow: auto;
  width: 100%;
  box-sizing: border-box;
}
._tv-dialog-content {
  width: calc(100vw - 32px);
  box-sizing: border-box;
}
._tv-dialog._tv-dialog-nonmodal.ui-draggable {
  width: calc(100vw - 32px) !important;
  min-width: unset !important;
}
.group-2JyOhh7Z- {
  background: transparent !important;
}
.drawingToolbar-U3_QXRof- {
  background-color: transparent !important;
}

html {
  background-color: transparent !important;
}
html .chart-page {
  background-color: transparent !important;
}
html .chart-page .chart-container-border {
  background-color: transparent !important;
}
html .js-rootresizer__contents {
  background: transparent !important;
}
html .chart-page.on-widget {
  background: transparent !important;
}

/* 自定义 TradingView Logo */
.tv-logo {
  background-image: url("/images/logo.png") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: auto !important;
  height: auto !important;
}

/* 隐藏 TradingView 默认 logo 文字 */
.tv-logo__text,
.tv-logo__link {
  display: none !important;
}

/* 针对不同的 logo 容器类名 */
[class*="logo"] img,
[class*="Logo"] img {
  content: url("/images/logo.png") !important;
}

/* 如果有其他 logo 相关的选择器 */
.chart-container [class*="logo"],
.chart-container [class*="Logo"] {
  background-image: url("/images/logo.png") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}
