import { defineStore } from 'pinia'
import { getErrors, listMainCoins, getPairList, getCurrencyRateData } from '~/api/public'
import { assetsByCoin, assetByCoinList } from '~/api/tf'
import { getTradesList, getKlinesApi } from '~/api/order'
import { getPairSetting, getLandingPairsApi, getAddDownLink } from '~/api/public'
import { useI18n } from "vue-i18n"
import { cookies } from '~/utils/cookies'
import { marketSocket, tickerSocket } from '~/utils'
import SocketClass from '~/utils/socket.ts'
import { nextTick } from 'vue'
import { usePriceUpdateOptimizer } from '~/composables/usePriceUpdateOptimizer'

let localPairInfo = {}
if (process.client) {
  localPairInfo = JSON.parse(localStorage.getItem('pairInfo') || '{}')
}
export const commonStore = defineStore('publicCommon', () => {
  const errorMessages = ref({})
  const coinList = ref([])
  const currencyRate = ref({})
  const exchangeRate = ref({
    rate: 'USD',
    symbol: '$'
  })
  const pair = ref('')
  const depthsStore = ref({})
  const coinAssetListObj = ref({})
  const CoinAssetObj = ref({})
  const mainAssetObj = ref({})
  const tradeAssetObj = ref({})
  const posMapObj = ref({})
  const klineList = ref([])
  const klineTicker = ref({
    currentPair: '',
    currentPeriod: '',
    open: 0,
    high: 0,
    low: 0,
    close: 0,
    volume: 0,
    time: 0,
    _lastUpdate: 0
  })
  const allPairList = ref([])
  const dealsObj = ref({})
  const allAsset = ref({})
  const marketsObj = ref({})
  const COLLATERALSymbol = ref({})
  const assetAllCoinMap = ref({})
  const ticker = ref({})
  const isChangeOrder = ref(true)
  const isChangeFutureOrder = ref(false)
  const isChangePosition = ref(false)
  const orderChangeObj = ref({})
  const isHideAssets = ref(false)
  const pairInfo = ref(localPairInfo)
  const priceScale = ref(0)
  const quantityScale = ref(0)
  const isPairDetail = ref(false)
  const landingPairs = ref({})
  const tradeArr = ref([])
  const downLoadInfo = ref({})
  const currentLang = useI18n().locale.value

  // 价格更新优化器
  const priceOptimizer = usePriceUpdateOptimizer({
    updateThreshold: 0.0001,
    batchUpdateDelay: 16,
    enableSmartThrottling: true,
    maxUpdateFrequency: 60
  })
  const changePair = (p) => {
    const router = useRouter()
    const nuxtApp = useNuxtApp()
    const lang = nuxtApp.$i18n.locale.value
    if (p.includes('_SWAP')) {
      pair.value = p
      nextTick(() => {
        window.history.replaceState({}, null, `/${lang}/future/${p}`)
      })
      router.currentRoute.value.params.pair = p
    } else {
      router.push(`/${lang}/exchange/${p}`)
    }
  }
  const getDownLoadInfo = async() => {
    const { data } = await getAddDownLink()
    if (data) {
      const arr = data.filter((item) => {
        return item.device_type * 1 === 3 || item.device_type * 1 === 4 // 3是IOS 4是安卓
      })
      let obj = {}
      arr.forEach((item) => {
        obj[item.device_type * 1] = item
      })
      downLoadInfo.value = obj
    }
  }
  const getLandingPairs = async() => {
    const { data } = await getLandingPairsApi()
    if (data) {
      let obj = {}
      data.forEach((item) => {
        obj[item.pair] = item
      })
      landingPairs.value = obj
    }
  }
  const setHideAssets = () => {
    isHideAssets.value = !isHideAssets.value
  }
  const getMessageError = async(lang) => {
    const { data } = await getErrors({
      lang: lang ? lang : currentLang
    })
    if (data) {
      errorMessages.value = data
    } else {
      try {
        let times = 0
        const timer = setInterval(async () => {
          if (times > 4) {
            clearInterval(timer)
            return
          }
          times++
          const {
            result: intervalData
          } = await getErrors({
            lang: currentLang
          })

          if (intervalData) {
            errorMessages.value = result
            clearInterval(timer)
          }
        }, 1000)
      } catch (err) {}
    }
  }
  const getCurrencyRate = async() => {
    const { data } = await getCurrencyRateData()
    if (data) {
      currencyRate.value = data
    } else {
      try {
        window.isDispatchCurrencyRate = false
      } catch (err) {}
    }
  }
  const switchExchangeRate = (data) => {
    exchangeRate.value = data
  }
  const getCoinList = async() => {
    const { data } = await listMainCoins()
    if (data) {
      coinList.value = data
    }
  }
  const getAllPairList = async() => {
    const { data } = await getPairList()
    if (data) {
      allPairList.value = data.spot.concat(data.contract)
    }
  }
  const getAssetByCoinList = async() => {
    const { data } = await assetByCoinList()
    if (data) {
      assetAllCoinMap.value = data
    }
  }
  const getAssetsByCoin = async() => {
    const { data } = await assetsByCoin()
    if (data) {
      allAsset.value = {
        'all': data.eq,
        'main': data.mainEq,
        'trade': data.tradeEq,
        'unprofit': data.posmap['USDT'].unprofit,
        'assetmap': data.assetmap
      }
      if (data.arr.length > 0) {
        data.arr.forEach((item) => {
          CoinAssetObj.value[item.asset] = item
        })
      } else {
        CoinAssetObj.value = {}
      }
      const mainArray = data.main
      if (mainArray.length > 0) {
        mainAssetObj.value = {}
        mainArray.forEach((item) => {
          mainAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['main'] = mainArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        mainAssetObj.value = {}
        coinAssetListObj.value['main'] = [
          {
            icon_url: data.assetmap['USDT'].icon_url,
            asset: 'USDT',
            maxTransferOut: 0,
            asset_weight: 0,
            balance: 0,
            balanceUnify: 0,
            c: false,
            collateral: true,
            discount: 1,
            discountForFee: 1,
            discountForMargin: 1,
            eqbtc: 0,
            eqcny: 0,
            equsdt: 0,
            holds: 0,
            total: 0,
            usdtunify: 0,
            withdrawable: 0
          }
        ]
      }
      const tradeArray = data.trade
      if (tradeArray.length > 0) {
        tradeAssetObj.value = {}
        tradeArray.forEach((item) => {
          tradeAssetObj.value[item.asset] = item
        })
        coinAssetListObj.value['trade'] = tradeArray.map((item) => {
          item.icon_url = data.assetmap && data.assetmap[item.asset] && data.assetmap[item.asset].icon_url
          return item
        })
      } else {
        tradeAssetObj.value = {}
        coinAssetListObj.value['trade'] = tradeArray
      }
      tradeArr.value = tradeArray
      posMapObj.value = data.posmap
    }
  }
  const getPairDetail = async(type, pair) => {
    const { data } = await getPairSetting({
      all_spot: 1,
      all_cnt: 1
    })
    if (data) {
      pairInfo.value = data.map((v) => {
        v.price_scale = typeof Number(v.price_scale) === 'number' && !Number.isNaN(Number(v.price_scale)) ? Number(v.price_scale) : 2
        v.quantity_scale = typeof Number(v.quantity_scale) === 'number' && !Number.isNaN(Number(v.quantity_scale)) ? Number(v.quantity_scale) : 4
        return v
      }).reduce((acc, item) => {
        acc[item.symbol] = item
        return acc
      }, {})

      if (process.client) {
        localStorage.setItem('pairInfo', JSON.stringify(pairInfo.value))
      }
    }
  }

  // 设置价格精度（用户在盘口选择的小数位）
  const setPriceScale = (scale: number) => {
    if (typeof scale === 'number' && scale >= 0 && scale <= 10) {
      priceScale.value = scale
    }
  }

  // 设置数量精度
  const setQuantityScale = (scale: number) => {
    if (typeof scale === 'number' && scale >= 0 && scale <= 10) {
      quantityScale.value = scale
    }
  }
  const getDepthSocket = (pair: any) => {
    marketSocket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    const cb = (res) => {
      const convertToNumber = (value) => {
        return typeof value === 'string' ? parseFloat(value) : value;
      }
      if (res.t === 0) { // 全量
        const {
          asks,
          bids
        } = res.d
        const result = {
          pair: res.d.pair,
          asks,
          bids
        }
        depthsStore.value = { [pair]: result }
      } else {
        const {
          add,
          del
        } = res.d
        if (add.asks && depthsStore.value[pair]) {
          add.asks.forEach((v) => {
            depthsStore.value[pair].asks[v.price] = v
          })
        }
        if (add.bids && depthsStore.value[pair]) {
          add.bids.forEach((v) => {
            depthsStore.value[pair].bids[v.price] = v
          })
        }
        if (del.asks && depthsStore.value[pair]) {
          del.asks.forEach((v) => {
            if (Object.values(depthsStore.value[pair].asks).length > 0) {
              delete depthsStore.value[pair].asks[v.price]
            }
          })
        }
        if (del.bids && depthsStore.value[pair]) {
          del.bids.forEach((v) => {
            if (Object.values(depthsStore.value[pair].bids).length > 0) {
              delete depthsStore.value[pair].bids[v.price]
            }
          })
        }
      }
    }
    marketSocket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`, cb)
  }
  const transChartData = (obj) => {
    const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    const finalItem = {
      time: Number(obj[0])
    }
    obj.forEach((v, i) => {
      finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
    })
    return finalItem
  }
  // 基础版 1w/1M 首屏历史优先：就绪标记与增量缓存
  const basicKlineHistoryReady = new Map<string, boolean>()
  const basicKlineQueuedIncrements = new Map<string, any[]>()
  const getBasicKey = (pair: string, time: string) => `${pair}__${time}`
  const resetBasicKlineHistoryReady = (pair: string, time: string) => {
    const key = getBasicKey(pair, time)
    basicKlineHistoryReady.set(key, false)
    basicKlineQueuedIncrements.set(key, [])
  }
  const markBasicKlineHistoryReady = (pair: string, time: string) => {
    const key = getBasicKey(pair, time)
    basicKlineHistoryReady.set(key, true)
    const queued = basicKlineQueuedIncrements.get(key) || []
    if (!queued.length) return
    // 将排队的增量帧按时间顺序合并到 klineList
    const existingList = [...klineList.value]
    queued.forEach((latestCandle) => {
      const lastIndex = existingList.length - 1
      if (lastIndex >= 0) {
        const lastExisting = existingList[lastIndex]
        if (lastExisting.time === latestCandle.time || lastExisting.timestamp === latestCandle.timestamp) {
          existingList[lastIndex] = latestCandle
        } else if ((latestCandle.time ?? 0) > (lastExisting.time ?? 0) || (latestCandle.timestamp ?? 0) > (lastExisting.timestamp ?? 0)) {
          existingList.push(latestCandle)
          const maxKlineCount = 5000
          if (existingList.length > maxKlineCount) {
            existingList.splice(0, existingList.length - maxKlineCount)
          }
        }
      } else {
        existingList.push(latestCandle)
      }
    })
    klineList.value = existingList
    basicKlineQueuedIncrements.set(key, [])
  }

  const getKlineList = async(pair: any, time: any) => {
    const { data } = await getKlinesApi({
      symbol: pair,
      market: pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: time,
      limit: 2000
    })
    if (data) {
      klineList.value = data.e.map((item: any) => transChartData(item))
    }
  }
  const getKlineSocket = async(pair: any, time: any) => {
    return new Promise((resolve: Function, reject: Function) => {
      marketSocket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`]})
      const cb = (res) => {
        if (res.t === 0) {
          // 初始全量数据
          // 根因修复：基础版 1w/1M 一律忽略 WS 首帧（t=0）对 klineList 的覆盖，避免首屏由 WS 快照驱动
          const isWeekOrMonth = (time === '1w' || time === '1M')
          if (isWeekOrMonth) {
            if (res.d && res.d.length) {
              // 仅同步 ticker，保持最新价信息
              klineTicker.value = {
                ...res.d[res.d.length - 1],
                currentPair: res.stream.split('.')[1],
                currentPeriod: time,
                _lastUpdate: Date.now()
              }
            }
            resolve()
            return
          }

          // 非 1w/1M，仍按原逻辑接收首帧
          klineList.value = res.d
          if (res.d.length && time === '1M') {
            klineTicker.value = {
              ...res.d[res.d.length - 1],
              currentPair: res.stream.split('.')[1],
              currentPeriod: time,
            }
            resolve()
            return
          }
        } else if (res.t === 1 && res.d.length > 0) {
          // 增量更新：基础版 1w/1M 在历史未就绪前不直接落地，避免“先WS后历史”的闪屏
          const latestCandle = res.d[res.d.length - 1]
          const isWeekOrMonth = (time === '1w' || time === '1M')
          if (isWeekOrMonth) {
            const key = getBasicKey(pair, time)
            const ready = basicKlineHistoryReady.get(key)
            if (!ready) {
              // 仅缓存增量，更新ticker即可
              const queued = basicKlineQueuedIncrements.get(key) || []
              queued.push(latestCandle)
              basicKlineQueuedIncrements.set(key, queued)
              klineTicker.value = {
                ...latestCandle,
                currentPair: res.stream.split('.')[1],
                currentPeriod: time,
                _lastUpdate: Date.now()
              }
              return
            }
          }

          const existingList = [...klineList.value]
          const lastIndex = existingList.length - 1
          if (lastIndex >= 0) {
            const lastExisting = existingList[lastIndex]
            if (lastExisting.time === latestCandle.time || lastExisting.timestamp === latestCandle.timestamp) {
              existingList[lastIndex] = latestCandle
            } else if ((latestCandle.time ?? 0) > (lastExisting.time ?? 0) || (latestCandle.timestamp ?? 0) > (lastExisting.timestamp ?? 0)) {
              existingList.push(latestCandle)
              const maxKlineCount = 5000
              if (existingList.length > maxKlineCount) {
                existingList.splice(0, existingList.length - maxKlineCount)
              }
            }
          } else {
            existingList.push(latestCandle)
          }
          klineList.value = existingList
          klineTicker.value = {
            ...latestCandle,
            currentPair: res.stream.split('.')[1],
            currentPeriod: time,
            _lastUpdate: Date.now()
          }
        } else if (res.t === 0 && res.d.length) {
          // 全量数据也更新ticker
          klineTicker.value = {
            ...res.d[res.d.length - 1],
            currentPair: res.stream.split('.')[1],
            currentPeriod: time,
            _lastUpdate: Date.now() // 新增：添加更新时间戳
          }
        }
        resolve()
      }
      marketSocket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`, cb)
    })
  }
  const socketLogin = ref(null)
  const subLogin = () => {
    if (socketLogin.value && socketLogin.value.websocket) {
      socketLogin.value.destroy(); // 使用新增的销毁方法
      socketLogin.value = null;
    }
    socketLogin.value = new SocketClass(`wss://u-stream${getDomainF()}`)
    const session_id = cookies.get('session_id_origin') || cookies.get('session_id')
    socketLogin.value.send({"method":"LOGIN","auth":{"sid": session_id}})
    const cb = (res) => { // 资产变化
      setTimeout(() => {
        getAssetsByCoin()
      }, 500)
    }
    const cbO = (res) => { // 订单变化
      if (res && res.data && res.data.product.includes('_SWAP')) {
        isChangeFutureOrder.value = true
      } else {
        isChangeOrder.value = true
      }
    }
    const cb1 = (res) => { // 仓位变化
      isChangePosition.value = true
    }
    socketLogin.value.on('account', cb)
    socketLogin.value.on('order', cbO)
    socketLogin.value.on('position', cb1)
  }
  const reConnectUser = (pair) => {
    if (pair.includes('_SWAP')) {
      isChangePosition.value = true
      isChangeFutureOrder.value = true
    } else {
      isChangeOrder.value = true
    }
    getAssetsByCoin()
  }
  const subTradesSocket = (pair: any) => {
    marketSocket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
    const cb = (res) => {
      if (res.t === 0) {
        dealsObj.value[pair] = res.d
        if (Object.keys(res.d).length > 0) {
          const latestDeal = Object.values(res.d).sort((a, b) => b.t - a.t)[0]
          if (latestDeal) {
            const updateTimestamp = Date.now()

            // 直接创建或更新ticker数据，保持数据流的连续性
            const newTickerData = {
              ...(ticker.value[pair] || {}),
              last: latestDeal.p,
              product: pair,
              _lastUpdate: updateTimestamp,
              _source: 'trades'
            }

            // 优化：直接更新，移除try-catch延迟
            ticker.value[pair] = newTickerData

            // 价格优化器直接调用，提升速度
            priceOptimizer.queueUpdate(pair, newTickerData)
          }
        }
      } else {
        // 处理增量交易数据
        Object.values(res.d).forEach((item) => {
          dealsObj.value[pair][item.i] = item

          const currentPrice = ticker.value[pair] ? Number(ticker.value[pair].last) : 0
          const newPrice = Number(item.p)

          // 只在价格确实发生变化时才更新
          if (currentPrice !== newPrice) {
            const updateTimestamp = Date.now()

            const newTickerData = {
              ...(ticker.value[pair] || {}),
              last: item.p,
              product: pair,
              _lastUpdate: updateTimestamp,
              _source: 'trades'
            }

            // 优化：直接更新，移除try-catch延迟
            ticker.value[pair] = newTickerData

            // 价格优化器直接调用，提升速度
            priceOptimizer.queueUpdate(pair, newTickerData)
          }
        })
      }
    }
    marketSocket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`, cb)
  }
  const subTickerSocket = (pair: any) => {
    marketSocket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    const cb = (res) => {
      const product = res.data.product
      const newData = res.data
      const currentData = ticker.value[product]

      // 简化的数据变化检测，避免复杂的条件判断
      const hasDataChange = !currentData ||
          currentData.last !== newData.last ||
          currentData.change !== newData.change ||
          currentData.high !== newData.high ||
          currentData.low !== newData.low ||
          currentData.amount !== newData.amount ||
          currentData.volume !== newData.volume

      if (hasDataChange) {
        // 生成可靠的更新时间戳
        const updateTimestamp = Date.now()

        // 保留高优先级的实时成交数据（如果存在且时间较新）
        let finalData = { ...newData }

        if (currentData && currentData._source === 'trades' && currentData._lastUpdate) {
          const timeDiff = updateTimestamp - currentData._lastUpdate
          // 如果trades数据很新（5秒内）且价格不同，优先使用trades价格
          if (timeDiff < 5000 && Number(currentData.last) !== Number(newData.last)) {
            finalData.last = currentData.last
            finalData._lastUpdate = currentData._lastUpdate
            finalData._source = 'trades'
          } else {
            finalData._lastUpdate = updateTimestamp
            finalData._source = 'ticker'
          }
        } else {
          finalData._lastUpdate = updateTimestamp
          finalData._source = 'ticker'
        }

        // 优化：直接更新ticker数据，确保数据流畅通
        ticker.value[product] = finalData

        // 价格优化器直接调用，无需try-catch
        priceOptimizer.queueUpdate(product, finalData)
      }
    }
    marketSocket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`, cb)
  }
  const subAllTickerSocket = () => {
    tickerSocket.send({"method":"SUBSCRIBE","params":["ALL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        marketsObj.value[item.product] = item
      })
    }
    tickerSocket.on('ALL.ticker', cb)
  }
  const subCOLLATERALTickerSocket = () => {
    tickerSocket.send({"method":"SUBSCRIBE","params":["COLLATERAL.ticker"]})
    const cb = (res) => {
      res.data.forEach((item) => {
        COLLATERALSymbol.value[item.symbol] = item
      })
    }
    tickerSocket.on('COLLATERAL.ticker', cb)
  }
  const cancelCOLLATERALTickerSocket = () => {
    marketSocket.send({"method":"UNSUBSCRIBE","params":["COLLATERAL.ticker"]})
  }
  const cancelAllTicker = () => {
    marketSocket.send({"method":"UNSUBSCRIBE","params":["ALL.ticker"]})
  }
  const cancelKline = (pair: any, time: any) => {
    marketSocket.send({"method":"UNSUBSCRIBE", "params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.candles.${time}`]})
  }
  const cancelSocket = (pair: any) => {
    marketSocket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`]})
    marketSocket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.order_book.100`]})
    marketSocket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.ticker`]})
    marketSocket.send({"method":"UNSUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.trades`]})
  }
  return {
    assetAllCoinMap,
    allAsset,
    coinAssetListObj,
    CoinAssetObj,
    mainAssetObj,
    tradeAssetObj,
    posMapObj,
    errorMessages,
    currencyRate,
    exchangeRate,
    coinList,
    depthsStore,
    ticker,
    klineList,
    klineTicker,
    allPairList,
    dealsObj,
    isChangeFutureOrder,
    isChangeOrder,
    isChangePosition,
    orderChangeObj,
    isHideAssets,
    marketsObj,
    pairInfo,
    priceScale,
    quantityScale,
    isPairDetail,
    landingPairs,
    tradeArr,
    COLLATERALSymbol,
    downLoadInfo,
    pair,
    subCOLLATERALTickerSocket,
    cancelCOLLATERALTickerSocket,
    getLandingPairs,
    getPairDetail,
    setHideAssets,
    setPriceScale,
    setQuantityScale,
    switchExchangeRate,
    getCurrencyRate,
    getAssetByCoinList,
    getAllPairList,
    cancelKline,
    getKlineSocket,
    // 基础版 1w/1M 首屏历史优先：暴露历史就绪标记接口
    resetBasicKlineHistoryReady,
    markBasicKlineHistoryReady,
    getMessageError,
    getCoinList,
    getAssetsByCoin,
    getDepthSocket,
    subLogin,
    subAllTickerSocket,
    cancelAllTicker,
    subTradesSocket,
    subTickerSocket,
    cancelSocket,
    getDownLoadInfo,
    reConnectUser,
    changePair,
    priceOptimizer
  }
})