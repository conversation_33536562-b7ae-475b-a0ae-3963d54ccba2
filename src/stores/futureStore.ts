import { defineStore } from 'pinia'
import { getTicker, getApiCBUPositions, getDepthList, getCurrenOrderList } from '~/api/order'
import { getUserRateApi, getUserLeverageApi } from '~/api/user'
import { cookies } from '~/utils/cookies'
import { marketSocket } from '~/utils'
export const futureStore = defineStore('futureCommon', () => {
  const mode = ref({}) // 当前交易仓位模式 1 全仓 2 逐仓
  const leverage = ref({}) // 当前交易对杠杆倍数
  const ticker = ref({})
  const cbcTokenUnit = ref('') // 币本位交易单位
  const cbuTokenUnit = ref('') // U本位交易单位
  const cbuPositions = ref([])
  const lpcCurrentList = ref([])
  const lpcCbuPositions = ref([])
  const futureInfo = ref({})
  const userRate = ref({})
  const positionMode = ref({})
  const getPositionMode = async () => {
    const { data } = await getUserLeverageApi();
    if (!data) return;  // 如果 data 为空，直接返回
    positionMode.value = data;  // 存储原始数据（可选）
    // 初始化 mode 和 leverage 为对象
    mode.value = {};
    leverage.value = {};
    // 遍历 data 的每一个交易对
    const modeInfo = {};
    Object.entries(data).forEach(([pair, item]) => {
      const modeTxt = item.current.split('_')[0]
      const currentMode = modeTxt === 'cross' ? 1 : 2 // 1: cross, 2: isolate
      const currentLeverage = item[modeTxt].long
      mode.value[pair] =  currentMode  
      leverage.value[pair] = currentLeverage
      // 构建 modeInfo 对象（用于 Cookie）
      modeInfo[pair] = {
        mode: currentMode,
        leverage: currentLeverage
      }
    })
    cookies.set('modeInfo', modeInfo)
  }
  const getUserRate = async() => {
    const { data } = await getUserRateApi()
    if (data) {
      data.forEach((item) => {
        userRate.value[item.product] = item
      })
    }
  }
  const getLpcCurrentList = async() => {
    const { data } = await getCurrenOrderList({
      status: 'unsettled',
      market: 'lpc'
    })
    if (data) {
      lpcCurrentList.value = data.reverse()
    }
  }
  const setModeInfoState = (pair, modeVal, leverageVal) => {
    // 1. 读取当前 modeInfo（如果没有则初始化空对象）
    const currentModeInfo = cookies.get('modeInfo') || {}
    // 2. 更新指定 pair 的数据
    currentModeInfo[pair] = {
        mode: modeVal,
        leverage: leverageVal
    };
    mode.value[pair] = currentModeInfo[pair].mode
    leverage.value[pair] = currentModeInfo[pair].leverage
    // 3. 保存回 Cookie
    cookies.set('modeInfo', currentModeInfo)
  }
  const setCbcTokenUnit = (val) => {
    cbcTokenUnit.value = val
  }
  const setCbuTokenUnit = (val) => {
    cbuTokenUnit.value = val
  }
  const subTicker = async(pair, market) => {
    const { data } = await getTicker({
      market,
      symbol: pair
    })
    if (data) {
      ticker.value = data[0]
    }
  }
  const subDepth = async(market, symbol, level) => {
    const { data } = await getDepthList({
      market,
      symbol,
      level
    })
    if (data) {
      console.info(data, 'dhduehuehudue')
    }
  }
  const subFutureInfoSocket = (pair: any) => {
    marketSocket.send({"method":"SUBSCRIBE","params":[`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`]})
    const cb = (res) => {
      futureInfo.value = res.d
    }
    marketSocket.on(`${pair.includes('_SWAP') ? 'lpc.' : 'spot.'}${pair}.info`, cb)
  }
  const getCBUPositions = async(pair, market) => {
    const { data } = await getApiCBUPositions({
      symbol: pair,
      market 
    })
    if (data) {
      cbuPositions.value = data.filter((item) => {
        return item.quantity * 1 !== 0
      })
    }
  }
  const getLpcCBUPositions = async() => {
    const { data } = await getApiCBUPositions({
      market: 'lpc'
    })
    if (data) {
      lpcCbuPositions.value = data.filter((item) => {
        return item.quantity * 1 !== 0
      })
    }
  }
  return {
    mode,
    leverage,
    ticker,
    cbcTokenUnit,
    cbuTokenUnit,
    cbuPositions,
    lpcCurrentList,
    lpcCbuPositions,
    userRate,
    futureInfo,
    positionMode,
    getPositionMode,
    subFutureInfoSocket,
    getUserRate,
    getLpcCBUPositions,
    getLpcCurrentList,
    setCbcTokenUnit,
    setCbuTokenUnit,
    setModeInfoState,
    subTicker,
    subDepth,
    getCBUPositions
  }
})