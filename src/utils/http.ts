import axios, { type AxiosRequestConfig, type AxiosRequestHeaders, type AxiosResponse } from "axios"
import { cookies, setStorage, generateId, AESEncrypt, AESDecrypt, encryptPrivateKey, addWaterMark, getFileExt } from '~/utils'
import { port, BASE_EXCHANGE_URL, PROD_EXCHANGE_URL, PROD_ORDER_EXCHANGE_URL } from "~/config"
import apiPortsConfig from '~/config/api.port.config'
import qs from 'qs'
interface AdaptAxiosRequestConfig extends AxiosRequestConfig {
  headers: AxiosRequestHeaders
}
const isDev = import.meta.env.VITE_ENV === 'localment'
const isTest = import.meta.env.VITE_ENV === 'development'
// Set config defaults when creating the instance
const instance1 = axios.create({
  timeout: 20000,
  withCredentials: true
})
instance1.interceptors.request.use((config: AxiosRequestConfig): AdaptAxiosRequestConfig => {
  const result: AdaptAxiosRequestConfig = config as AdaptAxiosRequestConfig
  result.headers['content-type'] = 'application/json'
  result.headers['accept'] = 'application/json, text/plain'
  return result as AdaptAxiosRequestConfig
}, error => {
  Promise.reject(error)
})
instance1.interceptors.response.use((res: AxiosResponse<any>): any => {
  if (res && res.data && res.data.result && res.data.result[0] && res.data.result[0].result) {
    res = res.data.result[0].result
  } else if (res && res.data && JSON.stringify(res.data.result) === '{}' && res.data.state && res.data.msg) {
    res = {
      ...res.data.result,
      error: {
        code: res.data.state,
        msg: res.data.msg
      }
    }
  } else if (res && res.data && res.data.result) {
    res = res.data.result
  } else if (res && res.data && !res.data.error) {
    res = res.data
  }
  if ((res && res.data && res.data.error) || (res && res.state && res.msg)) {
    const error = {
      data: null,
      error: res.state ? {
        code: res.state,
        ...res
      } : res.data.error
    }
    if (process.client && error.error.code * 1 === 2013) {
      cookies.remove('isLogin')
      cookies.remove('session_id')
      cookies.remove('session_id_origin')
      cookies.remove('futureslpcNotOrderConfirmAgain')
      cookies.remove('showMessage')
    } else if (error.error.code * 1 === 2011) {
      cookies.remove('isLogin')
      cookies.remove('session_id')
      cookies.remove('session_id_origin')
      cookies.remove('futureslpcNotOrderConfirmAgain')
      cookies.remove('showMessage')
    }
    return error
  } else {
    return {
      data: res,
      error: null
    }
  }
  return {
    data: res,
    error: null
  }
})

const instance = axios.create({
  timeout: 20000,
  withCredentials: true
})
instance.interceptors.request.use((config: AxiosRequestConfig): AdaptAxiosRequestConfig => {
  const result: AdaptAxiosRequestConfig = config as AdaptAxiosRequestConfig
  // result.headers['content-type'] = 'application/json'
  // result.headers['accept'] = 'application/json, text/plain'
  return result as AdaptAxiosRequestConfig
}, error => {
  Promise.reject(error)
})

instance.interceptors.response.use((res: AxiosResponse<any>): any => {
  if (res && res.data && res.data.result && res.data.result[0] && res.data.result[0].result) {
    res = res.data.result[0].result
  } else if (res && res.data && JSON.stringify(res.data.result) === '{}' && res.data.state && res.data.msg) {
    res = {
      ...res.data.result,
      error: {
        code: res.data.state,
        msg: res.data.msg
      }
    }
  } else if (res && res.data && res.data.result) {
    res = res.data.result
  } else if (res && res.data && !res.data.error) {
    res = res.data
  }
  if ((res && res.data && res.data.error) || (res && res.state && (res.msg || res.msg === ''))) {
    const error = {
      data: null,
      error: res.state ? {
        code: res.state,
        ...res
      } : res.data.error
    }
    if (process.client && error.error.code * 1 === 2013) {
      cookies.remove('isLogin')
    } else if (error.error.code * 1 === 2011) {
      cookies.remove('isLogin')
    }
    return error
  } else {
    return {
      data: res,
      error: null
    }
  }
  return {
    data: res,
    error: null
  }
})
const getDomain = () => {
  if (!process.client) {
    return '.ktx.one'
  }
  const hostname = location.hostname
  const host = hostname.split(':')[0].split('.')
  return host.length === 2 ? ['', host[0], host[1]].join('.') : ['', host[1], host[2]].join('.')
}
const PROD_EXCHANGE_URL = `${getDomain().includes('tonetou') ? 'https://ma-tapi' : 'https://api'}${getDomain()}`
console.log(PROD_EXCHANGE_URL, 'ddheidjeijdiejdijeidjeije')
function addOriginParam(params: any): any {
  return {
    ...params,
    origin: 1
  }
}
export function get(url: string, params?: any): any {
  return instance.get(`${isDev ? '' : (isTest ? BASE_EXCHANGE_URL : PROD_EXCHANGE_URL)}${isTest ? `:${apiPortsConfig[url]}` : ''}${url}`, {
    params: addOriginParam(params || {})
  })
}

export function post(url: string, params: any = {}): any {
  const paramsEnd = qs.stringify(addOriginParam(params))
  // ${isDev ? `:${apiPortsConfig[url]}` : ''}
  return instance.post(`${isDev ? '' : (isTest ? BASE_EXCHANGE_URL : PROD_EXCHANGE_URL)}${isTest ? `:${apiPortsConfig[url]}` : ''}${url}`, paramsEnd)
}

export function Delete(url: string, params: any = {}): any {
  // ${isDev ? `:${apiPortsConfig[url]}` : ''}
  return instance.delete(`${isDev ? '' : (isTest ? BASE_EXCHANGE_URL : PROD_EXCHANGE_URL)}${isTest ? `:${apiPortsConfig[url]}` : ''}${url}`, {
    data: addOriginParam(params)
  })
}
export function orderDelete(url: string, params: any = {}): any {
  // ${isDev ? `:${apiPortsConfig[url]}` : ''}
  return instance.delete(`${isDev ? '' : (isTest ? BASE_EXCHANGE_URL : PROD_ORDER_EXCHANGE_URL)}${isTest ? `:${apiPortsConfig[url]}` : ''}${url}`, {
    data: addOriginParam(params)
  })
}
export function orderPost(url: string, params: any = {}): any {
  const paramsEnd = addOriginParam(params)
  // ${isDev ? `:${apiPortsConfig[url]}` : ''}
  return instance1.post(`${isDev ? '' : (isTest ? BASE_EXCHANGE_URL : PROD_ORDER_EXCHANGE_URL)}${isTest ? `:${apiPortsConfig[url]}` : ''}${url}`, paramsEnd)
}

export function encrypt(url: string, params: any): any {
  console.info(url, 'psppspspspsp')
  const pkey = cookies.get('pkey')
  if (!pkey) {
    console.error('request  error ,unknow public key')
    return Promise.resolve({ code: 500, msg: '请求失败' })
  } else {
    params = {
      data: AESEncrypt(JSON.stringify(addOriginParam(params))),
      key: encryptPrivateKey()
    }
  }
  return post(url, params)
}

export async function decrypt(url: string, params?: any): Promise<any> {
  const response = await post(`${isDev ? `:${apiPortsConfig[url]}` : ''}${url}`, addOriginParam(params))
  return {
    ...response,
    data: AESDecrypt(response.data)
  }
}
